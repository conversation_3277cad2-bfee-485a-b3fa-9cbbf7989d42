//---------------------------------------------------------------------------
// Sword3 Engine - Modernized Sorted List System
// 现代化的排序列表系统 - 使用std::map和现代C++特性
//---------------------------------------------------------------------------
#ifndef KSortList_Modern_H
#define KSortList_Modern_H

#include "KWin32.h"
#include <map>
#include <vector>
#include <algorithm>
#include <functional>

//---------------------------------------------------------------------------
// 现代化的排序列表项
//---------------------------------------------------------------------------
template<typename T>
struct SortItem {
    DWORD hashCode;     // 哈希码，用于排序和查找
    T data;             // 实际数据
    
    SortItem() : hashCode(0) {}
    SortItem(DWORD hash, const T& item) : hashCode(hash), data(item) {}
    
    // 比较操作符
    bool operator<(const SortItem& other) const {
        return hashCode < other.hashCode;
    }
    
    bool operator==(const SortItem& other) const {
        return hashCode == other.hashCode;
    }
};

//---------------------------------------------------------------------------
// 现代化的排序列表类
//---------------------------------------------------------------------------
template<typename T>
#ifndef __linux
class ENGINE_API KSortList
#else
class KSortList
#endif
{
private:
    std::map<DWORD, T> m_sortedMap;     // 使用map自动排序
    std::vector<SortItem<T>> m_items;   // 备用向量存储
    bool m_useMap;                      // 是否使用map模式
    
public:
    // 构造函数和析构函数
    KSortList(bool useMapMode = true);
    ~KSortList();
    
    // 兼容性接口
    BOOL Init(int nItemSize, int nItemCount);
    BOOL Insert(DWORD dwHashCode, const T& item);
    BOOL Search(DWORD dwHashCode, T& result) const;
    void Free();
    
    // 现代化接口
    bool Insert(DWORD hashCode, const T& item);
    bool Find(DWORD hashCode, T& result) const;
    bool Contains(DWORD hashCode) const;
    bool Remove(DWORD hashCode);
    void Clear();
    
    // 访问操作
    T* Get(DWORD hashCode);
    const T* Get(DWORD hashCode) const;
    T& operator[](DWORD hashCode);
    const T& operator[](DWORD hashCode) const;
    
    // 容器操作
    size_t Size() const;
    bool Empty() const;
    void Reserve(size_t capacity);
    
    // 遍历操作
    std::vector<DWORD> GetHashCodes() const;
    std::vector<T> GetValues() const;
    std::vector<SortItem<T>> GetAllItems() const;
    
    // 范围查询
    std::vector<SortItem<T>> GetRange(DWORD minHash, DWORD maxHash) const;
    std::vector<SortItem<T>> GetFirst(size_t count) const;
    std::vector<SortItem<T>> GetLast(size_t count) const;
    
    // 排序和查找
    void SortByHash(bool ascending = true);
    void SortByValue(std::function<bool(const T&, const T&)> comparator);
    
    // 过滤操作
    std::vector<SortItem<T>> Filter(std::function<bool(const SortItem<T>&)> predicate) const;
    std::vector<SortItem<T>> FilterByHashRange(DWORD minHash, DWORD maxHash) const;
    
    // 聚合操作
    size_t CountIf(std::function<bool(const SortItem<T>&)> predicate) const;
    SortItem<T>* FindIf(std::function<bool(const SortItem<T>&)> predicate);
    
    // 转换操作
    template<typename U>
    std::vector<U> Transform(std::function<U(const SortItem<T>&)> transformer) const;
    
    // 统计操作
    DWORD GetMinHash() const;
    DWORD GetMaxHash() const;
    double GetAverageHash() const;
    
    // 调试和输出
    void DebugPrint() const;
    std::string ToString() const;
    
    // 迭代器支持（简化版本）
    class Iterator {
    private:
        typename std::map<DWORD, T>::iterator m_mapIt;
        typename std::vector<SortItem<T>>::iterator m_vecIt;
        bool m_isMap;
        
    public:
        Iterator(typename std::map<DWORD, T>::iterator mapIt) 
            : m_mapIt(mapIt), m_isMap(true) {}
        Iterator(typename std::vector<SortItem<T>>::iterator vecIt) 
            : m_vecIt(vecIt), m_isMap(false) {}
        
        SortItem<T> operator*() const {
            if (m_isMap) {
                return SortItem<T>(m_mapIt->first, m_mapIt->second);
            } else {
                return *m_vecIt;
            }
        }
        
        Iterator& operator++() {
            if (m_isMap) {
                ++m_mapIt;
            } else {
                ++m_vecIt;
            }
            return *this;
        }
        
        bool operator!=(const Iterator& other) const {
            if (m_isMap != other.m_isMap) return true;
            if (m_isMap) {
                return m_mapIt != other.m_mapIt;
            } else {
                return m_vecIt != other.m_vecIt;
            }
        }
    };
    
    Iterator Begin();
    Iterator End();
    
private:
    // 内部辅助函数
    void SyncMapToVector();
    void SyncVectorToMap();
    DWORD GenerateHash(const T& item) const;
};

//---------------------------------------------------------------------------
// 特化版本：指针类型的排序列表
//---------------------------------------------------------------------------
template<typename T>
class KSortPtrList : public KSortList<T*> {
public:
    KSortPtrList() : KSortList<T*>(true) {}
    
    // 安全的指针操作
    bool InsertPtr(DWORD hashCode, T* ptr) {
        if (!ptr) return false;
        return KSortList<T*>::Insert(hashCode, ptr);
    }
    
    T* FindPtr(DWORD hashCode) const {
        T* result = nullptr;
        if (KSortList<T*>::Find(hashCode, result)) {
            return result;
        }
        return nullptr;
    }
    
    bool RemovePtr(DWORD hashCode) {
        return KSortList<T*>::Remove(hashCode);
    }
    
    // 清理时不删除指针指向的对象
    void ClearPtrs() {
        KSortList<T*>::Clear();
    }
    
    // 清理时删除指针指向的对象
    void ClearAndDelete() {
        auto values = KSortList<T*>::GetValues();
        for (T* ptr : values) {
            delete ptr;
        }
        KSortList<T*>::Clear();
    }
};

//---------------------------------------------------------------------------
// 常用类型的typedef
//---------------------------------------------------------------------------
typedef KSortList<int> KIntSortList;
typedef KSortList<float> KFloatSortList;
typedef KSortList<std::string> KStringSortList;
typedef KSortList<DWORD> KDwordSortList;

typedef KSortPtrList<void> KVoidPtrSortList;
typedef KSortPtrList<char> KCharPtrSortList;

//---------------------------------------------------------------------------
// 工厂函数
//---------------------------------------------------------------------------
template<typename T>
KSortList<T>* CreateSortList(bool useMapMode = true) {
    return new KSortList<T>(useMapMode);
}

template<typename T>
KSortPtrList<T>* CreateSortPtrList() {
    return new KSortPtrList<T>();
}

//---------------------------------------------------------------------------
#endif // KSortList_Modern_H
