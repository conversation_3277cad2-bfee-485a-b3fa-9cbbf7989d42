#ifndef WINDOWS_COMPATIBILITY_H
#define WINDOWS_COMPATIBILITY_H

// Windows SDK兼容性头文件
// 解决新版Windows SDK与老代码的兼容性问题

// 强制使用Windows 2000兼容性
#ifndef _WIN32_WINNT
#define _WIN32_WINNT 0x0500
#endif

#ifndef WINVER
#define WINVER 0x0500
#endif

// 禁用一些新的Windows特性以避免兼容性问题
#define WIN32_LEAN_AND_MEAN
#define NOMINMAX
#define _CRT_SECURE_NO_WARNINGS
#define _WINSOCK_DEPRECATED_NO_WARNINGS

// 强制禁用新版SDK的64位指针定义
#define _NO_W32_PSEUDO_MODIFIERS

// 在包含Windows头文件之前，先定义一些宏来避免冲突
#define PVOID64 void*
#define POINTER_64

// 包含基本的Windows类型定义，但跳过有问题的部分
#include <windef.h>
#include <winbase.h>

// 手动定义我们需要的类型，避免包含winnt.h
typedef void* PVOID64;

// 如果需要其他Windows功能，可以在这里添加特定的头文件
// 但要避免包含winnt.h，因为它包含有问题的PVOID64定义

#endif // WINDOWS_COMPATIBILITY_H
