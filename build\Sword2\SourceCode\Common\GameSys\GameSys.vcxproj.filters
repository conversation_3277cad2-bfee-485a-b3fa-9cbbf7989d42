﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\GameSys\NameAddressedData\KDataBaseDef.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\GameSys\NameAddressedData\KG_RelayDatabase.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\GameSys\NameAddressedData\KRoleAddtionalData.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\GameSys\NameAddressedData\KDataBaseDef.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\GameSys\NameAddressedData\KG_CommandQueue.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\GameSys\NameAddressedData\KG_RelayDatabase.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\GameSys\NameAddressedData\KGlobal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\GameSys\NameAddressedData\KMemInfoCache.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\GameSys\NameAddressedData\KRoleAddtionalData.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\Sword2Code\Sword2\SourceCode\Common\GameSys\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{0B3A53E7-52A2-3721-8EFE-549413474C42}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{9B634824-E985-3319-BF78-ED480F445829}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
