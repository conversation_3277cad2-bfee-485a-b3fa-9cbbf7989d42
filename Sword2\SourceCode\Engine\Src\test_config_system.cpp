// Configuration system modernization test - verify KIniFile_modern
#include "KWin32.h"

// Forward declarations to avoid conflicts
void g_DebugLog(const char* fmt, ...);

#include "KIniFile_modern.h"
#include <stdio.h>
#include <stdarg.h>
#include <string>
#include <vector>

// Simple debug implementation for testing
void g_DebugLog(const char* fmt, ...) {
    char buffer[1024];
    va_list va;
    
    va_start(va, fmt);
    vsnprintf(buffer, sizeof(buffer), fmt, va);
    va_end(va);
    
    printf("[DEBUG] %s\n", buffer);
}

// Test configuration system functionality
ENGINE_API int TestConfigSystem() {
    g_DebugLog("Starting configuration system modernization test...");
    
    // Test 1: Basic INI file operations
    g_DebugLog("=== Test 1: Basic INI File Operations ===");
    
    KIniFile config;
    
    // Test writing different types
    config.SetString("General", "AppName", "Sword3 Engine");
    config.SetInt("General", "Version", 100);
    config.SetFloat("General", "Scale", 1.5f);
    config.SetBool("General", "Debug", true);
    
    config.SetString("Graphics", "Resolution", "1024x768");
    config.SetInt("Graphics", "ColorDepth", 32);
    config.SetBool("Graphics", "Fullscreen", false);
    
    // Test reading back
    std::string appName = config.GetString("General", "AppName", "");
    if (appName != "Sword3 Engine") {
        g_DebugLog("ERROR: String read failed. Got: '%s'", appName.c_str());
        return -1;
    }
    g_DebugLog("String read passed: '%s'", appName.c_str());
    
    int version = config.GetInt("General", "Version", 0);
    if (version != 100) {
        g_DebugLog("ERROR: Int read failed. Got: %d", version);
        return -2;
    }
    g_DebugLog("Int read passed: %d", version);
    
    float scale = config.GetFloat("General", "Scale", 0.0f);
    if (scale < 1.4f || scale > 1.6f) {
        g_DebugLog("ERROR: Float read failed. Got: %f", scale);
        return -3;
    }
    g_DebugLog("Float read passed: %f", scale);
    
    bool debug = config.GetBool("General", "Debug", false);
    if (!debug) {
        g_DebugLog("ERROR: Bool read failed");
        return -4;
    }
    g_DebugLog("Bool read passed: %s", debug ? "true" : "false");
    
    // Test 2: Section and key operations
    g_DebugLog("=== Test 2: Section and Key Operations ===");
    
    if (!config.HasSection("General")) {
        g_DebugLog("ERROR: HasSection failed for existing section");
        return -5;
    }
    
    if (config.HasSection("NonExistent")) {
        g_DebugLog("ERROR: HasSection failed for non-existing section");
        return -6;
    }
    
    if (!config.HasKey("General", "AppName")) {
        g_DebugLog("ERROR: HasKey failed for existing key");
        return -7;
    }
    
    if (config.HasKey("General", "NonExistent")) {
        g_DebugLog("ERROR: HasKey failed for non-existing key");
        return -8;
    }
    
    std::vector<std::string> sections = config.GetSectionNames();
    if (sections.size() != 2) {
        g_DebugLog("ERROR: Expected 2 sections, got %d", (int)sections.size());
        return -9;
    }
    g_DebugLog("Section count passed: %d sections", (int)sections.size());
    
    std::vector<std::string> keys = config.GetKeyNames("General");
    if (keys.size() != 4) {
        g_DebugLog("ERROR: Expected 4 keys in General section, got %d", (int)keys.size());
        return -10;
    }
    g_DebugLog("Key count passed: %d keys in General section", (int)keys.size());
    
    // Test 3: Array operations
    g_DebugLog("=== Test 3: Array Operations ===");
    
    std::vector<int> intArray = {10, 20, 30, 40};
    config.SetIntArray("Arrays", "Numbers", intArray, ',');
    
    std::vector<int> readIntArray = config.GetIntArray("Arrays", "Numbers", ',');
    if (readIntArray.size() != 4 || readIntArray[0] != 10 || readIntArray[3] != 40) {
        g_DebugLog("ERROR: Int array read/write failed");
        return -11;
    }
    g_DebugLog("Int array passed: %d elements", (int)readIntArray.size());
    
    std::vector<float> floatArray = {1.1f, 2.2f, 3.3f};
    config.SetFloatArray("Arrays", "Floats", floatArray, ',');
    
    std::vector<float> readFloatArray = config.GetFloatArray("Arrays", "Floats", ',');
    if (readFloatArray.size() != 3 || readFloatArray[0] < 1.0f || readFloatArray[0] > 1.2f) {
        g_DebugLog("ERROR: Float array read/write failed");
        return -12;
    }
    g_DebugLog("Float array passed: %d elements", (int)readFloatArray.size());
    
    std::vector<std::string> stringArray = {"apple", "banana", "cherry"};
    config.SetStringArray("Arrays", "Fruits", stringArray, ',');
    
    std::vector<std::string> readStringArray = config.GetStringArray("Arrays", "Fruits", ',');
    if (readStringArray.size() != 3 || readStringArray[0] != "apple") {
        g_DebugLog("ERROR: String array read/write failed");
        return -13;
    }
    g_DebugLog("String array passed: %d elements", (int)readStringArray.size());
    
    // Test 4: File I/O
    g_DebugLog("=== Test 4: File I/O ===");
    
    if (!config.SaveToFile("test_config.ini")) {
        g_DebugLog("ERROR: Failed to save config file");
        return -14;
    }
    g_DebugLog("Config file saved successfully");
    
    KIniFile config2;
    if (!config2.LoadFromFile("test_config.ini")) {
        g_DebugLog("ERROR: Failed to load config file");
        return -15;
    }
    g_DebugLog("Config file loaded successfully");
    
    // Verify loaded data
    std::string loadedAppName = config2.GetString("General", "AppName", "");
    if (loadedAppName != "Sword3 Engine") {
        g_DebugLog("ERROR: Loaded string mismatch. Got: '%s'", loadedAppName.c_str());
        return -16;
    }
    
    int loadedVersion = config2.GetInt("General", "Version", 0);
    if (loadedVersion != 100) {
        g_DebugLog("ERROR: Loaded int mismatch. Got: %d", loadedVersion);
        return -17;
    }
    
    g_DebugLog("File I/O verification passed");
    
    // Test 5: Template interface
    g_DebugLog("=== Test 5: Template Interface ===");
    
    config.Set<std::string>("Template", "StringValue", "Hello Template");
    config.Set<int>("Template", "IntValue", 42);
    config.Set<float>("Template", "FloatValue", 3.14f);
    config.Set<bool>("Template", "BoolValue", true);
    
    std::string templateString = config.Get<std::string>("Template", "StringValue", "default");
    if (templateString != "Hello Template") {
        g_DebugLog("ERROR: Template string failed. Got: '%s'", templateString.c_str());
        return -18;
    }
    
    int templateInt = config.Get<int>("Template", "IntValue", 0);
    if (templateInt != 42) {
        g_DebugLog("ERROR: Template int failed. Got: %d", templateInt);
        return -19;
    }
    
    float templateFloat = config.Get<float>("Template", "FloatValue", 0.0f);
    if (templateFloat < 3.13f || templateFloat > 3.15f) {
        g_DebugLog("ERROR: Template float failed. Got: %f", templateFloat);
        return -20;
    }
    
    bool templateBool = config.Get<bool>("Template", "BoolValue", false);
    if (!templateBool) {
        g_DebugLog("ERROR: Template bool failed");
        return -21;
    }
    
    g_DebugLog("Template interface passed");
    
    // Test 6: Legacy compatibility interface
    g_DebugLog("=== Test 6: Legacy Compatibility Interface ===");
    
    config.WriteString("Legacy", "TestString", "Legacy Value");
    config.WriteInteger("Legacy", "TestInt", 999);
    config.WriteFloat("Legacy", "TestFloat", 2.718f);
    config.WriteBool("Legacy", "TestBool", TRUE);
    
    char buffer[256];
    if (!config.GetString("Legacy", "TestString", "", buffer, sizeof(buffer))) {
        g_DebugLog("ERROR: Legacy GetString failed");
        return -22;
    }
    
    if (strcmp(buffer, "Legacy Value") != 0) {
        g_DebugLog("ERROR: Legacy string mismatch. Got: '%s'", buffer);
        return -23;
    }
    
    int legacyInt;
    if (!config.GetInteger("Legacy", "TestInt", 0, &legacyInt)) {
        g_DebugLog("ERROR: Legacy GetInteger failed");
        return -24;
    }
    
    if (legacyInt != 999) {
        g_DebugLog("ERROR: Legacy int mismatch. Got: %d", legacyInt);
        return -25;
    }
    
    float legacyFloat;
    if (!config.GetFloat("Legacy", "TestFloat", 0.0f, &legacyFloat)) {
        g_DebugLog("ERROR: Legacy GetFloat failed");
        return -26;
    }
    
    if (legacyFloat < 2.7f || legacyFloat > 2.8f) {
        g_DebugLog("ERROR: Legacy float mismatch. Got: %f", legacyFloat);
        return -27;
    }
    
    BOOL legacyBool;
    config.GetBool("Legacy", "TestBool", &legacyBool);
    if (!legacyBool) {
        g_DebugLog("ERROR: Legacy bool failed");
        return -28;
    }
    
    g_DebugLog("Legacy compatibility interface passed");
    
    g_DebugLog("=== All configuration system tests passed! ===");
    return 0;
}

// Test main function
#ifdef _STANDALONE
int main() {
    g_DebugLog("Starting configuration system modernization test suite...");
    
    int result = TestConfigSystem();
    
    if (result == 0) {
        printf("SUCCESS: All configuration system tests passed!\n");
    } else {
        printf("FAILED: Configuration system test failed with code %d\n", result);
    }
    
    return result;
}
#endif
