// Minimal test file - verify modernized KWin32.h compilation
#include "KWin32.h"

// Simple test function
ENGINE_API int TestFunction() {
    DWORD test = 42;
    BOOL success = TRUE;
    RECT rect = {0, 0, 100, 100};
    POINT point = {50, 50};

    return success ? (int)test : 0;
}

// Test main function
#ifdef _STANDALONE
int main() {
    int result = TestFunction();
    printf("Test result: %d\n", result);
    return 0;
}
#endif
