// Core modules test - verify <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, KMemBase compilation
#include "KWin32.h"
#include "KDebug.h"
#include "KNode.h"
#include "KList.h"
#include "KMemBase.h"

// Test node class
class TestNode : public KNode {
public:
    int value;
    TestNode(int v) : value(v) {}
};

// Test core functionality
ENGINE_API int TestCoreModules() {
    // Test memory allocation
    void* mem = g_MemAlloc(1024);
    if (!mem) {
        g_DebugLog("Memory allocation failed");
        return -1;
    }
    
    // Test memory operations
    g_MemZero(mem, 1024);
    g_MemFree(mem);
    
    // Test list operations
    KList testList;
    TestNode node1(1);
    TestNode node2(2);
    TestNode node3(3);
    
    // Add nodes to list
    testList.AddHead(&node1);
    testList.AddTail(&node2);
    testList.AddTail(&node3);
    
    // Verify list operations
    if (testList.IsEmpty()) {
        g_DebugL<PERSON>("List should not be empty");
        return -2;
    }
    
    LONG count = testList.GetNodeCount();
    if (count != 3) {
        g_DebugLog("Expected 3 nodes, got %d", count);
        return -3;
    }
    
    // Test node traversal
    TestNode* current = (TestNode*)testList.GetHead();
    int expectedValues[] = {1, 2, 3};
    int index = 0;
    
    while (current && index < 3) {
        if (current->value != expectedValues[index]) {
            g_DebugLog("Node value mismatch at index %d: expected %d, got %d", 
                      index, expectedValues[index], current->value);
            return -4;
        }
        current = (TestNode*)current->GetNext();
        index++;
    }
    
    g_DebugLog("All core module tests passed!");
    return 0;
}

// Test main function
#ifdef _STANDALONE
int main() {
    g_DebugLog("Starting core modules test...");
    
    int result = TestCoreModules();
    
    if (result == 0) {
        printf("SUCCESS: All core module tests passed!\n");
    } else {
        printf("FAILED: Core module test failed with code %d\n", result);
    }
    
    return result;
}
#endif
