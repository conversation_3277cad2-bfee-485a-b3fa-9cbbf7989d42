d:\sword2code\build\sword2\sourcecode\engine\coretest.dir\debug\kmembase.obj
d:\sword2code\build\sword2\sourcecode\engine\coretest.dir\debug\klist.obj
d:\sword2code\build\sword2\sourcecode\engine\coretest.dir\debug\knode.obj
d:\sword2code\build\sword2\sourcecode\engine\coretest.dir\debug\kdebug_minimal.obj
d:\sword2code\build\sword2\sourcecode\engine\coretest.dir\debug\test_core.obj
d:\sword2code\build\sword2\sourcecode\engine\cmakefiles\generate.stamp
d:\sword2code\build\bin\debug\coretest.exe
d:\sword2code\build\bin\debug\coretest.pdb
d:\sword2code\build\sword2\sourcecode\engine\coretest.dir\debug\coretest.ilk
d:\sword2code\build\sword2\sourcecode\engine\coretest.dir\debug\coretest.tlog\cl.command.1.tlog
d:\sword2code\build\sword2\sourcecode\engine\coretest.dir\debug\coretest.tlog\cl.items.tlog
d:\sword2code\build\sword2\sourcecode\engine\coretest.dir\debug\coretest.tlog\cl.read.1.tlog
d:\sword2code\build\sword2\sourcecode\engine\coretest.dir\debug\coretest.tlog\cl.write.1.tlog
d:\sword2code\build\sword2\sourcecode\engine\coretest.dir\debug\coretest.tlog\custombuild.command.1.tlog
d:\sword2code\build\sword2\sourcecode\engine\coretest.dir\debug\coretest.tlog\custombuild.read.1.tlog
d:\sword2code\build\sword2\sourcecode\engine\coretest.dir\debug\coretest.tlog\custombuild.write.1.tlog
d:\sword2code\build\sword2\sourcecode\engine\coretest.dir\debug\coretest.tlog\link.command.1.tlog
d:\sword2code\build\sword2\sourcecode\engine\coretest.dir\debug\coretest.tlog\link.read.1.tlog
d:\sword2code\build\sword2\sourcecode\engine\coretest.dir\debug\coretest.tlog\link.secondary.1.tlog
d:\sword2code\build\sword2\sourcecode\engine\coretest.dir\debug\coretest.tlog\link.write.1.tlog
