// Minimal file system test - completely standalone
#include <fstream>
#include <iostream>
#include <string>

// Simple types to avoid Windows dependencies
typedef int BOOL;
typedef unsigned long DWORD;
typedef void* LPVOID;
typedef char* LPSTR;
typedef long LONG;

#define TRUE 1
#define FALSE 0
#define FILE_BEGIN 0
#define FILE_CURRENT 1
#define FILE_END 2
#define SEEK_ERROR 0xFFFFFFFF

// Simple modern file class
class SimpleFile {
private:
    std::fstream m_file;
    DWORD m_size;
    DWORD m_pos;
    std::string m_path;
    
public:
    SimpleFile() : m_size(0), m_pos(0) {}
    
    ~SimpleFile() {
        Close();
    }
    
    BOOL Open(LPSTR fileName) {
        if (!fileName) return FALSE;
        
        Close();
        m_path = std::string(fileName);
        
        m_file.open(m_path, std::ios::in | std::ios::binary);
        if (!m_file.is_open()) {
            std::cout << "Failed to open file: " << fileName << std::endl;
            return FALSE;
        }
        
        UpdateSize();
        m_pos = 0;
        std::cout << "Opened file: " << fileName << " (size: " << m_size << " bytes)" << std::endl;
        return TRUE;
    }
    
    BOOL Create(LPSTR fileName) {
        if (!fileName) return FALSE;
        
        Close();
        m_path = std::string(fileName);
        
        m_file.open(m_path, std::ios::out | std::ios::binary | std::ios::trunc);
        if (!m_file.is_open()) {
            std::cout << "Failed to create file: " << fileName << std::endl;
            return FALSE;
        }
        
        m_size = 0;
        m_pos = 0;
        std::cout << "Created file: " << fileName << std::endl;
        return TRUE;
    }
    
    void Close() {
        if (m_file.is_open()) {
            m_file.close();
        }
        m_size = 0;
        m_pos = 0;
        m_path.clear();
    }
    
    DWORD Read(LPVOID buffer, DWORD bytes) {
        if (!m_file.is_open() || !buffer || bytes == 0) {
            return 0;
        }
        
        m_file.read(static_cast<char*>(buffer), bytes);
        DWORD bytesRead = static_cast<DWORD>(m_file.gcount());
        m_pos += bytesRead;
        return bytesRead;
    }
    
    DWORD Write(LPVOID buffer, DWORD bytes) {
        if (!m_file.is_open() || !buffer || bytes == 0) {
            return 0;
        }
        
        m_file.write(static_cast<const char*>(buffer), bytes);
        if (m_file.good()) {
            m_pos += bytes;
            if (m_pos > m_size) {
                m_size = m_pos;
            }
            return bytes;
        }
        return 0;
    }
    
    DWORD Seek(LONG distance, DWORD method) {
        if (!m_file.is_open()) {
            return SEEK_ERROR;
        }
        
        std::ios::seekdir dir;
        switch (method) {
            case FILE_BEGIN: dir = std::ios::beg; break;
            case FILE_CURRENT: dir = std::ios::cur; break;
            case FILE_END: dir = std::ios::end; break;
            default: return SEEK_ERROR;
        }
        
        m_file.seekg(distance, dir);
        m_file.seekp(distance, dir);
        
        if (m_file.good()) {
            m_pos = static_cast<DWORD>(m_file.tellg());
            return m_pos;
        }
        return SEEK_ERROR;
    }
    
    DWORD Tell() { return m_pos; }
    DWORD Size() { return m_size; }
    
private:
    void UpdateSize() {
        if (!m_file.is_open()) {
            m_size = 0;
            return;
        }
        
        std::streampos current = m_file.tellg();
        m_file.seekg(0, std::ios::end);
        m_size = static_cast<DWORD>(m_file.tellg());
        m_file.seekg(current);
    }
};

// Test function
int TestSimpleFile() {
    std::cout << "Starting simple file system test..." << std::endl;
    
    SimpleFile file;
    
    // Test 1: Create and write
    std::cout << "=== Test 1: Create and Write ===" << std::endl;
    if (!file.Create("test_simple.txt")) {
        std::cout << "ERROR: Failed to create file" << std::endl;
        return -1;
    }
    
    const char* testData = "Hello, Simple File System!\nThis is a test.\n";
    DWORD bytesWritten = file.Write((LPVOID)testData, strlen(testData));
    if (bytesWritten != strlen(testData)) {
        std::cout << "ERROR: Write failed" << std::endl;
        return -2;
    }
    std::cout << "Successfully wrote " << bytesWritten << " bytes" << std::endl;
    file.Close();
    
    // Test 2: Open and read
    std::cout << "=== Test 2: Open and Read ===" << std::endl;
    if (!file.Open("test_simple.txt")) {
        std::cout << "ERROR: Failed to open file" << std::endl;
        return -3;
    }
    
    DWORD fileSize = file.Size();
    std::cout << "File size: " << fileSize << " bytes" << std::endl;
    
    char readBuffer[256];
    memset(readBuffer, 0, sizeof(readBuffer));
    DWORD bytesRead = file.Read(readBuffer, fileSize);
    
    if (bytesRead != fileSize) {
        std::cout << "ERROR: Read failed" << std::endl;
        return -4;
    }
    
    if (strncmp(readBuffer, testData, fileSize) != 0) {
        std::cout << "ERROR: Data verification failed" << std::endl;
        return -5;
    }
    
    std::cout << "Successfully read and verified " << bytesRead << " bytes" << std::endl;
    file.Close();
    
    std::cout << "=== All tests passed! ===" << std::endl;
    return 0;
}

int main() {
    int result = TestSimpleFile();
    
    if (result == 0) {
        std::cout << "SUCCESS: All file system tests passed!" << std::endl;
    } else {
        std::cout << "FAILED: Test failed with code " << result << std::endl;
    }
    
    return result;
}
