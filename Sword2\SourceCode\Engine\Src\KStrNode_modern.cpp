//---------------------------------------------------------------------------
// Sword3 Engine - Modernized String Node Implementation
// 现代化的字符串节点实现
//---------------------------------------------------------------------------
#include "KStrNode_modern.h"
#include <iostream>

// Forward declaration for debug function
extern void g_DebugLog(LPSTR Fmt, ...);

//---------------------------------------------------------------------------
// 构造函数
//---------------------------------------------------------------------------
KStrNode::KStrNode() {
    // 默认构造函数，m_name已经是空字符串
}

KStrNode::KStrNode(const char* name) {
    if (name) {
        m_name = name;
    }
}

KStrNode::KStrNode(const std::string& name) : m_name(name) {
}

KStrNode::KStrNode(const KStrNode& other) : KNode(other), m_name(other.m_name) {
}

KStrNode::KStrNode(KStrNode&& other) noexcept : KNode(std::move(other)), m_name(std::move(other.m_name)) {
}

//---------------------------------------------------------------------------
// 赋值操作符
//---------------------------------------------------------------------------
KStrNode& KStrNode::operator=(const KStrNode& other) {
    if (this != &other) {
        KNode::operator=(other);
        m_name = other.m_name;
    }
    return *this;
}

KStrNode& KStrNode::operator=(KStrNode&& other) noexcept {
    if (this != &other) {
        KNode::operator=(std::move(other));
        m_name = std::move(other.m_name);
    }
    return *this;
}

KStrNode& KStrNode::operator=(const char* name) {
    if (name) {
        m_name = name;
    } else {
        m_name.clear();
    }
    return *this;
}

KStrNode& KStrNode::operator=(const std::string& name) {
    m_name = name;
    return *this;
}

//---------------------------------------------------------------------------
// 兼容性接口实现
//---------------------------------------------------------------------------
void KStrNode::SetName(char* str) {
    if (str) {
        m_name = str;
    } else {
        m_name.clear();
    }
}

void KStrNode::SetName(const char* str) {
    if (str) {
        m_name = str;
    } else {
        m_name.clear();
    }
}

//---------------------------------------------------------------------------
// 比较操作实现
//---------------------------------------------------------------------------
bool KStrNode::EqualsIgnoreCase(const char* str) const {
    if (!str) return m_name.empty();
    
    std::string lowerName = ModernString::ToLower(m_name);
    std::string lowerStr = ModernString::ToLower(std::string(str));
    
    return lowerName == lowerStr;
}

bool KStrNode::EqualsIgnoreCase(const std::string& str) const {
    std::string lowerName = ModernString::ToLower(m_name);
    std::string lowerStr = ModernString::ToLower(str);
    
    return lowerName == lowerStr;
}

//---------------------------------------------------------------------------
// 调试和输出
//---------------------------------------------------------------------------
void KStrNode::DebugPrint() const {
    g_DebugLog("KStrNode: name='%s', length=%d", m_name.c_str(), (int)m_name.length());
}

//---------------------------------------------------------------------------
