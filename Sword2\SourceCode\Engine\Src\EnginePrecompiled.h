#ifndef ENGINE_PRECOMPILED_H
#define ENGINE_PRECOMPILED_H

// 引擎预编译头文件 - 全局Windows SDK兼容性解决方案
// 这个文件必须在所有其他头文件之前包含

// 强制使用Windows XP兼容的API版本（更好的兼容性）
#ifdef _WIN32_WINNT
#undef _WIN32_WINNT
#endif
#define _WIN32_WINNT 0x0501  // Windows XP

#ifdef WINVER
#undef WINVER
#endif
#define WINVER 0x0501

#ifdef NTDDI_VERSION
#undef NTDDI_VERSION
#endif
#define NTDDI_VERSION 0x05010000

// 强制定义兼容性宏
#ifdef WIN32_LEAN_AND_MEAN
#undef WIN32_LEAN_AND_MEAN
#endif
#define WIN32_LEAN_AND_MEAN

#ifdef NOMINMAX
#undef NOMINMAX
#endif
#define NOMINMAX

#ifdef _CRT_SECURE_NO_WARNINGS
#undef _CRT_SECURE_NO_WARNINGS
#endif
#define _CRT_SECURE_NO_WARNINGS

#ifdef _WINSOCK_DEPRECATED_NO_WARNINGS
#undef _WINSOCK_DEPRECATED_NO_WARNINGS
#endif
#define _WINSOCK_DEPRECATED_NO_WARNINGS

// 在包含任何Windows头文件之前强制定义PVOID64
// 这是解决新版Windows SDK兼容性问题的关键
#ifdef PVOID64
#undef PVOID64
#endif
typedef void* PVOID64;

// 按正确顺序包含Windows头文件
#include <winsock2.h>
#include <windows.h>
#include <ws2tcpip.h>

// 重新定义一些常用的宏，避免与STL冲突
#ifndef max
#define max(a,b) (((a) > (b)) ? (a) : (b))
#endif

#ifndef min
#define min(a,b) (((a) < (b)) ? (a) : (b))
#endif

// 解决stricmp等POSIX函数名称问题
#ifndef stricmp
#define stricmp _stricmp
#endif

#ifndef strnicmp
#define strnicmp _strnicmp
#endif

#ifndef strcmpi
#define strcmpi _strcmpi
#endif

// 包含标准C++头文件
#include <algorithm>
#include <string>
#include <cstring>
#include <cstdio>
#include <cstdlib>

#endif // ENGINE_PRECOMPILED_H
