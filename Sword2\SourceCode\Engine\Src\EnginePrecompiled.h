#ifndef ENGINE_PRECOMPILED_H
#define ENGINE_PRECOMPILED_H

// 引擎预编译头文件
// 解决Windows SDK兼容性问题

// 强制使用较老的Windows API版本
#ifndef _WIN32_WINNT
#define _WIN32_WINNT 0x0601  // Windows 7
#endif

#ifndef WINVER
#define WINVER 0x0601
#endif

#ifndef NTDDI_VERSION
#define NTDDI_VERSION 0x06010000
#endif

// 定义宏来解决兼容性问题（只定义未定义的）
#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif

#ifndef NOMINMAX
#define NOMINMAX
#endif

#ifndef _CRT_SECURE_NO_WARNINGS
#define _CRT_SECURE_NO_WARNINGS
#endif

#ifndef _WINSOCK_DEPRECATED_NO_WARNINGS
#define _WINSOCK_DEPRECATED_NO_WARNINGS
#endif

// 解决PVOID64类型定义问题
#ifndef PVOID64
typedef void* PVOID64;
#endif

// 包含Windows头文件
#include <winsock2.h>
#include <windows.h>
#include <ws2tcpip.h>

// 重新定义一些常用的宏，避免与STL冲突
#ifndef max
#define max(a,b) (((a) > (b)) ? (a) : (b))
#endif

#ifndef min
#define min(a,b) (((a) < (b)) ? (a) : (b))
#endif

// 解决stricmp等POSIX函数名称问题
#ifndef stricmp
#define stricmp _stricmp
#endif

#ifndef strnicmp
#define strnicmp _strnicmp
#endif

#ifndef strcmpi
#define strcmpi _strcmpi
#endif

// 包含标准C++头文件
#include <algorithm>
#include <string>
#include <cstring>
#include <cstdio>
#include <cstdlib>

#endif // ENGINE_PRECOMPILED_H
