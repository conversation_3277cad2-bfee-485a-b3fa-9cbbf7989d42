//---------------------------------------------------------------------------
// Sword3 Engine - Modernized String System Implementation
// 现代化的字符串系统实现
//---------------------------------------------------------------------------
#include "KStrBase_modern.h"
#include <cstring>
#include <sstream>
#include <iomanip>
#include <stdarg.h>
#include <vector>

// Forward declaration for debug function
extern void g_DebugLog(LPSTR Fmt, ...);

//---------------------------------------------------------------------------
// 传统C风格接口实现（保持兼容性）
//---------------------------------------------------------------------------

ENGINE_API int g_StrLen(LPCSTR lpStr) {
    return lpStr ? static_cast<int>(strlen(lpStr)) : 0;
}

ENGINE_API LPSTR g_StrEnd(LPCSTR lpStr) {
    if (!lpStr) return nullptr;
    return const_cast<LPSTR>(lpStr + strlen(lpStr));
}

ENGINE_API void g_StrCpy(LPSTR lpDest, LPCSTR lpSrc) {
    if (lpDest && lpSrc) {
        strcpy(lpDest, lpSrc);
    }
}

ENGINE_API void g_StrCpyLen(LPSTR lpDest, LPCSTR lpSrc, int nMaxLen) {
    if (lpDest && lpSrc && nMaxLen > 0) {
        strncpy(lpDest, lpSrc, nMaxLen - 1);
        lpDest[nMaxLen - 1] = '\0';
    }
}

ENGINE_API void g_StrCat(LPSTR lpDest, LPCSTR lpSrc) {
    if (lpDest && lpSrc) {
        strcat(lpDest, lpSrc);
    }
}

ENGINE_API void g_StrCatLen(LPSTR lpDest, LPCSTR lpSrc, int nMaxLen) {
    if (lpDest && lpSrc && nMaxLen > 0) {
        strncat(lpDest, lpSrc, nMaxLen - strlen(lpDest) - 1);
    }
}

ENGINE_API BOOL g_StrCmp(LPCSTR lpDest, LPCSTR lpSrc) {
    if (!lpDest || !lpSrc) return FALSE;
    return strcmp(lpDest, lpSrc) == 0 ? TRUE : FALSE;
}

ENGINE_API BOOL g_StrCmpLen(LPCSTR lpDest, LPCSTR lpSrc, int nMaxLen) {
    if (!lpDest || !lpSrc) return FALSE;
    return strncmp(lpDest, lpSrc, nMaxLen) == 0 ? TRUE : FALSE;
}

ENGINE_API void g_StrUpper(LPSTR lpDest) {
    if (!lpDest) return;
    
    char* ptr = lpDest;
    while (*ptr) {
        if (*ptr >= 'a' && *ptr <= 'z') {
            *ptr += 'A' - 'a';
        }
        ptr++;
    }
}

ENGINE_API void g_StrLower(LPSTR lpDest) {
    if (!lpDest) return;
    
    char* ptr = lpDest;
    while (*ptr) {
        if (*ptr >= 'A' && *ptr <= 'Z') {
            *ptr += 'a' - 'A';
        }
        ptr++;
    }
}

ENGINE_API void g_StrRep(LPSTR lpDest, LPSTR lpSrc, LPSTR lpRep) {
    if (!lpDest || !lpSrc || !lpRep) return;

    // 使用现代化的字符串替换
    std::string destStr(lpDest);
    std::string srcStr(lpSrc);
    std::string repStr(lpRep);

    std::string result = ModernString::Replace(destStr, srcStr, repStr);

    // 安全复制回原始缓冲区
    size_t destLen = strlen(lpDest) + 1;
    strncpy(lpDest, result.c_str(), destLen - 1);
    lpDest[destLen - 1] = '\0';
}

//---------------------------------------------------------------------------
// 现代化扩展接口实现
//---------------------------------------------------------------------------

namespace ModernString {
    
    std::string ToUpper(const std::string& str) {
        std::string result = str;
        std::transform(result.begin(), result.end(), result.begin(), 
                      [](unsigned char c) { return std::toupper(c); });
        return result;
    }
    
    std::string ToLower(const std::string& str) {
        std::string result = str;
        std::transform(result.begin(), result.end(), result.begin(), 
                      [](unsigned char c) { return std::tolower(c); });
        return result;
    }
    
    std::string Trim(const std::string& str) {
        return TrimLeft(TrimRight(str));
    }
    
    std::string TrimLeft(const std::string& str) {
        auto start = std::find_if(str.begin(), str.end(), 
                                 [](unsigned char c) { return !std::isspace(c); });
        return std::string(start, str.end());
    }
    
    std::string TrimRight(const std::string& str) {
        auto end = std::find_if(str.rbegin(), str.rend(), 
                               [](unsigned char c) { return !std::isspace(c); }).base();
        return std::string(str.begin(), end);
    }
    
    std::string Replace(const std::string& str, const std::string& from, const std::string& to) {
        if (from.empty()) return str;
        
        std::string result = str;
        size_t pos = result.find(from);
        if (pos != std::string::npos) {
            result.replace(pos, from.length(), to);
        }
        return result;
    }
    
    std::string ReplaceAll(const std::string& str, const std::string& from, const std::string& to) {
        if (from.empty()) return str;
        
        std::string result = str;
        size_t pos = 0;
        while ((pos = result.find(from, pos)) != std::string::npos) {
            result.replace(pos, from.length(), to);
            pos += to.length();
        }
        return result;
    }
    
    bool Contains(const std::string& str, const std::string& substr) {
        return str.find(substr) != std::string::npos;
    }
    
    bool StartsWith(const std::string& str, const std::string& prefix) {
        return str.length() >= prefix.length() && 
               str.compare(0, prefix.length(), prefix) == 0;
    }
    
    bool EndsWith(const std::string& str, const std::string& suffix) {
        return str.length() >= suffix.length() && 
               str.compare(str.length() - suffix.length(), suffix.length(), suffix) == 0;
    }
    
    std::vector<std::string> Split(const std::string& str, char delimiter) {
        std::vector<std::string> result;
        std::stringstream ss(str);
        std::string item;
        
        while (std::getline(ss, item, delimiter)) {
            result.push_back(item);
        }
        
        return result;
    }
    
    std::vector<std::string> Split(const std::string& str, const std::string& delimiter) {
        std::vector<std::string> result;
        if (delimiter.empty()) {
            result.push_back(str);
            return result;
        }
        
        size_t start = 0;
        size_t pos = 0;
        
        while ((pos = str.find(delimiter, start)) != std::string::npos) {
            result.push_back(str.substr(start, pos - start));
            start = pos + delimiter.length();
        }
        
        result.push_back(str.substr(start));
        return result;
    }
    
    std::string Join(const std::vector<std::string>& strings, const std::string& delimiter) {
        if (strings.empty()) return "";
        
        std::ostringstream oss;
        for (size_t i = 0; i < strings.size(); ++i) {
            if (i > 0) oss << delimiter;
            oss << strings[i];
        }
        
        return oss.str();
    }
    
    int ToInt(const std::string& str, int defaultValue) {
        try {
            return std::stoi(Trim(str));
        } catch (...) {
            return defaultValue;
        }
    }
    
    float ToFloat(const std::string& str, float defaultValue) {
        try {
            return std::stof(Trim(str));
        } catch (...) {
            return defaultValue;
        }
    }
    
    bool ToBool(const std::string& str, bool defaultValue) {
        std::string trimmed = ToLower(Trim(str));
        if (trimmed == "true" || trimmed == "1" || trimmed == "yes" || trimmed == "on") {
            return true;
        } else if (trimmed == "false" || trimmed == "0" || trimmed == "no" || trimmed == "off") {
            return false;
        }
        return defaultValue;
    }
    
    std::string ToString(int value) {
        return std::to_string(value);
    }
    
    std::string ToString(float value) {
        return std::to_string(value);
    }
    
    std::string ToString(bool value) {
        return value ? "true" : "false";
    }
    
    std::string Format(const char* format, ...) {
        va_list args;
        va_start(args, format);

        // 简化实现：使用固定大小缓冲区
        char buffer[1024];
        vsnprintf(buffer, sizeof(buffer), format, args);
        va_end(args);

        return std::string(buffer);
    }
    
    void SafeCopy(char* dest, size_t destSize, const std::string& src) {
        if (!dest || destSize == 0) return;
        
        size_t copySize = std::min(src.length(), destSize - 1);
        memcpy(dest, src.c_str(), copySize);
        dest[copySize] = '\0';
    }
    
    void SafeCat(char* dest, size_t destSize, const std::string& src) {
        if (!dest || destSize == 0) return;
        
        size_t currentLen = strlen(dest);
        if (currentLen >= destSize - 1) return;
        
        size_t remainingSize = destSize - currentLen - 1;
        size_t copySize = std::min(src.length(), remainingSize);
        
        memcpy(dest + currentLen, src.c_str(), copySize);
        dest[currentLen + copySize] = '\0';
    }
    
    // 编码转换占位符实现（将来可以扩展）
    std::string UTF8ToANSI(const std::string& utf8Str) {
        // 简化实现：直接返回原字符串
        // 在实际项目中，这里应该实现真正的编码转换
        return utf8Str;
    }
    
    std::string ANSIToUTF8(const std::string& ansiStr) {
        // 简化实现：直接返回原字符串
        // 在实际项目中，这里应该实现真正的编码转换
        return ansiStr;
    }
}

//---------------------------------------------------------------------------
