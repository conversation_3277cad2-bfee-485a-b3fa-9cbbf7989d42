﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{A35C0092-ACAA-3B78-BBBE-8CFC7A5A149A}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>Support</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\Sword2Code\build\lib\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Support.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Support</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\Sword2Code\build\lib\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Support.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Support</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\Sword2Code\build\lib\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Support.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Support</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\Sword2Code\build\lib\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Support.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Support</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.lib</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager;D:\Sword2Code\Sword2\SourceCode\Common\Support\DynamicLoginPwd;D:\Sword2Code\Sword2\SourceCode\Common\Support\LocalStringLoader;D:\Sword2Code\Sword2\SourceCode\Common\Support\LogDatabase;D:\Sword2Code\Sword2\SourceCode\Common\Support\NameFilter;D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure;D:\Sword2Code\Sword2\SourceCode\Common\Support\SaveLoad;D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter;D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_LIB;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_LIB;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager;D:\Sword2Code\Sword2\SourceCode\Common\Support\DynamicLoginPwd;D:\Sword2Code\Sword2\SourceCode\Common\Support\LocalStringLoader;D:\Sword2Code\Sword2\SourceCode\Common\Support\LogDatabase;D:\Sword2Code\Sword2\SourceCode\Common\Support\NameFilter;D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure;D:\Sword2Code\Sword2\SourceCode\Common\Support\SaveLoad;D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter;D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager;D:\Sword2Code\Sword2\SourceCode\Common\Support\DynamicLoginPwd;D:\Sword2Code\Sword2\SourceCode\Common\Support\LocalStringLoader;D:\Sword2Code\Sword2\SourceCode\Common\Support\LogDatabase;D:\Sword2Code\Sword2\SourceCode\Common\Support\NameFilter;D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure;D:\Sword2Code\Sword2\SourceCode\Common\Support\SaveLoad;D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter;D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager;D:\Sword2Code\Sword2\SourceCode\Common\Support\DynamicLoginPwd;D:\Sword2Code\Sword2\SourceCode\Common\Support\LocalStringLoader;D:\Sword2Code\Sword2\SourceCode\Common\Support\LogDatabase;D:\Sword2Code\Sword2\SourceCode\Common\Support\NameFilter;D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure;D:\Sword2Code\Sword2\SourceCode\Common\Support\SaveLoad;D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter;D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_LIB;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_LIB;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager;D:\Sword2Code\Sword2\SourceCode\Common\Support\DynamicLoginPwd;D:\Sword2Code\Sword2\SourceCode\Common\Support\LocalStringLoader;D:\Sword2Code\Sword2\SourceCode\Common\Support\LogDatabase;D:\Sword2Code\Sword2\SourceCode\Common\Support\NameFilter;D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure;D:\Sword2Code\Sword2\SourceCode\Common\Support\SaveLoad;D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter;D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager;D:\Sword2Code\Sword2\SourceCode\Common\Support\DynamicLoginPwd;D:\Sword2Code\Sword2\SourceCode\Common\Support\LocalStringLoader;D:\Sword2Code\Sword2\SourceCode\Common\Support\LogDatabase;D:\Sword2Code\Sword2\SourceCode\Common\Support\NameFilter;D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure;D:\Sword2Code\Sword2\SourceCode\Common\Support\SaveLoad;D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter;D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager;D:\Sword2Code\Sword2\SourceCode\Common\Support\DynamicLoginPwd;D:\Sword2Code\Sword2\SourceCode\Common\Support\LocalStringLoader;D:\Sword2Code\Sword2\SourceCode\Common\Support\LogDatabase;D:\Sword2Code\Sword2\SourceCode\Common\Support\NameFilter;D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure;D:\Sword2Code\Sword2\SourceCode\Common\Support\SaveLoad;D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter;D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_LIB;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_LIB;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager;D:\Sword2Code\Sword2\SourceCode\Common\Support\DynamicLoginPwd;D:\Sword2Code\Sword2\SourceCode\Common\Support\LocalStringLoader;D:\Sword2Code\Sword2\SourceCode\Common\Support\LogDatabase;D:\Sword2Code\Sword2\SourceCode\Common\Support\NameFilter;D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure;D:\Sword2Code\Sword2\SourceCode\Common\Support\SaveLoad;D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter;D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager;D:\Sword2Code\Sword2\SourceCode\Common\Support\DynamicLoginPwd;D:\Sword2Code\Sword2\SourceCode\Common\Support\LocalStringLoader;D:\Sword2Code\Sword2\SourceCode\Common\Support\LogDatabase;D:\Sword2Code\Sword2\SourceCode\Common\Support\NameFilter;D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure;D:\Sword2Code\Sword2\SourceCode\Common\Support\SaveLoad;D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter;D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager;D:\Sword2Code\Sword2\SourceCode\Common\Support\DynamicLoginPwd;D:\Sword2Code\Sword2\SourceCode\Common\Support\LocalStringLoader;D:\Sword2Code\Sword2\SourceCode\Common\Support\LogDatabase;D:\Sword2Code\Sword2\SourceCode\Common\Support\NameFilter;D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure;D:\Sword2Code\Sword2\SourceCode\Common\Support\SaveLoad;D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter;D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_LIB;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_LIB;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager;D:\Sword2Code\Sword2\SourceCode\Common\Support\DynamicLoginPwd;D:\Sword2Code\Sword2\SourceCode\Common\Support\LocalStringLoader;D:\Sword2Code\Sword2\SourceCode\Common\Support\LogDatabase;D:\Sword2Code\Sword2\SourceCode\Common\Support\NameFilter;D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure;D:\Sword2Code\Sword2\SourceCode\Common\Support\SaveLoad;D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter;D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager;D:\Sword2Code\Sword2\SourceCode\Common\Support\DynamicLoginPwd;D:\Sword2Code\Sword2\SourceCode\Common\Support\LocalStringLoader;D:\Sword2Code\Sword2\SourceCode\Common\Support\LogDatabase;D:\Sword2Code\Sword2\SourceCode\Common\Support\NameFilter;D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure;D:\Sword2Code\Sword2\SourceCode\Common\Support\SaveLoad;D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter;D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/Sword2Code/Sword2/SourceCode/Common/Support/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/Sword2Code -BD:/Sword2Code/build --check-stamp-file D:/Sword2Code/build/Sword2/SourceCode/Common/Support/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\Sword2Code\build\Sword2\SourceCode\Common\Support\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/Sword2Code/Sword2/SourceCode/Common/Support/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/Sword2Code -BD:/Sword2Code/build --check-stamp-file D:/Sword2Code/build/Sword2/SourceCode/Common/Support/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\Sword2Code\build\Sword2\SourceCode\Common\Support\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule D:/Sword2Code/Sword2/SourceCode/Common/Support/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/Sword2Code -BD:/Sword2Code/build --check-stamp-file D:/Sword2Code/build/Sword2/SourceCode/Common/Support/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\Sword2Code\build\Sword2\SourceCode\Common\Support\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule D:/Sword2Code/Sword2/SourceCode/Common/Support/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/Sword2Code -BD:/Sword2Code/build --check-stamp-file D:/Sword2Code/build/Sword2/SourceCode/Common/Support/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\Sword2Code\build\Sword2\SourceCode\Common\Support\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager\KAreaCamp.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager\KAreaCampMgr.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager\KAreaCampTongSort.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager\KCampMsgProcessor.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\DynamicLoginPwd\DllCall_linux.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\DynamicLoginPwd\DynamicPwdLoginDll.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\DynamicLoginPwd\DynamicPwdLoginImplement.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\LocalStringLoader\LocalStringLoader.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\LogDatabase\LogDB_Entry.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\LogDatabase\LogDataBase.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\NameFilter\KBlackFilter.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\NameFilter\KFilter.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\NameFilter\KWhiteFilter.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KMailAttachmentImp.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KMailImp.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KPlayerTreasure.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KPostOfficeImp.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KPostOffice_Entry.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\SaveLoad\SaveLoadAux.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongCenter.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongDefine.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongDuty.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongMagicAttribute.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongManager.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongMember.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongProcessor.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongSync.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongTeam.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongTechControl.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongTechRes.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor\AssistFunction.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor\ItemAttribute.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor\ItemModeTransformer.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor\ItemSkill.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor\ItemTemplate.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor\StringTable.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor\VirtualItem.cpp" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager\KAreaCampTongSort.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\DynamicLoginPwd\DynamicPwdLoginImplement.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\DynamicLoginPwd\debugnew.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\LogDatabase\LogDataBase.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\LogDatabase\PreCompileFile.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\NameFilter\KBlackFilter.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\NameFilter\KFilter.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\NameFilter\KWhiteFilter.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KMailAttachmentImp.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KMailImp.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KPlayerTreasure.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KPostOfficeImp.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KTreasureQueue.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\SaveLoad\SaveLoadAux.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongCenter.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongDuty.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongMagicAttribute.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongManager.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongMember.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongSync.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongTeam.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongTechControl.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongTechRes.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor\AssistFunction.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor\ItemAttribute.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor\ItemModeTransformer.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor\ItemSkill.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor\ItemTemplate.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor\StringTable.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor\enum.h" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\Sword2Code\build\ZERO_CHECK.vcxproj">
      <Project>{50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\Sword2Code\build\Sword2\SourceCode\Common\Core\Core.vcxproj">
      <Project>{DCBB2576-63F8-3282-8324-6B83E2291047}</Project>
      <Name>Core</Name>
    </ProjectReference>
    <ProjectReference Include="D:\Sword2Code\build\Sword2\SourceCode\Engine\Engine.vcxproj">
      <Project>{F5730F5E-5A8C-36D0-B965-53150D6418B9}</Project>
      <Name>Engine</Name>
    </ProjectReference>
    <ProjectReference Include="D:\Sword2Code\build\DevEnv\Lua5\Lua5.vcxproj">
      <Project>{86782B81-090C-311F-9847-D8085B2DEC07}</Project>
      <Name>Lua5</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>