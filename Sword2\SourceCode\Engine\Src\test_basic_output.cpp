// Basic output test - verify console output works
#include "KWin32.h"
#include <stdio.h>

// Test basic output functionality
ENGINE_API int TestBasicOutput() {
    printf("Hello from TestBasicOutput!\n");
    printf("Testing printf functionality...\n");
    
    // Test formatted output
    int number = 42;
    float decimal = 3.14f;
    const char* text = "World";
    
    printf("Number: %d\n", number);
    printf("Decimal: %.2f\n", decimal);
    printf("Text: %s\n", text);
    
    // Test return value
    printf("Returning success code 0\n");
    return 0;
}

// Test main function
#ifdef _STANDALONE
int main() {
    printf("Starting basic output test...\n");
    
    int result = TestBasicOutput();
    
    printf("Test result: %d\n", result);
    
    if (result == 0) {
        printf("SUCCESS: Basic output test passed!\n");
    } else {
        printf("FAILED: Basic output test failed!\n");
    }
    
    return result;
}
#endif
