﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager\KAreaCamp.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager\KAreaCampMgr.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager\KAreaCampTongSort.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager\KCampMsgProcessor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\DynamicLoginPwd\DllCall_linux.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\DynamicLoginPwd\DynamicPwdLoginDll.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\DynamicLoginPwd\DynamicPwdLoginImplement.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\LocalStringLoader\LocalStringLoader.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\LogDatabase\LogDB_Entry.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\LogDatabase\LogDataBase.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\NameFilter\KBlackFilter.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\NameFilter\KFilter.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\NameFilter\KWhiteFilter.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KMailAttachmentImp.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KMailImp.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KPlayerTreasure.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KPostOfficeImp.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KPostOffice_Entry.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\SaveLoad\SaveLoadAux.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongCenter.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongDefine.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongDuty.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongMagicAttribute.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongMember.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongProcessor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongSync.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongTeam.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongTechControl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongTechRes.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor\AssistFunction.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor\ItemAttribute.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor\ItemModeTransformer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor\ItemSkill.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor\ItemTemplate.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor\StringTable.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor\VirtualItem.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager\KAreaCampTongSort.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\DynamicLoginPwd\DynamicPwdLoginImplement.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\DynamicLoginPwd\debugnew.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\LogDatabase\LogDataBase.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\LogDatabase\PreCompileFile.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\NameFilter\KBlackFilter.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\NameFilter\KFilter.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\NameFilter\KWhiteFilter.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KMailAttachmentImp.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KMailImp.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KPlayerTreasure.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KPostOfficeImp.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KTreasureQueue.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\SaveLoad\SaveLoadAux.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongCenter.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongDuty.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongMagicAttribute.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongMember.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongSync.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongTeam.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongTechControl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongTechRes.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor\AssistFunction.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor\ItemAttribute.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor\ItemModeTransformer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor\ItemSkill.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor\ItemTemplate.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor\StringTable.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor\enum.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{5BBBD2BF-96B6-3EE1-8EBA-AFDC2DC00F3A}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{44AB8148-4A3D-32F7-9B57-640603BD4364}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
