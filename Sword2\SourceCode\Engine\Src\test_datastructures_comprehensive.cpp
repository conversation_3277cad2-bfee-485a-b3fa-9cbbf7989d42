// Comprehensive data structures test - verify all modernized data structures
#include "KWin32.h"
#include <map>
#include <vector>
#include <string>
#include <list>
#include <stack>
#include <queue>
#include <stdio.h>
#include <stdarg.h>

// Simple debug implementation for testing
void g_DebugLog(const char* fmt, ...) {
    char buffer[1024];
    va_list va;
    
    va_start(va, fmt);
    vsnprintf(buffer, sizeof(buffer), fmt, va);
    va_end(va);
    
    printf("[DEBUG] %s\n", buffer);
}

// Test comprehensive data structures functionality
ENGINE_API int TestComprehensiveDataStructures() {
    g_DebugLog("Starting comprehensive data structures test...");
    
    // Test 1: Modern List (std::vector)
    g_DebugLog("=== Test 1: Modern List (std::vector) ===");
    
    std::vector<int> modernList;
    modernList.push_back(10);
    modernList.push_back(20);
    modernList.push_back(30);
    modernList.push_back(40);
    
    if (modernList.size() != 4) {
        g_DebugLog("ERROR: Modern list size failed");
        return -1;
    }
    
    if (modernList[0] != 10 || modernList[3] != 40) {
        g_DebugLog("ERROR: Modern list access failed");
        return -2;
    }
    
    // Test iteration
    int sum = 0;
    for (size_t i = 0; i < modernList.size(); ++i) {
        sum += modernList[i];
    }
    
    if (sum != 100) {
        g_DebugLog("ERROR: Modern list iteration failed. Sum: %d", sum);
        return -3;
    }
    
    g_DebugLog("Modern list passed: %d items, sum=%d", (int)modernList.size(), sum);
    
    // Test 2: Modern Map (std::map)
    g_DebugLog("=== Test 2: Modern Map (std::map) ===");
    
    std::map<std::string, int> modernMap;
    modernMap["apple"] = 100;
    modernMap["banana"] = 200;
    modernMap["cherry"] = 300;
    
    if (modernMap.size() != 3) {
        g_DebugLog("ERROR: Modern map size failed");
        return -4;
    }
    
    if (modernMap["apple"] != 100 || modernMap["cherry"] != 300) {
        g_DebugLog("ERROR: Modern map access failed");
        return -5;
    }
    
    // Test find
    std::map<std::string, int>::iterator it = modernMap.find("banana");
    if (it == modernMap.end() || it->second != 200) {
        g_DebugLog("ERROR: Modern map find failed");
        return -6;
    }
    
    g_DebugLog("Modern map passed: %d items", (int)modernMap.size());
    
    // Test 3: Modern Linked List (std::list)
    g_DebugLog("=== Test 3: Modern Linked List (std::list) ===");
    
    std::list<std::string> modernLinkedList;
    modernLinkedList.push_back("first");
    modernLinkedList.push_back("second");
    modernLinkedList.push_front("zero");
    modernLinkedList.push_back("third");
    
    if (modernLinkedList.size() != 4) {
        g_DebugLog("ERROR: Modern linked list size failed");
        return -7;
    }
    
    if (modernLinkedList.front() != "zero" || modernLinkedList.back() != "third") {
        g_DebugLog("ERROR: Modern linked list access failed");
        return -8;
    }
    
    // Test iteration
    std::string concatenated;
    for (std::list<std::string>::iterator it = modernLinkedList.begin(); 
         it != modernLinkedList.end(); ++it) {
        if (!concatenated.empty()) concatenated += ",";
        concatenated += *it;
    }
    
    if (concatenated != "zero,first,second,third") {
        g_DebugLog("ERROR: Modern linked list iteration failed. Got: '%s'", concatenated.c_str());
        return -9;
    }
    
    g_DebugLog("Modern linked list passed: %d items", (int)modernLinkedList.size());
    
    // Test 4: Modern Stack (std::stack)
    g_DebugLog("=== Test 4: Modern Stack (std::stack) ===");
    
    std::stack<int> modernStack;
    modernStack.push(10);
    modernStack.push(20);
    modernStack.push(30);
    
    if (modernStack.size() != 3) {
        g_DebugLog("ERROR: Modern stack size failed");
        return -10;
    }
    
    if (modernStack.top() != 30) {
        g_DebugLog("ERROR: Modern stack top failed");
        return -11;
    }
    
    // Test LIFO behavior
    std::vector<int> poppedValues;
    while (!modernStack.empty()) {
        poppedValues.push_back(modernStack.top());
        modernStack.pop();
    }
    
    if (poppedValues.size() != 3 || poppedValues[0] != 30 || 
        poppedValues[1] != 20 || poppedValues[2] != 10) {
        g_DebugLog("ERROR: Modern stack LIFO behavior failed");
        return -12;
    }
    
    g_DebugLog("Modern stack passed: LIFO behavior correct");
    
    // Test 5: Modern Queue (std::queue)
    g_DebugLog("=== Test 5: Modern Queue (std::queue) ===");
    
    std::queue<std::string> modernQueue;
    modernQueue.push("first");
    modernQueue.push("second");
    modernQueue.push("third");
    
    if (modernQueue.size() != 3) {
        g_DebugLog("ERROR: Modern queue size failed");
        return -13;
    }
    
    if (modernQueue.front() != "first" || modernQueue.back() != "third") {
        g_DebugLog("ERROR: Modern queue access failed");
        return -14;
    }
    
    // Test FIFO behavior
    std::vector<std::string> dequeuedValues;
    while (!modernQueue.empty()) {
        dequeuedValues.push_back(modernQueue.front());
        modernQueue.pop();
    }
    
    if (dequeuedValues.size() != 3 || dequeuedValues[0] != "first" || 
        dequeuedValues[1] != "second" || dequeuedValues[2] != "third") {
        g_DebugLog("ERROR: Modern queue FIFO behavior failed");
        return -15;
    }
    
    g_DebugLog("Modern queue passed: FIFO behavior correct");
    
    // Test 6: Complex data structure combinations
    g_DebugLog("=== Test 6: Complex Data Structure Combinations ===");
    
    // Map of vectors
    std::map<std::string, std::vector<int> > mapOfVectors;
    
    std::vector<int> scores1;
    scores1.push_back(85);
    scores1.push_back(92);
    scores1.push_back(78);
    mapOfVectors["student1"] = scores1;
    
    std::vector<int> scores2;
    scores2.push_back(90);
    scores2.push_back(88);
    scores2.push_back(95);
    mapOfVectors["student2"] = scores2;
    
    if (mapOfVectors.size() != 2) {
        g_DebugLog("ERROR: Map of vectors size failed");
        return -16;
    }
    
    if (mapOfVectors["student1"].size() != 3 || mapOfVectors["student1"][1] != 92) {
        g_DebugLog("ERROR: Map of vectors access failed");
        return -17;
    }
    
    // Calculate average scores
    double totalAverage = 0.0;
    int studentCount = 0;
    
    for (std::map<std::string, std::vector<int> >::iterator it = mapOfVectors.begin(); 
         it != mapOfVectors.end(); ++it) {
        int sum = 0;
        for (size_t i = 0; i < it->second.size(); ++i) {
            sum += it->second[i];
        }
        double average = (double)sum / it->second.size();
        totalAverage += average;
        studentCount++;
        
        g_DebugLog("  %s: average = %.1f", it->first.c_str(), average);
    }
    
    totalAverage /= studentCount;
    
    if (totalAverage < 87.0 || totalAverage > 89.0) {
        g_DebugLog("ERROR: Complex calculation failed. Average: %.1f", totalAverage);
        return -18;
    }
    
    g_DebugLog("Complex data structures passed: overall average = %.1f", totalAverage);
    
    // Test 7: Performance comparison
    g_DebugLog("=== Test 7: Performance Comparison ===");
    
    const int PERFORMANCE_TEST_SIZE = 1000;
    
    // Vector performance
    std::vector<int> perfVector;
    for (int i = 0; i < PERFORMANCE_TEST_SIZE; ++i) {
        perfVector.push_back(i);
    }
    
    // Map performance
    std::map<int, int> perfMap;
    for (int i = 0; i < PERFORMANCE_TEST_SIZE; ++i) {
        perfMap[i] = i * 2;
    }
    
    // Test random access
    int vectorSum = 0;
    for (int i = 0; i < PERFORMANCE_TEST_SIZE; i += 10) {
        vectorSum += perfVector[i];
    }
    
    int mapSum = 0;
    for (int i = 0; i < PERFORMANCE_TEST_SIZE; i += 10) {
        mapSum += perfMap[i];
    }
    
    g_DebugLog("Performance test passed: vector_sum=%d, map_sum=%d", vectorSum, mapSum);
    
    g_DebugLog("=== All comprehensive data structure tests passed! ===");
    return 0;
}

// Test main function
#ifdef _STANDALONE
int main() {
    g_DebugLog("Starting comprehensive data structures test suite...");
    
    int result = TestComprehensiveDataStructures();
    
    if (result == 0) {
        printf("SUCCESS: All comprehensive data structure tests passed!\n");
    } else {
        printf("FAILED: Comprehensive data structure test failed with code %d\n", result);
    }
    
    return result;
}
#endif
