// Minimal string system test - verify basic string operations without complex dependencies
#include "KWin32.h"
#include <string>
#include <vector>
#include <algorithm>
#include <cctype>
#include <sstream>
#include <stdio.h>
#include <stdarg.h>

// Simple debug implementation for testing
void g_DebugLog(const char* fmt, ...) {
    char buffer[1024];
    va_list va;
    
    va_start(va, fmt);
    vsnprintf(buffer, sizeof(buffer), fmt, va);
    va_end(va);
    
    printf("[DEBUG] %s\n", buffer);
}

// Basic string operations (simplified implementations)
namespace SimpleString {
    
    std::string ToUpper(const std::string& str) {
        std::string result = str;
        std::transform(result.begin(), result.end(), result.begin(), 
                      [](unsigned char c) { return std::toupper(c); });
        return result;
    }
    
    std::string ToLower(const std::string& str) {
        std::string result = str;
        std::transform(result.begin(), result.end(), result.begin(), 
                      [](unsigned char c) { return std::tolower(c); });
        return result;
    }
    
    std::string Trim(const std::string& str) {
        auto start = std::find_if(str.begin(), str.end(), 
                                 [](unsigned char c) { return !std::isspace(c); });
        auto end = std::find_if(str.rbegin(), str.rend(), 
                               [](unsigned char c) { return !std::isspace(c); }).base();
        return std::string(start, end);
    }
    
    std::string Replace(const std::string& str, const std::string& from, const std::string& to) {
        if (from.empty()) return str;
        
        std::string result = str;
        size_t pos = result.find(from);
        if (pos != std::string::npos) {
            result.replace(pos, from.length(), to);
        }
        return result;
    }
    
    bool Contains(const std::string& str, const std::string& substr) {
        return str.find(substr) != std::string::npos;
    }
    
    bool StartsWith(const std::string& str, const std::string& prefix) {
        return str.length() >= prefix.length() && 
               str.compare(0, prefix.length(), prefix) == 0;
    }
    
    bool EndsWith(const std::string& str, const std::string& suffix) {
        return str.length() >= suffix.length() && 
               str.compare(str.length() - suffix.length(), suffix.length(), suffix) == 0;
    }
    
    std::vector<std::string> Split(const std::string& str, char delimiter) {
        std::vector<std::string> result;
        std::stringstream ss(str);
        std::string item;
        
        while (std::getline(ss, item, delimiter)) {
            result.push_back(item);
        }
        
        return result;
    }
    
    std::string Join(const std::vector<std::string>& strings, const std::string& delimiter) {
        if (strings.empty()) return "";
        
        std::ostringstream oss;
        for (size_t i = 0; i < strings.size(); ++i) {
            if (i > 0) oss << delimiter;
            oss << strings[i];
        }
        
        return oss.str();
    }
    
    int ToInt(const std::string& str, int defaultValue = 0) {
        try {
            return std::stoi(Trim(str));
        } catch (...) {
            return defaultValue;
        }
    }
    
    float ToFloat(const std::string& str, float defaultValue = 0.0f) {
        try {
            return std::stof(Trim(str));
        } catch (...) {
            return defaultValue;
        }
    }
    
    bool ToBool(const std::string& str, bool defaultValue = false) {
        std::string trimmed = ToLower(Trim(str));
        if (trimmed == "true" || trimmed == "1" || trimmed == "yes") {
            return true;
        } else if (trimmed == "false" || trimmed == "0" || trimmed == "no") {
            return false;
        }
        return defaultValue;
    }
    
    std::string ToString(int value) {
        return std::to_string(value);
    }
}

// Simple modern string class
class SimpleStr {
private:
    std::string m_str;
    
public:
    SimpleStr() = default;
    SimpleStr(const char* str) : m_str(str ? str : "") {}
    SimpleStr(const std::string& str) : m_str(str) {}
    
    const char* c_str() const { return m_str.c_str(); }
    const std::string& str() const { return m_str; }
    
    SimpleStr& ToUpper() { m_str = SimpleString::ToUpper(m_str); return *this; }
    SimpleStr& ToLower() { m_str = SimpleString::ToLower(m_str); return *this; }
    SimpleStr& Trim() { m_str = SimpleString::Trim(m_str); return *this; }
    SimpleStr& Replace(const std::string& from, const std::string& to) { 
        m_str = SimpleString::Replace(m_str, from, to); return *this; 
    }
    
    int ToInt(int defaultValue = 0) const { return SimpleString::ToInt(m_str, defaultValue); }
};

// Test basic string functionality
ENGINE_API int TestMinimalStringOperations() {
    g_DebugLog("Starting minimal string operations test...");
    
    // Test 1: Basic string operations
    g_DebugLog("=== Test 1: Basic String Operations ===");
    
    std::string testStr = "  Hello World  ";
    std::string trimmed = SimpleString::Trim(testStr);
    if (trimmed != "Hello World") {
        g_DebugLog("ERROR: String trim failed. Got: '%s'", trimmed.c_str());
        return -1;
    }
    g_DebugLog("String trim passed: '%s'", trimmed.c_str());
    
    std::string upper = SimpleString::ToUpper(trimmed);
    if (upper != "HELLO WORLD") {
        g_DebugLog("ERROR: String upper failed. Got: '%s'", upper.c_str());
        return -2;
    }
    g_DebugLog("String upper passed: '%s'", upper.c_str());
    
    std::string replaced = SimpleString::Replace(upper, "WORLD", "UNIVERSE");
    if (replaced != "HELLO UNIVERSE") {
        g_DebugLog("ERROR: String replace failed. Got: '%s'", replaced.c_str());
        return -3;
    }
    g_DebugLog("String replace passed: '%s'", replaced.c_str());
    
    // Test 2: String splitting and joining
    g_DebugLog("=== Test 2: String Splitting and Joining ===");
    
    std::string csvData = "apple,banana,cherry";
    std::vector<std::string> fruits = SimpleString::Split(csvData, ',');
    
    if (fruits.size() != 3) {
        g_DebugLog("ERROR: String split failed. Expected 3 parts, got %d", (int)fruits.size());
        return -4;
    }
    
    std::string rejoined = SimpleString::Join(fruits, " | ");
    if (rejoined != "apple | banana | cherry") {
        g_DebugLog("ERROR: String join failed. Got: '%s'", rejoined.c_str());
        return -5;
    }
    g_DebugLog("String join passed: '%s'", rejoined.c_str());
    
    // Test 3: Type conversions
    g_DebugLog("=== Test 3: Type Conversions ===");
    
    int intVal = SimpleString::ToInt("42");
    if (intVal != 42) {
        g_DebugLog("ERROR: String to int failed. Got: %d", intVal);
        return -6;
    }
    g_DebugLog("String to int passed: %d", intVal);
    
    float floatVal = SimpleString::ToFloat("3.14");
    if (floatVal < 3.13f || floatVal > 3.15f) {
        g_DebugLog("ERROR: String to float failed. Got: %f", floatVal);
        return -7;
    }
    g_DebugLog("String to float passed: %f", floatVal);
    
    bool boolVal = SimpleString::ToBool("true");
    if (!boolVal) {
        g_DebugLog("ERROR: String to bool failed");
        return -8;
    }
    g_DebugLog("String to bool passed: %s", boolVal ? "true" : "false");
    
    // Test 4: String queries
    g_DebugLog("=== Test 4: String Queries ===");
    
    std::string queryStr = "Hello World";
    
    if (!SimpleString::Contains(queryStr, "World")) {
        g_DebugLog("ERROR: String contains failed");
        return -9;
    }
    
    if (!SimpleString::StartsWith(queryStr, "Hello")) {
        g_DebugLog("ERROR: String starts with failed");
        return -10;
    }
    
    if (!SimpleString::EndsWith(queryStr, "World")) {
        g_DebugLog("ERROR: String ends with failed");
        return -11;
    }
    g_DebugLog("String queries passed");
    
    // Test 5: SimpleStr class
    g_DebugLog("=== Test 5: SimpleStr Class ===");
    
    SimpleStr simpleStr("  Hello SimpleStr  ");
    simpleStr.Trim().ToUpper().Replace("HELLO", "HI");
    
    if (simpleStr.str() != "HI SIMPLESTR") {
        g_DebugLog("ERROR: SimpleStr chaining failed. Got: '%s'", simpleStr.c_str());
        return -12;
    }
    g_DebugLog("SimpleStr chaining passed: '%s'", simpleStr.c_str());
    
    SimpleStr numberStr("42");
    int number = numberStr.ToInt();
    if (number != 42) {
        g_DebugLog("ERROR: SimpleStr ToInt failed. Got: %d", number);
        return -13;
    }
    g_DebugLog("SimpleStr ToInt passed: %d", number);
    
    g_DebugLog("=== All minimal string tests passed! ===");
    return 0;
}

// Test main function
#ifdef _STANDALONE
int main() {
    g_DebugLog("Starting minimal string system test suite...");
    
    int result = TestMinimalStringOperations();
    
    if (result == 0) {
        printf("SUCCESS: All minimal string tests passed!\n");
    } else {
        printf("FAILED: Minimal string test failed with code %d\n", result);
    }
    
    return result;
}
#endif
