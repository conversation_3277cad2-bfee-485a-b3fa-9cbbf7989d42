// Simple thread system test - verify basic thread operations
#include "KWin32.h"
#include <thread>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <chrono>
#include <vector>
#include <queue>
#include <functional>
#include <stdio.h>
#include <stdarg.h>

// Simple debug implementation for testing
void g_DebugLog(const char* fmt, ...) {
    char buffer[1024];
    va_list va;
    
    va_start(va, fmt);
    vsnprintf(buffer, sizeof(buffer), fmt, va);
    va_end(va);
    
    printf("[DEBUG] %s\n", buffer);
}

// Simple modern thread class
class SimpleModernThread {
private:
    std::thread m_thread;
    std::atomic<bool> m_shouldStop;
    std::atomic<bool> m_isRunning;
    std::string m_name;
    
public:
    SimpleModernThread(const std::string& name = "") 
        : m_shouldStop(false), m_isRunning(false), m_name(name) {}
    
    ~SimpleModernThread() {
        if (m_thread.joinable()) {
            Stop();
            m_thread.join();
        }
    }
    
    template<typename Func>
    bool Start(Func&& func) {
        if (m_isRunning.load()) {
            return false;
        }
        
        m_shouldStop.store(false);
        m_thread = std::thread([this, func]() {
            m_isRunning.store(true);
            func();
            m_isRunning.store(false);
        });
        
        return true;
    }
    
    void Stop() {
        m_shouldStop.store(true);
    }
    
    bool ShouldStop() const {
        return m_shouldStop.load();
    }
    
    bool IsRunning() const {
        return m_isRunning.load();
    }
    
    void Join() {
        if (m_thread.joinable()) {
            m_thread.join();
        }
    }
    
    std::thread::id GetId() const {
        return m_thread.get_id();
    }
    
    const std::string& GetName() const {
        return m_name;
    }
    
    static void Sleep(int milliseconds) {
        std::this_thread::sleep_for(std::chrono::milliseconds(milliseconds));
    }
};

// Simple modern mutex class
class SimpleModernMutex {
private:
    std::mutex m_mutex;
    
public:
    void Lock() {
        m_mutex.lock();
    }
    
    void Unlock() {
        m_mutex.unlock();
    }
    
    bool TryLock() {
        return m_mutex.try_lock();
    }
    
    std::mutex& GetNative() {
        return m_mutex;
    }

    const std::mutex& GetNative() const {
        return m_mutex;
    }
};

// Simple auto lock class
class SimpleAutoLock {
private:
    SimpleModernMutex& m_mutex;
    
public:
    explicit SimpleAutoLock(SimpleModernMutex& mutex) : m_mutex(mutex) {
        m_mutex.Lock();
    }
    
    ~SimpleAutoLock() {
        m_mutex.Unlock();
    }
};

// Simple thread pool class
class SimpleThreadPool {
private:
    std::vector<std::thread> m_workers;
    std::queue<std::function<void()>> m_tasks;
    std::mutex m_queueMutex;
    std::condition_variable m_condition;
    std::atomic<bool> m_stop;
    
public:
    explicit SimpleThreadPool(size_t threadCount = std::thread::hardware_concurrency()) 
        : m_stop(false) {
        
        for (size_t i = 0; i < threadCount; ++i) {
            m_workers.emplace_back([this] {
                while (true) {
                    std::function<void()> task;
                    
                    {
                        std::unique_lock<std::mutex> lock(m_queueMutex);
                        m_condition.wait(lock, [this] { return m_stop || !m_tasks.empty(); });
                        
                        if (m_stop && m_tasks.empty()) {
                            return;
                        }
                        
                        task = std::move(m_tasks.front());
                        m_tasks.pop();
                    }
                    
                    task();
                }
            });
        }
    }
    
    ~SimpleThreadPool() {
        {
            std::unique_lock<std::mutex> lock(m_queueMutex);
            m_stop = true;
        }
        
        m_condition.notify_all();
        
        for (std::thread& worker : m_workers) {
            worker.join();
        }
    }
    
    template<typename Func>
    void Submit(Func&& func) {
        {
            std::unique_lock<std::mutex> lock(m_queueMutex);
            if (m_stop) {
                return;
            }
            m_tasks.emplace(std::forward<Func>(func));
        }
        m_condition.notify_one();
    }
    
    size_t GetThreadCount() const {
        return m_workers.size();
    }
    
    size_t GetQueueSize() const {
        std::unique_lock<std::mutex> lock(const_cast<std::mutex&>(m_queueMutex));
        return m_tasks.size();
    }
};

// Global test data
SimpleModernMutex g_testMutex;
std::atomic<int> g_sharedCounter(0);
std::vector<int> g_testResults;

// Test thread function
void TestThreadFunction(int threadId) {
    g_DebugLog("Thread %d started", threadId);
    
    for (int i = 0; i < 10; ++i) {
        // Test atomic operations
        int oldValue = g_sharedCounter.fetch_add(1);
        
        // Test mutex protection
        {
            SimpleAutoLock lock(g_testMutex);
            g_testResults.push_back(threadId * 100 + i);
        }
        
        // Simulate some work
        SimpleModernThread::Sleep(1);
    }
    
    g_DebugLog("Thread %d finished", threadId);
}

// Test simple thread system functionality
ENGINE_API int TestSimpleThreadSystem() {
    g_DebugLog("Starting simple thread system test...");
    
    // Test 1: Basic thread operations
    g_DebugLog("=== Test 1: Basic Thread Operations ===");
    
    SimpleModernThread thread1("TestThread1");
    
    bool started = thread1.Start([]() {
        g_DebugLog("Lambda thread started");
        for (int i = 0; i < 5; ++i) {
            g_DebugLog("Lambda thread working: %d", i);
            SimpleModernThread::Sleep(10);
        }
        g_DebugLog("Lambda thread finished");
    });
    
    if (!started) {
        g_DebugLog("ERROR: Failed to start thread");
        return -1;
    }

    // Give the thread a moment to start
    SimpleModernThread::Sleep(20);

    if (!thread1.IsRunning()) {
        g_DebugLog("ERROR: Thread should be running");
        return -2;
    }
    
    thread1.Join();
    
    if (thread1.IsRunning()) {
        g_DebugLog("ERROR: Thread should not be running after join");
        return -3;
    }
    
    g_DebugLog("Basic thread operations passed");
    
    // Test 2: Multiple threads with synchronization
    g_DebugLog("=== Test 2: Multiple Threads with Synchronization ===");
    
    g_sharedCounter.store(0);
    g_testResults.clear();
    
    const int NUM_THREADS = 3;
    std::vector<SimpleModernThread*> threads;
    
    // Create and start threads
    for (int i = 0; i < NUM_THREADS; ++i) {
        char threadName[32];
        sprintf(threadName, "WorkerThread%d", i);
        SimpleModernThread* thread = new SimpleModernThread(threadName);
        bool success = thread->Start([i]() { TestThreadFunction(i); });
        
        if (!success) {
            g_DebugLog("ERROR: Failed to start worker thread %d", i);
            return -4;
        }
        
        threads.push_back(thread);
    }
    
    // Wait for all threads to complete
    for (SimpleModernThread* thread : threads) {
        thread->Join();
        delete thread;
    }
    
    // Verify results
    if (g_sharedCounter.load() != NUM_THREADS * 10) {
        g_DebugLog("ERROR: Shared counter mismatch. Expected: %d, Got: %d", 
                   NUM_THREADS * 10, g_sharedCounter.load());
        return -5;
    }
    
    if (g_testResults.size() != NUM_THREADS * 10) {
        g_DebugLog("ERROR: Test results size mismatch. Expected: %d, Got: %d", 
                   NUM_THREADS * 10, (int)g_testResults.size());
        return -6;
    }
    
    g_DebugLog("Multiple threads test passed: counter=%d, results=%d", 
               g_sharedCounter.load(), (int)g_testResults.size());
    
    // Test 3: Thread pool
    g_DebugLog("=== Test 3: Thread Pool ===");
    
    std::atomic<int> poolCounter(0);
    const int NUM_TASKS = 20;
    
    {
        SimpleThreadPool pool(4);
        
        g_DebugLog("Thread pool created with %d threads", (int)pool.GetThreadCount());
        
        // Submit tasks
        for (int i = 0; i < NUM_TASKS; ++i) {
            pool.Submit([&poolCounter, i]() {
                poolCounter.fetch_add(1);
                SimpleModernThread::Sleep(5);
                g_DebugLog("Pool task %d completed", i);
            });
        }
        
        // Wait a bit for tasks to complete
        SimpleModernThread::Sleep(200);
        
    } // Pool destructor will wait for all tasks to complete
    
    if (poolCounter.load() != NUM_TASKS) {
        g_DebugLog("ERROR: Pool counter mismatch. Expected: %d, Got: %d", 
                   NUM_TASKS, poolCounter.load());
        return -7;
    }
    
    g_DebugLog("Thread pool test passed: %d tasks completed", poolCounter.load());
    
    // Test 4: Mutex and condition variable
    g_DebugLog("=== Test 4: Mutex and Condition Variable ===");
    
    SimpleModernMutex testMutex;
    std::condition_variable testCondition;
    std::atomic<bool> ready(false);
    std::atomic<int> waitingThreads(0);
    
    // Producer thread
    SimpleModernThread producer("Producer");
    producer.Start([&]() {
        SimpleModernThread::Sleep(50);
        
        {
            std::unique_lock<std::mutex> lock(testMutex.GetNative());
            ready.store(true);
        }
        testCondition.notify_all();
        
        g_DebugLog("Producer notified all waiting threads");
    });
    
    // Consumer threads
    std::vector<SimpleModernThread*> consumers;
    for (int i = 0; i < 3; ++i) {
        char consumerName[32];
        sprintf(consumerName, "Consumer%d", i);
        SimpleModernThread* consumer = new SimpleModernThread(consumerName);
        consumer->Start([&, i]() {
            waitingThreads.fetch_add(1);
            
            std::unique_lock<std::mutex> lock(testMutex.GetNative());
            testCondition.wait(lock, [&] { return ready.load(); });
            
            g_DebugLog("Consumer %d woke up", i);
        });
        consumers.push_back(consumer);
    }
    
    // Wait for all threads
    producer.Join();
    for (SimpleModernThread* consumer : consumers) {
        consumer->Join();
        delete consumer;
    }
    
    if (waitingThreads.load() != 3) {
        g_DebugLog("ERROR: Waiting threads count mismatch. Expected: 3, Got: %d", 
                   waitingThreads.load());
        return -8;
    }
    
    g_DebugLog("Condition variable test passed: %d threads synchronized", waitingThreads.load());
    
    // Test 5: Hardware concurrency
    g_DebugLog("=== Test 5: Hardware Information ===");
    
    unsigned int hwConcurrency = std::thread::hardware_concurrency();
    g_DebugLog("Hardware concurrency: %u threads", hwConcurrency);
    
    if (hwConcurrency == 0) {
        g_DebugLog("WARNING: Hardware concurrency detection failed");
    }
    
    g_DebugLog("Hardware information test passed");
    
    g_DebugLog("=== All simple thread system tests passed! ===");
    return 0;
}

// Test main function
#ifdef _STANDALONE
int main() {
    g_DebugLog("Starting simple thread system test suite...");
    
    int result = TestSimpleThreadSystem();
    
    if (result == 0) {
        printf("SUCCESS: All simple thread system tests passed!\n");
    } else {
        printf("FAILED: Simple thread system test failed with code %d\n", result);
    }
    
    return result;
}
#endif
