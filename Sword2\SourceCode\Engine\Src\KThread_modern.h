//---------------------------------------------------------------------------
// Sword3 Engine - Modernized Thread System
// 现代化的线程系统 - 使用std::thread和现代C++特性
//---------------------------------------------------------------------------
#ifndef KThread_Modern_H
#define KThread_Modern_H

#include "KWin32.h"
#include <thread>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <functional>
#include <future>
#include <chrono>
#include <memory>

//---------------------------------------------------------------------------
// 现代化的线程类
//---------------------------------------------------------------------------
#ifndef __linux
class ENGINE_API KModernThread
#else
class KModernThread
#endif
{
public:
    // 线程函数类型定义
    using ThreadFunction = std::function<void()>;
    using ThreadFunctionWithParam = std::function<void(void*)>;
    
    // 线程状态枚举
    enum class ThreadState {
        NotStarted,
        Running,
        Paused,
        Stopping,
        Stopped,
        Error
    };
    
private:
    std::unique_ptr<std::thread> m_thread;
    std::atomic<ThreadState> m_state;
    std::atomic<bool> m_shouldStop;
    std::atomic<bool> m_isPaused;
    
    std::mutex m_pauseMutex;
    std::condition_variable m_pauseCondition;
    std::mutex m_stateMutex;
    
    ThreadFunction m_threadFunction;
    void* m_userData;
    std::string m_threadName;
    
public:
    // 构造函数和析构函数
    KModernThread();
    explicit KModernThread(const std::string& name);
    ~KModernThread();
    
    // 禁用拷贝构造和赋值
    KModernThread(const KModernThread&) = delete;
    KModernThread& operator=(const KModernThread&) = delete;
    
    // 兼容性接口
    BOOL Create(TThreadFunc lpFunc, void* lpParam);
    void Destroy();
    void Suspend();
    void Resume();
    BOOL IsRunning();
    void WaitForExit();
    int GetPriority();
    BOOL SetPriority(int priority);
    
    // 现代化接口
    bool Start(ThreadFunction func);
    bool Start(ThreadFunctionWithParam func, void* userData);
    void Stop();
    void Pause();
    void Resume();
    
    bool Join(std::chrono::milliseconds timeout = std::chrono::milliseconds::zero());
    void Detach();
    
    // 状态查询
    ThreadState GetState() const;
    bool IsJoinable() const;
    bool ShouldStop() const;
    bool IsPaused() const;
    
    // 线程属性
    void SetName(const std::string& name);
    std::string GetName() const;
    std::thread::id GetId() const;
    
    // 静态工具函数
    static void Sleep(std::chrono::milliseconds duration);
    static void Yield();
    static std::thread::id GetCurrentThreadId();
    static unsigned int GetHardwareConcurrency();
    
private:
    // 内部线程包装函数
    void ThreadWrapper();
    void ThreadWrapperWithParam();
    void CheckPausePoint();
    void SetState(ThreadState newState);
};

//---------------------------------------------------------------------------
// 现代化的互斥锁类
//---------------------------------------------------------------------------
#ifndef __linux
class ENGINE_API KModernMutex
#else
class KModernMutex
#endif
{
private:
    mutable std::mutex m_mutex;
    mutable std::recursive_mutex m_recursiveMutex;
    bool m_isRecursive;
    
public:
    // 构造函数
    explicit KModernMutex(bool recursive = false);
    ~KModernMutex() = default;
    
    // 禁用拷贝构造和赋值
    KModernMutex(const KModernMutex&) = delete;
    KModernMutex& operator=(const KModernMutex&) = delete;
    
    // 兼容性接口
    void Lock();
    void Unlock();
    bool TryLock();
    
    // 现代化接口
    void lock();
    void unlock();
    bool try_lock();
    
    // 超时锁定
    template<typename Rep, typename Period>
    bool try_lock_for(const std::chrono::duration<Rep, Period>& timeout);
    
    template<typename Clock, typename Duration>
    bool try_lock_until(const std::chrono::time_point<Clock, Duration>& timeout);
    
    // 获取原生互斥锁（用于条件变量）
    std::mutex& GetNativeMutex();
    std::recursive_mutex& GetNativeRecursiveMutex();
};

//---------------------------------------------------------------------------
// 现代化的自动锁类
//---------------------------------------------------------------------------
template<typename MutexType>
class KModernLockGuard {
private:
    MutexType& m_mutex;
    
public:
    explicit KModernLockGuard(MutexType& mutex) : m_mutex(mutex) {
        m_mutex.lock();
    }
    
    ~KModernLockGuard() {
        m_mutex.unlock();
    }
    
    // 禁用拷贝构造和赋值
    KModernLockGuard(const KModernLockGuard&) = delete;
    KModernLockGuard& operator=(const KModernLockGuard&) = delete;
};

// 类型别名
using KAutoLock = KModernLockGuard<KModernMutex>;
using KAutoStdLock = std::lock_guard<std::mutex>;
using KUniqueStdLock = std::unique_lock<std::mutex>;

//---------------------------------------------------------------------------
// 现代化的条件变量类
//---------------------------------------------------------------------------
#ifndef __linux
class ENGINE_API KModernConditionVariable
#else
class KModernConditionVariable
#endif
{
private:
    std::condition_variable m_condVar;
    
public:
    KModernConditionVariable() = default;
    ~KModernConditionVariable() = default;
    
    // 禁用拷贝构造和赋值
    KModernConditionVariable(const KModernConditionVariable&) = delete;
    KModernConditionVariable& operator=(const KModernConditionVariable&) = delete;
    
    // 通知操作
    void NotifyOne();
    void NotifyAll();
    
    // 等待操作
    void Wait(std::unique_lock<std::mutex>& lock);
    
    template<typename Predicate>
    void Wait(std::unique_lock<std::mutex>& lock, Predicate pred);
    
    template<typename Rep, typename Period>
    std::cv_status WaitFor(std::unique_lock<std::mutex>& lock, 
                          const std::chrono::duration<Rep, Period>& timeout);
    
    template<typename Rep, typename Period, typename Predicate>
    bool WaitFor(std::unique_lock<std::mutex>& lock, 
                const std::chrono::duration<Rep, Period>& timeout, 
                Predicate pred);
    
    template<typename Clock, typename Duration>
    std::cv_status WaitUntil(std::unique_lock<std::mutex>& lock, 
                            const std::chrono::time_point<Clock, Duration>& timeout);
    
    template<typename Clock, typename Duration, typename Predicate>
    bool WaitUntil(std::unique_lock<std::mutex>& lock, 
                  const std::chrono::time_point<Clock, Duration>& timeout, 
                  Predicate pred);
};

//---------------------------------------------------------------------------
// 现代化的线程池类
//---------------------------------------------------------------------------
#ifndef __linux
class ENGINE_API KModernThreadPool
#else
class KModernThreadPool
#endif
{
public:
    using Task = std::function<void()>;
    
private:
    std::vector<std::unique_ptr<KModernThread>> m_workers;
    std::queue<Task> m_tasks;
    
    std::mutex m_queueMutex;
    std::condition_variable m_condition;
    std::atomic<bool> m_stop;
    
    size_t m_threadCount;
    
public:
    // 构造函数和析构函数
    explicit KModernThreadPool(size_t threadCount = std::thread::hardware_concurrency());
    ~KModernThreadPool();
    
    // 禁用拷贝构造和赋值
    KModernThreadPool(const KModernThreadPool&) = delete;
    KModernThreadPool& operator=(const KModernThreadPool&) = delete;
    
    // 任务提交
    template<typename F, typename... Args>
    auto Submit(F&& f, Args&&... args) -> std::future<typename std::result_of<F(Args...)>::type>;
    
    void SubmitTask(Task task);
    
    // 线程池管理
    void Start();
    void Stop();
    void WaitForAll();
    
    // 状态查询
    size_t GetThreadCount() const;
    size_t GetQueueSize() const;
    bool IsRunning() const;
    
private:
    void WorkerFunction();
};

//---------------------------------------------------------------------------
// 现代化的原子操作类
//---------------------------------------------------------------------------
template<typename T>
class KModernAtomic {
private:
    std::atomic<T> m_value;
    
public:
    KModernAtomic() : m_value{} {}
    explicit KModernAtomic(T value) : m_value(value) {}
    
    // 基本操作
    T Load() const { return m_value.load(); }
    void Store(T value) { m_value.store(value); }
    T Exchange(T value) { return m_value.exchange(value); }
    
    // 比较交换
    bool CompareExchange(T& expected, T desired) {
        return m_value.compare_exchange_weak(expected, desired);
    }
    
    // 算术操作（仅适用于数值类型）
    T FetchAdd(T value) { return m_value.fetch_add(value); }
    T FetchSub(T value) { return m_value.fetch_sub(value); }
    
    // 操作符重载
    operator T() const { return Load(); }
    T operator=(T value) { Store(value); return value; }
    T operator++() { return FetchAdd(1) + 1; }
    T operator++(int) { return FetchAdd(1); }
    T operator--() { return FetchSub(1) - 1; }
    T operator--(int) { return FetchSub(1); }
};

//---------------------------------------------------------------------------
// 常用类型的typedef
//---------------------------------------------------------------------------
using KAtomicInt = KModernAtomic<int>;
using KAtomicBool = KModernAtomic<bool>;
using KAtomicLong = KModernAtomic<long>;
using KAtomicPtr = KModernAtomic<void*>;

//---------------------------------------------------------------------------
// 工厂函数
//---------------------------------------------------------------------------
std::unique_ptr<KModernThread> CreateModernThread(const std::string& name = "");
std::unique_ptr<KModernMutex> CreateModernMutex(bool recursive = false);
std::unique_ptr<KModernThreadPool> CreateModernThreadPool(size_t threadCount = 0);

//---------------------------------------------------------------------------
#endif // KThread_Modern_H
