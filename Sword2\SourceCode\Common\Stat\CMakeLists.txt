# Stat库 CMakeLists.txt
# 统计系统库

cmake_minimum_required(VERSION 3.16)

# 设置项目名称
set(STAT_LIB_NAME Stat)

# 创建统计库目标
add_library(${STAT_LIB_NAME} STATIC)

# 收集所有源文件
file(GLOB STAT_SOURCES 
    "${CMAKE_CURRENT_SOURCE_DIR}/*.cpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/*.c"
)

file(GLOB STAT_HEADERS 
    "${CMAKE_CURRENT_SOURCE_DIR}/*.h"
)

# 添加源文件到目标
if(STAT_SOURCES)
    target_sources(${STAT_LIB_NAME} PRIVATE ${STAT_SOURCES} ${STAT_HEADERS})
    message(STATUS "Stat: Found ${CMAKE_LIST_LENGTH} source files")
else()
    # 如果没有源文件，创建一个空的源文件
    set(EMPTY_SOURCE_FILE "${CMAKE_CURRENT_BINARY_DIR}/empty_stat.cpp")
    file(WRITE ${EMPTY_SOURCE_FILE} "// Empty source file for Stat library\n")
    target_sources(${STAT_LIB_NAME} PRIVATE ${EMPTY_SOURCE_FILE})
    message(STATUS "Stat: No sources found, created empty library")
endif()

# 设置包含目录
target_include_directories(${STAT_LIB_NAME} PUBLIC 
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_SOURCE_DIR}/Sword2/SourceCode/Include
    ${CMAKE_SOURCE_DIR}/Sword2/SourceCode/Engine/Include
)

# 链接依赖库
target_link_libraries(${STAT_LIB_NAME} PUBLIC
    Core
    Engine
)

# 如果找到MySQL，添加MySQL支持
if(MYSQL_FOUND)
    target_link_libraries(${STAT_LIB_NAME} PRIVATE MySQL::MySQL)
    target_compile_definitions(${STAT_LIB_NAME} PRIVATE HAVE_MYSQL)
endif()

# 设置编译属性
set_target_properties(${STAT_LIB_NAME} PROPERTIES
    OUTPUT_NAME "Stat"
    ARCHIVE_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib"
)

# 平台特定设置
if(WIN32)
    target_compile_definitions(${STAT_LIB_NAME} PRIVATE
        WIN32
        _WINDOWS
        _LIB
    )
endif()

message(STATUS "Stat library configured")
