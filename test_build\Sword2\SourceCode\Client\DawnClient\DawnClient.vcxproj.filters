﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\DawnClient.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\DawnUi.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\GlobalSetting.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ErrorCode.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\stdafx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\GameAD.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\dawnuieditor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\BufferPool.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\ExceptionManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\SimpleSymbolEngine.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\SpecialFuncs.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\WndCloseBtn.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\WndDropDownList.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\WndList3.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\autolocatewnd.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kboundslot.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kcolor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kcompositewidget.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kconststringmanager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kevent.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\keventset.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kfont.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kimage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kimagepart.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kpicframe.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kprocessbar.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kproperty.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kpropertyhelper.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kpropertyset.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\krect.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\krichtext.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\krolemodel.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kshadow.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\ksimpletext.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\ksubscriberslot.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwebpage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtbutton.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtcheckbox.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtedit.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgteffect.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgteffectbutton.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtimage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtimagepart.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtitemlistbox.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtlabel.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtlabelbutton.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtlistitem.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtmatrix.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtmixtypesetting.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtmsglistbox.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtobjectbox.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtpicframe.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtprocessbar.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtrichtext.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtrichtextbox.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtrolemodel.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtscrollbar.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtsliderbar.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgttree.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgttreeitem.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwidget.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwidgetfactories.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwidgetfactorymanager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwidgetmanage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwndanimation.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwndie.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwndlayoutmanager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\mousehover.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\popupmenu.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\regexpr2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\syntax2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\textpic.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\uicursor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\uiimage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\uistatus.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndHelpWnd.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndbutton.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndedit.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndimage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndlabeledbutton.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndlist.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndmessagelistbox.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndobjcontainer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wnds.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndscrollbar.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndtext.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndwindow.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\gamespacechangednotify.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\globaleventset.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\function_def\wndmanager_func.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\function_def\wndwindow_func.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua51_script_ui.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua_tinker.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\trace_dlg.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\luafunctor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\uischeme_analyzer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\window_alloc.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\window_event_scan.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\window_factor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\window_manager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\window_msg_filter.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\window_template.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\shortcutkey.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uibase.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\KMainSkillTable.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\KeySequence.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiAntiEnthrallment.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiBaguaCompose.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiBaguaDepose.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiBattleFieldInfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiBattleInfoFrame.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiBattlePlayerInfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiBattleRankInfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiBesetItem.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiBroadCastMessage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiButtomIB.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiCampFrame.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiCampHero.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiCampInfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiChannelItemView.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiChatForbid.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiClewInfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiComposeItem.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiComposeLingShi.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiComposeProgress.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiComposeSkillBase.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiComposeSkillFrame.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiCooling.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiDiceWnd.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiECard.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiEKey.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiEnhanceItem.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiEpurateStone.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiEpurateStuff.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiFairy.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiFairyMode.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiFairyTalk.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiFightSkillFrame.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiFollowInfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiForceUnlock.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiGatherProgress.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiGestConvention.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiGestConventionFrame.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiGestConventionRank.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiGmPanelFrame.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiGmPanelPad.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiGutTalk.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiHealthGame.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiHelpPopTips.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiHitCount.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiIbFrame.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiIbNavigation.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiIbOtherType.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiIbPreview.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiIbResult.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiIbSilver.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiInformationTimeCount.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiInputPos.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiItemDetail.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiKeyboard.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiLoading.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiMainboardOptions.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiMessageBox.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiMonsterHandbook.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiMsgCentreBar.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiMsgCentrePad.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiMultiRoles.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiNewGather.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiNewsMessage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiOptionFrame.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiParadeItem.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiPlayerAttribute.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiPlayerAutoEat.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiPlayerBaseInfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiPlayerBuff.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiPlayerInfoFrame.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiPlayerTreasure.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiQuickBar.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiRoleButton.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiSceneMessage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiSecretSkillPad.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiShortcutKey.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiSkillKeySequence.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiSkillPadSwitch.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiSkillProgress.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiStall.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiStallItem.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiStallManage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiStrengthRank.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTeamOverview.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTimeGuage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTongCommonWnd.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTongFrame.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTongFuli.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTongManagement.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTongTech.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTransformItemAttr.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTrembleItem.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTrusteeship.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiAgreement.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiRoleStatus.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uibodymoveselector.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uibrifeskill.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uichatcentre.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uichatstatus.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiconnectinfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uidetailSkill.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiescdlg.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uifaceselector.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uigame.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uigamespaceshadow.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uigetmoney.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uigetstring.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiinit.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiitem.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uilearnskill.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uilifeskillframe.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uilogin.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiloginbg.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiminimap.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiminirolestatus.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uimsgsel.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uinewplayer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uioptions.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiplayerbar.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiplayermingli.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiplayvideo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiproduce.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uireconnect.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uirepair.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiselplayer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiselserver.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uishop.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiskillframe.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uistorebox.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uisysmsgcentre.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uitaskaccept.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uitaskdatafile.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uitaskfinish.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uitasknote.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uitasktrace.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiteammanage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uitrade.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uitradeconfirmwnd.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiworldmap.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uichatphrase.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uishell.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uisoundsetting.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\netconnect\netconnectagent.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\login\login.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\textctrlcmd\textctrlcmd.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\igw\UiIGW.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\Plugin\pluginmanager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ClientFStringTable.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ClientGStringTable.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\StringTable.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\resource.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\DawnClient.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\DawnUi.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ErrorCode.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\GlobalSetting.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\Include\DawnUiPlugin.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\Include\GlobalSetting.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\Include\WndWindow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\Include\dawnuieditor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\Include\kgui.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\Include\pluginmanager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\Include\singleton.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\Plugin\DawnUiPlugin.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\Plugin\pluginmanager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\Stdafx.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\igw\SDOA4Client.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\igw\SDOADx9.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\igw\UiIGW.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\login\login.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\netconnect\netconnectagent.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\netconnect\netmsgtargetobject.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\textctrlcmd\textctrlcmd.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\GameAD.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\dawnuieditor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\BufferPool.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\ExceptionManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\KLinerGradientControl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\Link.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\ObjectPool.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\SimpleSymbolEngine.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\SpecialFuncs.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\WndCloseBtn.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\WndDropDownList.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\WndList3.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\autolocatewnd.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kboundslot.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kcolor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kcompositewidget.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kconststringmanager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kcoreeventargs.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kcountedptr.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kevent.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\keventargs.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\keventset.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kfont.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kfreefunctionslot.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kfunctorcopyslot.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kimage.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kimagepart.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kinputeventargs.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kmemberfunctionslot.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kpicframe.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kpoint.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kprerequisites.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kprocessbar.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kproperty.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kpropertyhelper.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kpropertyset.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\krect.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\krepresentitem.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\krichtext.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\krolemodel.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kshadow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\ksimpletext.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\ksize.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kslotfunctorbase.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\ksubscriberslot.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kvector.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwebpage.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtbutton.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtcheckbox.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtedit.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgteffect.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgteffectbutton.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtimage.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtimagepart.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtitemlistbox.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtlabel.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtlabelbutton.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtlistitem.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtmatrix.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtmixtypesetting.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtmsglistbox.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtobjectbox.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtpicframe.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtprocessbar.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtrichtext.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtrichtextbox.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtrolemodel.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtscrollbar.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtsliderbar.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgttree.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgttreeitem.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwidget.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwidgetfactories.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwidgetfactorymanager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwidgetfactorytempalte.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwidgetmanage.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwidgetproperties.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwndanimation.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwndie.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwndlayoutmanager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\mousehover.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\popupmenu.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\regexpr2.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\reimpl2.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\restack.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\singleton.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\syntax2.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\textpic.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\uicursor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\uiimage.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\uistatus.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\windowtemplate.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndHelpWnd.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndbutton.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wnddatastruct.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndedit.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndimage.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndlabeledbutton.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndlist.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndmessage.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndmessagelistbox.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndobjcontainer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wnds.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndscrollbar.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndtext.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndwindow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\globaleventset.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\function_def\wndmanager_func.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\function_def\wndwindow_func.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua51_iscript_ui.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua51_script_ui.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\lapi.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\lauxlib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\lcode.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\ldebug.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\ldo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\lfunc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\lgc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\llex.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\llimits.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\lmem.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\lobject.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\lopcodes.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\lparser.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\lstate.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\lstring.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\ltable.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\ltm.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\lua.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\luaconf.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\lualib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\lundump.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\lvm.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\lzio.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua_tinker.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\resource.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\stdafx.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\trace_dlg.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\neodll.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\LuaMarco.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\iwindow_manager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\luafunctor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\swindow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\uischeme_analyzer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\window_alloc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\window_event_scan.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\window_factor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\window_manager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\window_msg_crack.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\window_msg_filter.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\window_template.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\shortcutkey.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uibase.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\KMainSkillTable.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\KeySequence.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UIMainboardOptions.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiAntiEnthrallment.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiBaguaCompose.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiBaguaDepose.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiBattleFieldInfo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiBattleInfoFrame.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiBattleInfoPad.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiBattlePlayerInfo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiBattleRankInfo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiBesetItem.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiBroadCastMessage.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiButtomIB.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiCampFrame.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiCampHero.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiCampInfo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiChannelItemView.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiChatForbid.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiClewInfo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiComposeItem.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiComposeLingShi.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiComposeProgress.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiComposeSkillBase.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiComposeSkillFrame.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiCooling.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiDiceWnd.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiECard.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiEKey.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiEnhanceItem.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiEpurateStone.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiEpurateStuff.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiFairy.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiFairyMode.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiFairyTalk.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiFightSkillFrame.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiFollowInfo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiForceUnlock.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiGatherProgress.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiGestConvention.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiGestConventionFrame.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiGestConventionRank.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiGestInfoPad.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiGmPanelFrame.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiGmPanelPad.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiGutTalk.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiHealthGame.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiHelpPopTips.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiHitCount.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiIbFrame.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiIbNavigation.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiIbOtherType.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiIbPreview.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiIbResult.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiIbSilver.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiInformationTimeCount.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiInputPos.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiItemDetail.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiKeyboard.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiLoading.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiMessageBox.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiMonsterHandbook.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiMsgCentreBar.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiMultiRoles.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiNewGather.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiNewsMessage.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiOptionFrame.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiParadeItem.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiPlayerAttribute.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiPlayerAutoEat.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiPlayerBaseInfo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiPlayerBuff.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiPlayerInfoFrame.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiPlayerInfoPad.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiPlayerTreasure.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiQuickBar.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiRoleButton.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiRoleStatus.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiSceneMessage.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiSecretSkillPad.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiShortcutKey.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiSkillKeySequence.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiSkillPadSwitch.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiSkillProgress.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiStall.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiStallItem.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiStallManage.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiStrengthRank.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTeamOverview.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTimeGuage.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTongCommonWnd.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTongFrame.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTongFuli.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTongManagement.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTongTech.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTransformItemAttr.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTrembleItem.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTrusteeship.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiAgreement.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uibodymoveselector.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uibrifeskill.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uichatcentre.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uichatstatus.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiconnectinfo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uidetailskill.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiescdlg.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uifaceselector.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uigame.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uigamespaceshadow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uigetmoney.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uigetstring.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiinit.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiitem.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uilearnskill.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uilifeskillframe.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uilogin.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiloginbg.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiminimap.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiminirolestatus.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uimsgcentrepad.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uimsgsel.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uinewplayer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uioptions.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiplayerbar.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiplayermingli.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiplayvideo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiproduce.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uireconnect.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uirepair.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiselplayer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiselserver.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uishop.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiskillframe.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uistorebox.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uisysmsgcentre.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uitaskaccept.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uitaskdatafile.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uitaskfinish.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uitasknote.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uitasktrace.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiteammanage.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uitrade.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uitradeconfirmwnd.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiworldmap.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uichatphrase.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uishell.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uisoundsetting.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\wxeditor\adjustproperty.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\wxeditor\editortouiimpl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\wxeditor\globaldll.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\wxeditor\layertree.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\wxeditor\menuaction.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\wxeditor\namegenerator.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\wxeditor\qtpropertybrowser\qteditorfactory.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\wxeditor\qtpropertybrowser\qtpropertybrowser.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\wxeditor\qtpropertybrowser\qtpropertybrowserutils_p.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\wxeditor\qtpropertybrowser\qtpropertymanager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\wxeditor\qtpropertybrowser\qttreepropertybrowser.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\wxeditor\qtpropertybrowser\qtvariantproperty.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\wxeditor\qtwinbridge\qmfcapp.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\wxeditor\qtwinbridge\qwinwidget.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\wxeditor\toolbox.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\wxeditor\widgetfileparser.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\wxeditor\widgetpropertysetting.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\wxeditor\wxdawnuiplugin.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\wxeditor\wxeditor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\VersionNo.rc2" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\DawnClient.rc">
      <Filter>Source Files</Filter>
    </ResourceCompile>
    <ResourceCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\S3Client.rc">
      <Filter>Source Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{5BBBD2BF-96B6-3EE1-8EBA-AFDC2DC00F3A}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{44AB8148-4A3D-32F7-9B57-640603BD4364}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
