//---------------------------------------------------------------------------
// Sword3 Engine - Modernized String Node
// 现代化的字符串节点 - 使用std::string替代固定长度字符数组
//---------------------------------------------------------------------------
#ifndef KStrNode_Modern_H
#define KStrNode_Modern_H

#include "KWin32.h"
#include "KNode.h"
#include "KStrBase_modern.h"
#include <string>

//---------------------------------------------------------------------------
// 现代化的字符串节点类
//---------------------------------------------------------------------------
#ifndef __linux
class ENGINE_API KStrNode : public KNode
#else
class KStrNode : public KNode
#endif
{
private:
    std::string m_name;  // 现代化：使用std::string替代固定长度数组
    
public:
    // 构造函数
    KStrNode();
    KStrNode(const char* name);
    KStrNode(const std::string& name);
    KStrNode(const KStrNode& other);
    KStrNode(KStrNode&& other) noexcept;
    
    // 析构函数
    virtual ~KStrNode() = default;
    
    // 赋值操作符
    KStrNode& operator=(const KStrNode& other);
    KStrNode& operator=(KStrNode&& other) noexcept;
    KStrNode& operator=(const char* name);
    KStrNode& operator=(const std::string& name);
    
    // 兼容性接口（保持与原有代码兼容）
    virtual char* GetName() { return const_cast<char*>(m_name.c_str()); }
    virtual const char* GetName() const { return m_name.c_str(); }
    virtual void SetName(char* str);
    virtual void SetName(const char* str);
    
    // 现代化接口
    const std::string& GetNameStr() const { return m_name; }
    void SetNameStr(const std::string& name) { m_name = name; }
    
    // 字符串操作
    void ToUpper() { m_name = ModernString::ToUpper(m_name); }
    void ToLower() { m_name = ModernString::ToLower(m_name); }
    void Trim() { m_name = ModernString::Trim(m_name); }
    
    // 查询操作
    bool IsEmpty() const { return m_name.empty(); }
    size_t GetLength() const { return m_name.length(); }
    bool Contains(const std::string& substr) const { return ModernString::Contains(m_name, substr); }
    bool StartsWith(const std::string& prefix) const { return ModernString::StartsWith(m_name, prefix); }
    bool EndsWith(const std::string& suffix) const { return ModernString::EndsWith(m_name, suffix); }
    
    // 比较操作
    bool Equals(const char* str) const { return str ? (m_name == str) : m_name.empty(); }
    bool Equals(const std::string& str) const { return m_name == str; }
    bool EqualsIgnoreCase(const char* str) const;
    bool EqualsIgnoreCase(const std::string& str) const;
    
    // 操作符重载
    bool operator==(const KStrNode& other) const { return m_name == other.m_name; }
    bool operator!=(const KStrNode& other) const { return m_name != other.m_name; }
    bool operator<(const KStrNode& other) const { return m_name < other.m_name; }
    bool operator==(const char* str) const { return Equals(str); }
    bool operator==(const std::string& str) const { return m_name == str; }
    
    // 调试和输出
    virtual void DebugPrint() const;
};

//---------------------------------------------------------------------------
// 现代化的字符串节点模板类（支持额外数据）
//---------------------------------------------------------------------------
template<typename T>
class KStrNodeT : public KStrNode {
private:
    T m_data;
    
public:
    // 构造函数
    KStrNodeT() = default;
    KStrNodeT(const char* name, const T& data) : KStrNode(name), m_data(data) {}
    KStrNodeT(const std::string& name, const T& data) : KStrNode(name), m_data(data) {}
    
    // 数据访问
    T& GetData() { return m_data; }
    const T& GetData() const { return m_data; }
    void SetData(const T& data) { m_data = data; }
    
    // 操作符重载
    T& operator*() { return m_data; }
    const T& operator*() const { return m_data; }
    T* operator->() { return &m_data; }
    const T* operator->() const { return &m_data; }
};

//---------------------------------------------------------------------------
// 常用的特化类型定义
//---------------------------------------------------------------------------
using KStrIntNode = KStrNodeT<int>;
using KStrFloatNode = KStrNodeT<float>;
using KStrBoolNode = KStrNodeT<bool>;
using KStrPtrNode = KStrNodeT<void*>;

//---------------------------------------------------------------------------
#endif // KStrNode_Modern_H
