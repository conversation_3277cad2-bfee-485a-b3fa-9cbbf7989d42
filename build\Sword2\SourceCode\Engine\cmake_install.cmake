# Install script for directory: D:/Sword2Code/Sword2/SourceCode/Engine

# Set the install prefix
if(NOT DEFINED CMAKE_INSTALL_PREFIX)
  set(CMAKE_INSTALL_PREFIX "C:/Program Files/Sword2")
endif()
string(REGEX REPLACE "/$" "" CMAKE_INSTALL_PREFIX "${CMAKE_INSTALL_PREFIX}")

# Set the install configuration name.
if(NOT DEFINED CMAKE_INSTALL_CONFIG_NAME)
  if(BUILD_TYPE)
    string(REGEX REPLACE "^[^A-Za-z0-9_]+" ""
           CMAKE_INSTALL_CONFIG_NAME "${BUILD_TYPE}")
  else()
    set(CMAKE_INSTALL_CONFIG_NAME "Release")
  endif()
  message(STATUS "Install configuration: \"${CMAKE_INSTALL_CONFIG_NAME}\"")
endif()

# Set the component getting installed.
if(NOT CMAKE_INSTALL_COMPONENT)
  if(COMPONENT)
    message(STATUS "Install component: \"${COMPONENT}\"")
    set(CMAKE_INSTALL_COMPONENT "${COMPONENT}")
  else()
    set(CMAKE_INSTALL_COMPONENT)
  endif()
endif()

# Is this installation the result of a crosscompile?
if(NOT DEFINED CMAKE_CROSSCOMPILING)
  set(CMAKE_CROSSCOMPILING "FALSE")
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/lib" TYPE STATIC_LIBRARY FILES "D:/Sword2Code/build/lib/Debug/EngineD.lib")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/lib" TYPE STATIC_LIBRARY FILES "D:/Sword2Code/build/lib/Release/Engine.lib")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/lib" TYPE STATIC_LIBRARY FILES "D:/Sword2Code/build/lib/MinSizeRel/Engine.lib")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/lib" TYPE STATIC_LIBRARY FILES "D:/Sword2Code/build/lib/RelWithDebInfo/Engine.lib")
  endif()
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/include/Engine" TYPE FILE FILES
    "D:/Sword2Code/Sword2/SourceCode/Engine/Src/KEngine.h"
    "D:/Sword2Code/Sword2/SourceCode/Engine/Src/KWin32.h"
    "D:/Sword2Code/Sword2/SourceCode/Engine/Src/KWin32App.h"
    "D:/Sword2Code/Sword2/SourceCode/Engine/Src/KWin32Wnd.h"
    "D:/Sword2Code/Sword2/SourceCode/Engine/Src/KCanvas.h"
    "D:/Sword2Code/Sword2/SourceCode/Engine/Src/KDDraw.h"
    "D:/Sword2Code/Sword2/SourceCode/Engine/Src/KBitmap.h"
    "D:/Sword2Code/Sword2/SourceCode/Engine/Src/KBitmap16.h"
    "D:/Sword2Code/Sword2/SourceCode/Engine/Src/KSprite.h"
    "D:/Sword2Code/Sword2/SourceCode/Engine/Src/KDrawBase.h"
    "D:/Sword2Code/Sword2/SourceCode/Engine/Src/KDrawSprite.h"
    "D:/Sword2Code/Sword2/SourceCode/Engine/Src/KDSound.h"
    "D:/Sword2Code/Sword2/SourceCode/Engine/Src/KMusic.h"
    "D:/Sword2Code/Sword2/SourceCode/Engine/Src/KWavSound.h"
    "D:/Sword2Code/Sword2/SourceCode/Engine/Src/KMp3Music.h"
    "D:/Sword2Code/Sword2/SourceCode/Engine/Src/KDInput.h"
    "D:/Sword2Code/Sword2/SourceCode/Engine/Src/KKeyboard.h"
    "D:/Sword2Code/Sword2/SourceCode/Engine/Src/KMouse.h"
    "D:/Sword2Code/Sword2/SourceCode/Engine/Src/KFile.h"
    "D:/Sword2Code/Sword2/SourceCode/Engine/Src/KFilePath.h"
    "D:/Sword2Code/Sword2/SourceCode/Engine/Src/KPakFile.h"
    "D:/Sword2Code/Sword2/SourceCode/Engine/Src/KMemBase.h"
    "D:/Sword2Code/Sword2/SourceCode/Engine/Src/KList.h"
    "D:/Sword2Code/Sword2/SourceCode/Engine/Src/KTimer.h"
    "D:/Sword2Code/Sword2/SourceCode/Engine/Src/KRandom.h"
    "D:/Sword2Code/Sword2/SourceCode/Engine/Src/KDebug.h"
    "D:/Sword2Code/Sword2/SourceCode/Engine/Src/KIniFile.h"
    )
endif()

string(REPLACE ";" "\n" CMAKE_INSTALL_MANIFEST_CONTENT
       "${CMAKE_INSTALL_MANIFEST_FILES}")
if(CMAKE_INSTALL_LOCAL_ONLY)
  file(WRITE "D:/Sword2Code/build/Sword2/SourceCode/Engine/install_local_manifest.txt"
     "${CMAKE_INSTALL_MANIFEST_CONTENT}")
endif()
