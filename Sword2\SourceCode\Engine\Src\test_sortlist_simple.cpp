// Simple sorted list test - verify basic sorted list operations
#include "KWin32.h"
#include <map>
#include <vector>
#include <algorithm>
#include <stdio.h>
#include <stdarg.h>

// Simple debug implementation for testing
void g_DebugLog(const char* fmt, ...) {
    char buffer[1024];
    va_list va;
    
    va_start(va, fmt);
    vsnprintf(buffer, sizeof(buffer), fmt, va);
    va_end(va);
    
    printf("[DEBUG] %s\n", buffer);
}

// Simple sorted list item
template<typename T>
struct SimpleSortItem {
    DWORD hashCode;
    T data;
    
    SimpleSortItem() : hashCode(0) {}
    SimpleSortItem(DWORD hash, const T& item) : hashCode(hash), data(item) {}
    
    bool operator<(const SimpleSortItem& other) const {
        return hashCode < other.hashCode;
    }
    
    bool operator==(const SimpleSortItem& other) const {
        return hashCode == other.hashCode;
    }
};

// Simple sorted list class
template<typename T>
class SimpleSortList {
private:
    std::map<DWORD, T> m_sortedMap;
    
public:
    SimpleSortList() {}
    ~SimpleSortList() { Clear(); }
    
    bool Insert(DWORD hashCode, const T& item) {
        m_sortedMap[hashCode] = item;
        return true;
    }
    
    bool Find(DWORD hashCode, T& result) const {
        typename std::map<DWORD, T>::const_iterator it = m_sortedMap.find(hashCode);
        if (it != m_sortedMap.end()) {
            result = it->second;
            return true;
        }
        return false;
    }
    
    bool Contains(DWORD hashCode) const {
        return m_sortedMap.find(hashCode) != m_sortedMap.end();
    }
    
    bool Remove(DWORD hashCode) {
        typename std::map<DWORD, T>::iterator it = m_sortedMap.find(hashCode);
        if (it != m_sortedMap.end()) {
            m_sortedMap.erase(it);
            return true;
        }
        return false;
    }
    
    void Clear() {
        m_sortedMap.clear();
    }
    
    size_t Size() const {
        return m_sortedMap.size();
    }
    
    bool Empty() const {
        return m_sortedMap.empty();
    }
    
    std::vector<DWORD> GetHashCodes() const {
        std::vector<DWORD> hashCodes;
        for (typename std::map<DWORD, T>::const_iterator it = m_sortedMap.begin(); 
             it != m_sortedMap.end(); ++it) {
            hashCodes.push_back(it->first);
        }
        return hashCodes;
    }
    
    std::vector<T> GetValues() const {
        std::vector<T> values;
        for (typename std::map<DWORD, T>::const_iterator it = m_sortedMap.begin(); 
             it != m_sortedMap.end(); ++it) {
            values.push_back(it->second);
        }
        return values;
    }
    
    std::vector<SimpleSortItem<T> > GetAllItems() const {
        std::vector<SimpleSortItem<T> > items;
        for (typename std::map<DWORD, T>::const_iterator it = m_sortedMap.begin(); 
             it != m_sortedMap.end(); ++it) {
            items.push_back(SimpleSortItem<T>(it->first, it->second));
        }
        return items;
    }
    
    std::vector<SimpleSortItem<T> > GetRange(DWORD minHash, DWORD maxHash) const {
        std::vector<SimpleSortItem<T> > result;
        
        typename std::map<DWORD, T>::const_iterator lower = m_sortedMap.lower_bound(minHash);
        typename std::map<DWORD, T>::const_iterator upper = m_sortedMap.upper_bound(maxHash);
        
        for (typename std::map<DWORD, T>::const_iterator it = lower; it != upper; ++it) {
            result.push_back(SimpleSortItem<T>(it->first, it->second));
        }
        
        return result;
    }
    
    DWORD GetMinHash() const {
        if (Empty()) return 0;
        return m_sortedMap.begin()->first;
    }
    
    DWORD GetMaxHash() const {
        if (Empty()) return 0;
        typename std::map<DWORD, T>::const_iterator it = m_sortedMap.end();
        --it;
        return it->first;
    }
    
    void DebugPrint() const {
        g_DebugLog("SimpleSortList: %d items", (int)Size());
        
        int count = 0;
        for (typename std::map<DWORD, T>::const_iterator it = m_sortedMap.begin(); 
             it != m_sortedMap.end() && count < 10; ++it, ++count) {
            g_DebugLog("  [%d] Hash: %u", count, it->first);
        }
        
        if (Size() > 10) {
            g_DebugLog("  ... and %d more items", (int)Size() - 10);
        }
    }
};

// Test simple sorted list functionality
ENGINE_API int TestSimpleSortList() {
    g_DebugLog("Starting simple sorted list test...");
    
    // Test 1: Basic operations
    g_DebugLog("=== Test 1: Basic Operations ===");
    
    SimpleSortList<int> sortList;
    
    // Test insertion
    if (!sortList.Insert(100, 10)) {
        g_DebugLog("ERROR: Insert failed");
        return -1;
    }
    
    if (!sortList.Insert(50, 5)) {
        g_DebugLog("ERROR: Insert failed");
        return -2;
    }
    
    if (!sortList.Insert(200, 20)) {
        g_DebugLog("ERROR: Insert failed");
        return -3;
    }
    
    if (!sortList.Insert(75, 7)) {
        g_DebugLog("ERROR: Insert failed");
        return -4;
    }
    
    g_DebugLog("Insertion passed: %d items", (int)sortList.Size());
    
    // Test finding
    int result;
    if (!sortList.Find(100, result) || result != 10) {
        g_DebugLog("ERROR: Find failed for hash 100");
        return -5;
    }
    
    if (!sortList.Find(50, result) || result != 5) {
        g_DebugLog("ERROR: Find failed for hash 50");
        return -6;
    }
    
    if (sortList.Find(999, result)) {
        g_DebugLog("ERROR: Find should have failed for hash 999");
        return -7;
    }
    
    g_DebugLog("Find operations passed");
    
    // Test contains
    if (!sortList.Contains(100)) {
        g_DebugLog("ERROR: Contains failed for existing hash");
        return -8;
    }
    
    if (sortList.Contains(999)) {
        g_DebugLog("ERROR: Contains should have failed for non-existing hash");
        return -9;
    }
    
    g_DebugLog("Contains operations passed");
    
    // Test 2: Sorted order
    g_DebugLog("=== Test 2: Sorted Order ===");
    
    std::vector<DWORD> hashCodes = sortList.GetHashCodes();
    if (hashCodes.size() != 4) {
        g_DebugLog("ERROR: Expected 4 hash codes, got %d", (int)hashCodes.size());
        return -10;
    }
    
    // Check if sorted
    if (hashCodes[0] != 50 || hashCodes[1] != 75 || hashCodes[2] != 100 || hashCodes[3] != 200) {
        g_DebugLog("ERROR: Hash codes not in sorted order");
        g_DebugLog("  Got: %u, %u, %u, %u", hashCodes[0], hashCodes[1], hashCodes[2], hashCodes[3]);
        return -11;
    }
    
    g_DebugLog("Sorted order passed");
    
    // Test 3: Range queries
    g_DebugLog("=== Test 3: Range Queries ===");
    
    std::vector<SimpleSortItem<int> > rangeItems = sortList.GetRange(60, 150);
    if (rangeItems.size() != 2) {
        g_DebugLog("ERROR: Expected 2 items in range [60, 150], got %d", (int)rangeItems.size());
        return -12;
    }
    
    if (rangeItems[0].hashCode != 75 || rangeItems[1].hashCode != 100) {
        g_DebugLog("ERROR: Range query returned wrong items");
        return -13;
    }
    
    g_DebugLog("Range queries passed");
    
    // Test 4: Min/Max operations
    g_DebugLog("=== Test 4: Min/Max Operations ===");
    
    DWORD minHash = sortList.GetMinHash();
    DWORD maxHash = sortList.GetMaxHash();
    
    if (minHash != 50) {
        g_DebugLog("ERROR: Min hash should be 50, got %u", minHash);
        return -14;
    }
    
    if (maxHash != 200) {
        g_DebugLog("ERROR: Max hash should be 200, got %u", maxHash);
        return -15;
    }
    
    g_DebugLog("Min/Max operations passed");
    
    // Test 5: Removal
    g_DebugLog("=== Test 5: Removal ===");
    
    if (!sortList.Remove(75)) {
        g_DebugLog("ERROR: Remove failed for existing hash");
        return -16;
    }
    
    if (sortList.Remove(999)) {
        g_DebugLog("ERROR: Remove should have failed for non-existing hash");
        return -17;
    }
    
    if (sortList.Size() != 3) {
        g_DebugLog("ERROR: Size should be 3 after removal, got %d", (int)sortList.Size());
        return -18;
    }
    
    if (sortList.Contains(75)) {
        g_DebugLog("ERROR: Item should not exist after removal");
        return -19;
    }
    
    g_DebugLog("Removal operations passed");
    
    // Test 6: Clear
    g_DebugLog("=== Test 6: Clear ===");
    
    sortList.Clear();
    
    if (!sortList.Empty()) {
        g_DebugLog("ERROR: List should be empty after clear");
        return -20;
    }
    
    if (sortList.Size() != 0) {
        g_DebugLog("ERROR: Size should be 0 after clear");
        return -21;
    }
    
    g_DebugLog("Clear operations passed");
    
    g_DebugLog("=== All simple sorted list tests passed! ===");
    return 0;
}

// Test main function
#ifdef _STANDALONE
int main() {
    g_DebugLog("Starting simple sorted list test suite...");
    
    int result = TestSimpleSortList();
    
    if (result == 0) {
        printf("SUCCESS: All simple sorted list tests passed!\n");
    } else {
        printf("FAILED: Simple sorted list test failed with code %d\n", result);
    }
    
    return result;
}
#endif
