# GameSys库 CMakeLists.txt
# 游戏系统库

cmake_minimum_required(VERSION 3.16)

# 设置项目名称
set(GAMESYS_LIB_NAME GameSys)

# 收集所有子目录
set(GAMESYS_SUBDIRS
    NameAddressedData
)

# 创建游戏系统库目标
add_library(${GAMESYS_LIB_NAME} STATIC)

# 收集所有源文件
set(GAMESYS_SOURCES "")
set(GAMESYS_HEADERS "")

# 收集当前目录的源文件
file(GLOB CURRENT_SOURCES "${CMAKE_CURRENT_SOURCE_DIR}/*.cpp")
file(GLOB CURRENT_C_SOURCES "${CMAKE_CURRENT_SOURCE_DIR}/*.c")
file(GLOB CURRENT_HEADERS "${CMAKE_CURRENT_SOURCE_DIR}/*.h")

list(APPEND GAMESYS_SOURCES ${CURRENT_SOURCES} ${CURRENT_C_SOURCES})
list(APPEND GAMESYS_HEADERS ${CURRENT_HEADERS})

# 遍历每个子目录，收集源文件
foreach(SUBDIR ${GAMESYS_SUBDIRS})
    if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/${SUBDIR})
        # 收集.cpp文件
        file(GLOB SUBDIR_SOURCES "${CMAKE_CURRENT_SOURCE_DIR}/${SUBDIR}/*.cpp")
        file(GLOB SUBDIR_C_SOURCES "${CMAKE_CURRENT_SOURCE_DIR}/${SUBDIR}/*.c")
        
        # 收集.h文件
        file(GLOB SUBDIR_HEADERS "${CMAKE_CURRENT_SOURCE_DIR}/${SUBDIR}/*.h")
        
        # 添加到总列表
        list(APPEND GAMESYS_SOURCES ${SUBDIR_SOURCES} ${SUBDIR_C_SOURCES})
        list(APPEND GAMESYS_HEADERS ${SUBDIR_HEADERS})
        
        # 添加包含目录
        target_include_directories(${GAMESYS_LIB_NAME} PUBLIC ${CMAKE_CURRENT_SOURCE_DIR}/${SUBDIR})
        
        message(STATUS "GameSys: Added ${SUBDIR}")
    else()
        message(STATUS "GameSys: Skipping missing directory ${SUBDIR}")
    endif()
endforeach()

# 如果有源文件，添加到目标
if(GAMESYS_SOURCES)
    target_sources(${GAMESYS_LIB_NAME} PRIVATE ${GAMESYS_SOURCES} ${GAMESYS_HEADERS})
    message(STATUS "GameSys: Found sources")
else()
    # 如果没有源文件，创建一个空的源文件
    set(EMPTY_SOURCE_FILE "${CMAKE_CURRENT_BINARY_DIR}/empty_gamesys.cpp")
    file(WRITE ${EMPTY_SOURCE_FILE} "// Empty source file for GameSys library\n")
    target_sources(${GAMESYS_LIB_NAME} PRIVATE ${EMPTY_SOURCE_FILE})
    message(STATUS "GameSys: No sources found, created empty library")
endif()

# 设置包含目录
target_include_directories(${GAMESYS_LIB_NAME} PUBLIC 
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_SOURCE_DIR}/Sword2/SourceCode/Include
    ${CMAKE_SOURCE_DIR}/Sword2/SourceCode/Engine/Include
)

# 链接依赖库
target_link_libraries(${GAMESYS_LIB_NAME} PUBLIC
    Core
    Engine
)

# 设置编译属性
set_target_properties(${GAMESYS_LIB_NAME} PROPERTIES
    OUTPUT_NAME "GameSys"
    ARCHIVE_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib"
)

# 平台特定设置
if(WIN32)
    target_compile_definitions(${GAMESYS_LIB_NAME} PRIVATE
        WIN32
        _WINDOWS
        _LIB
    )
endif()

message(STATUS "GameSys library configured")
