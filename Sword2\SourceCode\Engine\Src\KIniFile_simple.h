//---------------------------------------------------------------------------
// Sword3 Engine - Simple Configuration File System
// 简化的配置文件系统 - 避免复杂的C++11语法
//---------------------------------------------------------------------------
#ifndef KIniFile_Simple_H
#define KIniFile_Simple_H

#include "KWin32.h"
#include <string>
#include <map>
#include <vector>

//---------------------------------------------------------------------------
// 简化的配置文件类
//---------------------------------------------------------------------------
#ifndef __linux
class ENGINE_API KIniFile
#else
class KIniFile
#endif
{
public:
    // 简化的类型定义
    typedef std::map<std::string, std::string> KeyValueMap;
    typedef std::map<std::string, KeyValueMap> SectionMap;
    
private:
    SectionMap m_sections;          // 所有节和键值对
    std::string m_filename;         // 当前文件名
    bool m_modified;                // 是否已修改
    
public:
    // 构造函数和析构函数
    KIniFile();
    explicit KIniFile(const std::string& filename);
    ~KIniFile();
    
    // 兼容性接口（保持与原有代码兼容）
    BOOL Load(LPCSTR FileName);
    BOOL Save(LPCSTR FileName);
    void Clear();
    
    BOOL IsSectionExist(LPCSTR lpSection);
    void EraseSection(LPCSTR lpSection);
    void EraseKey(LPCSTR lpSection, LPCSTR lpKey);
    
    BOOL GetString(LPCSTR lpSection, LPCSTR lpKeyName, LPCSTR lpDefault, 
                   LPSTR lpRString, DWORD dwSize);
    BOOL GetInteger(LPCSTR lpSection, LPCSTR lpKeyName, int nDefault, int* pnValue);
    BOOL GetFloat(LPCSTR lpSection, LPCSTR lpKeyName, float fDefault, float* pfValue);
    void GetBool(LPCSTR lpSection, LPCSTR lpKeyName, BOOL* pBool);
    
    void WriteString(LPCSTR lpSection, LPCSTR lpKeyName, LPCSTR lpString);
    void WriteInteger(LPCSTR lpSection, LPCSTR lpKeyName, int Value);
    void WriteFloat(LPCSTR lpSection, LPCSTR lpKeyName, float fValue);
    void WriteBool(LPCSTR lpSection, LPCSTR lpKeyName, BOOL bValue);
    
    // 扩展的兼容性接口
    void GetInteger2(LPCSTR lpSection, LPCSTR lpKeyName, int* pnValue1, int* pnValue2);
    void GetFloat2(LPCSTR lpSection, LPCSTR lpKeyName, float* pfValue1, float* pfValue2);
    void GetFloat3(LPCSTR lpSection, LPCSTR lpKeyName, float* pFloat);
    void GetFloat4(LPCSTR lpSection, LPCSTR lpKeyName, float* pRect);
    void GetRect(LPCSTR lpSection, LPCSTR lpKeyName, RECT* pRect);
    
    void WriteInteger2(LPCSTR lpSection, LPCSTR lpKeyName, int Value1, int Value2);
    void WriteFloat2(LPCSTR lpSection, LPCSTR lpKeyName, float fValue1, float fValue2);
    
    BOOL GetNextSection(LPCSTR pSection, LPSTR pNextSection);
    BOOL GetNextKey(LPCSTR pSection, LPCSTR pKey, LPSTR pNextKey);
    int GetSectionCount();
    
    // 现代化接口
    bool LoadFromFile(const std::string& filename);
    bool SaveToFile(const std::string& filename = "");
    
    // 节操作
    bool HasSection(const std::string& section) const;
    bool CreateSection(const std::string& section);
    bool RemoveSection(const std::string& section);
    std::vector<std::string> GetSectionNames() const;
    
    // 键操作
    bool HasKey(const std::string& section, const std::string& key) const;
    bool RemoveKey(const std::string& section, const std::string& key);
    std::vector<std::string> GetKeyNames(const std::string& section) const;
    
    // 类型安全的读取接口
    std::string GetString(const std::string& section, const std::string& key, 
                         const std::string& defaultValue = "") const;
    int GetInt(const std::string& section, const std::string& key, int defaultValue = 0) const;
    float GetFloat(const std::string& section, const std::string& key, float defaultValue = 0.0f) const;
    bool GetBool(const std::string& section, const std::string& key, bool defaultValue = false) const;
    
    // 向量和数组读取
    std::vector<int> GetIntArray(const std::string& section, const std::string& key, 
                                char delimiter = ',') const;
    std::vector<float> GetFloatArray(const std::string& section, const std::string& key, 
                                    char delimiter = ',') const;
    std::vector<std::string> GetStringArray(const std::string& section, const std::string& key, 
                                           char delimiter = ',') const;
    
    // 类型安全的写入接口
    void SetString(const std::string& section, const std::string& key, const std::string& value);
    void SetInt(const std::string& section, const std::string& key, int value);
    void SetFloat(const std::string& section, const std::string& key, float value);
    void SetBool(const std::string& section, const std::string& key, bool value);
    
    // 向量和数组写入
    void SetIntArray(const std::string& section, const std::string& key, 
                    const std::vector<int>& values, char delimiter = ',');
    void SetFloatArray(const std::string& section, const std::string& key, 
                      const std::vector<float>& values, char delimiter = ',');
    void SetStringArray(const std::string& section, const std::string& key, 
                       const std::vector<std::string>& values, char delimiter = ',');
    
    // 实用功能
    bool IsModified() const { return m_modified; }
    void SetModified(bool modified = true) { m_modified = modified; }
    const std::string& GetFilename() const { return m_filename; }
    
    // 调试和输出
    void DebugPrint() const;
    std::string ToString() const;
    
    // 导入导出
    bool LoadFromString(const std::string& content);
    std::string SaveToString() const;
    
private:
    // 内部辅助函数
    std::string NormalizeSection(const std::string& section) const;
    std::string NormalizeKey(const std::string& key) const;
    bool ParseLine(const std::string& line, std::string& currentSection);
    std::string EscapeValue(const std::string& value) const;
    std::string UnescapeValue(const std::string& value) const;
    void EnsureSection(const std::string& section);
    
    // 字符串操作辅助函数
    std::string Trim(const std::string& str) const;
    std::string ToLower(const std::string& str) const;
    int ToInt(const std::string& str, int defaultValue = 0) const;
    float ToFloat(const std::string& str, float defaultValue = 0.0f) const;
    bool ToBool(const std::string& str, bool defaultValue = false) const;
    std::string ToString(int value) const;
    std::string ToString(float value) const;
    std::string ToString(bool value) const;
    std::vector<std::string> Split(const std::string& str, char delimiter) const;
    std::string Join(const std::vector<int>& values, char delimiter) const;
    std::string Join(const std::vector<float>& values, char delimiter) const;
    std::string Join(const std::vector<std::string>& values, char delimiter) const;
};

//---------------------------------------------------------------------------
#endif // KIniFile_Simple_H
