﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{AC75C354-5955-3182-88BD-DA62DDD7760A}"
	ProjectSection(ProjectDependencies) = postProject
		{CFD13F0C-2493-3B4A-82F5-55C7C73BF55A} = {CFD13F0C-2493-3B4A-82F5-55C7C73BF55A}
		{07393D3B-33CE-3CB7-8514-34195DF74393} = {07393D3B-33CE-3CB7-8514-34195DF74393}
		{9964A574-EC5B-3E2E-9EC4-5382C57643AF} = {9964A574-EC5B-3E2E-9EC4-5382C57643AF}
		{1946B23A-828A-3EB6-83E5-5A3787A50438} = {1946B23A-828A-3EB6-83E5-5A3787A50438}
		{F49DE8A8-3416-3614-873F-9DFEDB82E16F} = {F49DE8A8-3416-3614-873F-9DFEDB82E16F}
		{A87FAD10-EA99-3DB1-8F9E-44636C9885D9} = {A87FAD10-EA99-3DB1-8F9E-44636C9885D9}
		{C9F953A3-9235-3E02-90C6-F745942FD865} = {C9F953A3-9235-3E02-90C6-F745942FD865}
		{ED90C747-225A-39C5-B974-E2A06ECA179E} = {ED90C747-225A-39C5-B974-E2A06ECA179E}
		{17FBC3FB-A110-38C7-97F5-D3741D7475A7} = {17FBC3FB-A110-38C7-97F5-D3741D7475A7}
		{E5089369-255E-3420-AD09-104F2959B72A} = {E5089369-255E-3420-AD09-104F2959B72A}
		{C0A6828F-D7DF-3084-91A2-DFF8343D5737} = {C0A6828F-D7DF-3084-91A2-DFF8343D5737}
		{5056683F-7A79-357B-A3C6-E0F657A7870C} = {5056683F-7A79-357B-A3C6-E0F657A7870C}
		{19AFDCC1-1922-3D21-9362-875095820843} = {19AFDCC1-1922-3D21-9362-875095820843}
		{C39EB322-2EC7-34D8-BCED-D36A59041D19} = {C39EB322-2EC7-34D8-BCED-D36A59041D19}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Core", "Sword2\SourceCode\Common\Core\Core.vcxproj", "{CFD13F0C-2493-3B4A-82F5-55C7C73BF55A}"
	ProjectSection(ProjectDependencies) = postProject
		{1946B23A-828A-3EB6-83E5-5A3787A50438} = {1946B23A-828A-3EB6-83E5-5A3787A50438}
		{C9F953A3-9235-3E02-90C6-F745942FD865} = {C9F953A3-9235-3E02-90C6-F745942FD865}
		{5056683F-7A79-357B-A3C6-E0F657A7870C} = {5056683F-7A79-357B-A3C6-E0F657A7870C}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Dawn", "Sword2\SourceCode\Client\Dawn\Dawn.vcxproj", "{07393D3B-33CE-3CB7-8514-34195DF74393}"
	ProjectSection(ProjectDependencies) = postProject
		{CFD13F0C-2493-3B4A-82F5-55C7C73BF55A} = {CFD13F0C-2493-3B4A-82F5-55C7C73BF55A}
		{1946B23A-828A-3EB6-83E5-5A3787A50438} = {1946B23A-828A-3EB6-83E5-5A3787A50438}
		{C9F953A3-9235-3E02-90C6-F745942FD865} = {C9F953A3-9235-3E02-90C6-F745942FD865}
		{C0A6828F-D7DF-3084-91A2-DFF8343D5737} = {C0A6828F-D7DF-3084-91A2-DFF8343D5737}
		{5056683F-7A79-357B-A3C6-E0F657A7870C} = {5056683F-7A79-357B-A3C6-E0F657A7870C}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "DawnClient", "Sword2\SourceCode\Client\DawnClient\DawnClient.vcxproj", "{9964A574-EC5B-3E2E-9EC4-5382C57643AF}"
	ProjectSection(ProjectDependencies) = postProject
		{5056683F-7A79-357B-A3C6-E0F657A7870C} = {5056683F-7A79-357B-A3C6-E0F657A7870C}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Engine", "Sword2\SourceCode\Engine\Engine.vcxproj", "{1946B23A-828A-3EB6-83E5-5A3787A50438}"
	ProjectSection(ProjectDependencies) = postProject
		{5056683F-7A79-357B-A3C6-E0F657A7870C} = {5056683F-7A79-357B-A3C6-E0F657A7870C}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "GameServer", "Sword2\SourceCode\Server\GameServer\GameServer.vcxproj", "{F49DE8A8-3416-3614-873F-9DFEDB82E16F}"
	ProjectSection(ProjectDependencies) = postProject
		{CFD13F0C-2493-3B4A-82F5-55C7C73BF55A} = {CFD13F0C-2493-3B4A-82F5-55C7C73BF55A}
		{1946B23A-828A-3EB6-83E5-5A3787A50438} = {1946B23A-828A-3EB6-83E5-5A3787A50438}
		{C9F953A3-9235-3E02-90C6-F745942FD865} = {C9F953A3-9235-3E02-90C6-F745942FD865}
		{5056683F-7A79-357B-A3C6-E0F657A7870C} = {5056683F-7A79-357B-A3C6-E0F657A7870C}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "GameSys", "Sword2\SourceCode\Common\GameSys\GameSys.vcxproj", "{A87FAD10-EA99-3DB1-8F9E-44636C9885D9}"
	ProjectSection(ProjectDependencies) = postProject
		{CFD13F0C-2493-3B4A-82F5-55C7C73BF55A} = {CFD13F0C-2493-3B4A-82F5-55C7C73BF55A}
		{1946B23A-828A-3EB6-83E5-5A3787A50438} = {1946B23A-828A-3EB6-83E5-5A3787A50438}
		{C9F953A3-9235-3E02-90C6-F745942FD865} = {C9F953A3-9235-3E02-90C6-F745942FD865}
		{5056683F-7A79-357B-A3C6-E0F657A7870C} = {5056683F-7A79-357B-A3C6-E0F657A7870C}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{E3EB63EF-DA38-38D7-83A1-6302B73E49B3}"
	ProjectSection(ProjectDependencies) = postProject
		{AC75C354-5955-3182-88BD-DA62DDD7760A} = {AC75C354-5955-3182-88BD-DA62DDD7760A}
		{5056683F-7A79-357B-A3C6-E0F657A7870C} = {5056683F-7A79-357B-A3C6-E0F657A7870C}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Lua5", "DevEnv\Lua5\Lua5.vcxproj", "{C9F953A3-9235-3E02-90C6-F745942FD865}"
	ProjectSection(ProjectDependencies) = postProject
		{5056683F-7A79-357B-A3C6-E0F657A7870C} = {5056683F-7A79-357B-A3C6-E0F657A7870C}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "PACKAGE", "PACKAGE.vcxproj", "{6C84229A-5D56-3904-BE26-FCE675E5AFDC}"
	ProjectSection(ProjectDependencies) = postProject
		{AC75C354-5955-3182-88BD-DA62DDD7760A} = {AC75C354-5955-3182-88BD-DA62DDD7760A}
		{5056683F-7A79-357B-A3C6-E0F657A7870C} = {5056683F-7A79-357B-A3C6-E0F657A7870C}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Relay", "Sword2\SourceCode\Server\Relay\Relay.vcxproj", "{ED90C747-225A-39C5-B974-E2A06ECA179E}"
	ProjectSection(ProjectDependencies) = postProject
		{CFD13F0C-2493-3B4A-82F5-55C7C73BF55A} = {CFD13F0C-2493-3B4A-82F5-55C7C73BF55A}
		{1946B23A-828A-3EB6-83E5-5A3787A50438} = {1946B23A-828A-3EB6-83E5-5A3787A50438}
		{C9F953A3-9235-3E02-90C6-F745942FD865} = {C9F953A3-9235-3E02-90C6-F745942FD865}
		{5056683F-7A79-357B-A3C6-E0F657A7870C} = {5056683F-7A79-357B-A3C6-E0F657A7870C}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "RelayT", "Sword2\SourceCode\Server\RelayT\RelayT.vcxproj", "{17FBC3FB-A110-38C7-97F5-D3741D7475A7}"
	ProjectSection(ProjectDependencies) = postProject
		{CFD13F0C-2493-3B4A-82F5-55C7C73BF55A} = {CFD13F0C-2493-3B4A-82F5-55C7C73BF55A}
		{1946B23A-828A-3EB6-83E5-5A3787A50438} = {1946B23A-828A-3EB6-83E5-5A3787A50438}
		{C9F953A3-9235-3E02-90C6-F745942FD865} = {C9F953A3-9235-3E02-90C6-F745942FD865}
		{5056683F-7A79-357B-A3C6-E0F657A7870C} = {5056683F-7A79-357B-A3C6-E0F657A7870C}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Stat", "Sword2\SourceCode\Common\Stat\Stat.vcxproj", "{E5089369-255E-3420-AD09-104F2959B72A}"
	ProjectSection(ProjectDependencies) = postProject
		{CFD13F0C-2493-3B4A-82F5-55C7C73BF55A} = {CFD13F0C-2493-3B4A-82F5-55C7C73BF55A}
		{1946B23A-828A-3EB6-83E5-5A3787A50438} = {1946B23A-828A-3EB6-83E5-5A3787A50438}
		{C9F953A3-9235-3E02-90C6-F745942FD865} = {C9F953A3-9235-3E02-90C6-F745942FD865}
		{5056683F-7A79-357B-A3C6-E0F657A7870C} = {5056683F-7A79-357B-A3C6-E0F657A7870C}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Support", "Sword2\SourceCode\Common\Support\Support.vcxproj", "{C0A6828F-D7DF-3084-91A2-DFF8343D5737}"
	ProjectSection(ProjectDependencies) = postProject
		{CFD13F0C-2493-3B4A-82F5-55C7C73BF55A} = {CFD13F0C-2493-3B4A-82F5-55C7C73BF55A}
		{1946B23A-828A-3EB6-83E5-5A3787A50438} = {1946B23A-828A-3EB6-83E5-5A3787A50438}
		{C9F953A3-9235-3E02-90C6-F745942FD865} = {C9F953A3-9235-3E02-90C6-F745942FD865}
		{5056683F-7A79-357B-A3C6-E0F657A7870C} = {5056683F-7A79-357B-A3C6-E0F657A7870C}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{5056683F-7A79-357B-A3C6-E0F657A7870C}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "lua", "DevEnv\Lua5\lua.vcxproj", "{19AFDCC1-1922-3D21-9362-875095820843}"
	ProjectSection(ProjectDependencies) = postProject
		{C9F953A3-9235-3E02-90C6-F745942FD865} = {C9F953A3-9235-3E02-90C6-F745942FD865}
		{5056683F-7A79-357B-A3C6-E0F657A7870C} = {5056683F-7A79-357B-A3C6-E0F657A7870C}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "luac", "DevEnv\Lua5\luac.vcxproj", "{C39EB322-2EC7-34D8-BCED-D36A59041D19}"
	ProjectSection(ProjectDependencies) = postProject
		{C9F953A3-9235-3E02-90C6-F745942FD865} = {C9F953A3-9235-3E02-90C6-F745942FD865}
		{5056683F-7A79-357B-A3C6-E0F657A7870C} = {5056683F-7A79-357B-A3C6-E0F657A7870C}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{AC75C354-5955-3182-88BD-DA62DDD7760A}.Debug|x64.ActiveCfg = Debug|x64
		{AC75C354-5955-3182-88BD-DA62DDD7760A}.Debug|x64.Build.0 = Debug|x64
		{AC75C354-5955-3182-88BD-DA62DDD7760A}.Release|x64.ActiveCfg = Release|x64
		{AC75C354-5955-3182-88BD-DA62DDD7760A}.Release|x64.Build.0 = Release|x64
		{AC75C354-5955-3182-88BD-DA62DDD7760A}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{AC75C354-5955-3182-88BD-DA62DDD7760A}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{AC75C354-5955-3182-88BD-DA62DDD7760A}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{AC75C354-5955-3182-88BD-DA62DDD7760A}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{CFD13F0C-2493-3B4A-82F5-55C7C73BF55A}.Debug|x64.ActiveCfg = Debug|x64
		{CFD13F0C-2493-3B4A-82F5-55C7C73BF55A}.Debug|x64.Build.0 = Debug|x64
		{CFD13F0C-2493-3B4A-82F5-55C7C73BF55A}.Release|x64.ActiveCfg = Release|x64
		{CFD13F0C-2493-3B4A-82F5-55C7C73BF55A}.Release|x64.Build.0 = Release|x64
		{CFD13F0C-2493-3B4A-82F5-55C7C73BF55A}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{CFD13F0C-2493-3B4A-82F5-55C7C73BF55A}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{CFD13F0C-2493-3B4A-82F5-55C7C73BF55A}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{CFD13F0C-2493-3B4A-82F5-55C7C73BF55A}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{07393D3B-33CE-3CB7-8514-34195DF74393}.Debug|x64.ActiveCfg = Debug|x64
		{07393D3B-33CE-3CB7-8514-34195DF74393}.Debug|x64.Build.0 = Debug|x64
		{07393D3B-33CE-3CB7-8514-34195DF74393}.Release|x64.ActiveCfg = Release|x64
		{07393D3B-33CE-3CB7-8514-34195DF74393}.Release|x64.Build.0 = Release|x64
		{07393D3B-33CE-3CB7-8514-34195DF74393}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{07393D3B-33CE-3CB7-8514-34195DF74393}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{07393D3B-33CE-3CB7-8514-34195DF74393}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{07393D3B-33CE-3CB7-8514-34195DF74393}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{9964A574-EC5B-3E2E-9EC4-5382C57643AF}.Debug|x64.ActiveCfg = Debug|x64
		{9964A574-EC5B-3E2E-9EC4-5382C57643AF}.Debug|x64.Build.0 = Debug|x64
		{9964A574-EC5B-3E2E-9EC4-5382C57643AF}.Release|x64.ActiveCfg = Release|x64
		{9964A574-EC5B-3E2E-9EC4-5382C57643AF}.Release|x64.Build.0 = Release|x64
		{9964A574-EC5B-3E2E-9EC4-5382C57643AF}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{9964A574-EC5B-3E2E-9EC4-5382C57643AF}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{9964A574-EC5B-3E2E-9EC4-5382C57643AF}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{9964A574-EC5B-3E2E-9EC4-5382C57643AF}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{1946B23A-828A-3EB6-83E5-5A3787A50438}.Debug|x64.ActiveCfg = Debug|x64
		{1946B23A-828A-3EB6-83E5-5A3787A50438}.Debug|x64.Build.0 = Debug|x64
		{1946B23A-828A-3EB6-83E5-5A3787A50438}.Release|x64.ActiveCfg = Release|x64
		{1946B23A-828A-3EB6-83E5-5A3787A50438}.Release|x64.Build.0 = Release|x64
		{1946B23A-828A-3EB6-83E5-5A3787A50438}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{1946B23A-828A-3EB6-83E5-5A3787A50438}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{1946B23A-828A-3EB6-83E5-5A3787A50438}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{1946B23A-828A-3EB6-83E5-5A3787A50438}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{F49DE8A8-3416-3614-873F-9DFEDB82E16F}.Debug|x64.ActiveCfg = Debug|x64
		{F49DE8A8-3416-3614-873F-9DFEDB82E16F}.Debug|x64.Build.0 = Debug|x64
		{F49DE8A8-3416-3614-873F-9DFEDB82E16F}.Release|x64.ActiveCfg = Release|x64
		{F49DE8A8-3416-3614-873F-9DFEDB82E16F}.Release|x64.Build.0 = Release|x64
		{F49DE8A8-3416-3614-873F-9DFEDB82E16F}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{F49DE8A8-3416-3614-873F-9DFEDB82E16F}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{F49DE8A8-3416-3614-873F-9DFEDB82E16F}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{F49DE8A8-3416-3614-873F-9DFEDB82E16F}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{A87FAD10-EA99-3DB1-8F9E-44636C9885D9}.Debug|x64.ActiveCfg = Debug|x64
		{A87FAD10-EA99-3DB1-8F9E-44636C9885D9}.Debug|x64.Build.0 = Debug|x64
		{A87FAD10-EA99-3DB1-8F9E-44636C9885D9}.Release|x64.ActiveCfg = Release|x64
		{A87FAD10-EA99-3DB1-8F9E-44636C9885D9}.Release|x64.Build.0 = Release|x64
		{A87FAD10-EA99-3DB1-8F9E-44636C9885D9}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{A87FAD10-EA99-3DB1-8F9E-44636C9885D9}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{A87FAD10-EA99-3DB1-8F9E-44636C9885D9}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{A87FAD10-EA99-3DB1-8F9E-44636C9885D9}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{E3EB63EF-DA38-38D7-83A1-6302B73E49B3}.Debug|x64.ActiveCfg = Debug|x64
		{E3EB63EF-DA38-38D7-83A1-6302B73E49B3}.Release|x64.ActiveCfg = Release|x64
		{E3EB63EF-DA38-38D7-83A1-6302B73E49B3}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{E3EB63EF-DA38-38D7-83A1-6302B73E49B3}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{C9F953A3-9235-3E02-90C6-F745942FD865}.Debug|x64.ActiveCfg = Debug|x64
		{C9F953A3-9235-3E02-90C6-F745942FD865}.Debug|x64.Build.0 = Debug|x64
		{C9F953A3-9235-3E02-90C6-F745942FD865}.Release|x64.ActiveCfg = Release|x64
		{C9F953A3-9235-3E02-90C6-F745942FD865}.Release|x64.Build.0 = Release|x64
		{C9F953A3-9235-3E02-90C6-F745942FD865}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{C9F953A3-9235-3E02-90C6-F745942FD865}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{C9F953A3-9235-3E02-90C6-F745942FD865}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{C9F953A3-9235-3E02-90C6-F745942FD865}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{6C84229A-5D56-3904-BE26-FCE675E5AFDC}.Debug|x64.ActiveCfg = Debug|x64
		{6C84229A-5D56-3904-BE26-FCE675E5AFDC}.Release|x64.ActiveCfg = Release|x64
		{6C84229A-5D56-3904-BE26-FCE675E5AFDC}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{6C84229A-5D56-3904-BE26-FCE675E5AFDC}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{ED90C747-225A-39C5-B974-E2A06ECA179E}.Debug|x64.ActiveCfg = Debug|x64
		{ED90C747-225A-39C5-B974-E2A06ECA179E}.Debug|x64.Build.0 = Debug|x64
		{ED90C747-225A-39C5-B974-E2A06ECA179E}.Release|x64.ActiveCfg = Release|x64
		{ED90C747-225A-39C5-B974-E2A06ECA179E}.Release|x64.Build.0 = Release|x64
		{ED90C747-225A-39C5-B974-E2A06ECA179E}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{ED90C747-225A-39C5-B974-E2A06ECA179E}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{ED90C747-225A-39C5-B974-E2A06ECA179E}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{ED90C747-225A-39C5-B974-E2A06ECA179E}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{17FBC3FB-A110-38C7-97F5-D3741D7475A7}.Debug|x64.ActiveCfg = Debug|x64
		{17FBC3FB-A110-38C7-97F5-D3741D7475A7}.Debug|x64.Build.0 = Debug|x64
		{17FBC3FB-A110-38C7-97F5-D3741D7475A7}.Release|x64.ActiveCfg = Release|x64
		{17FBC3FB-A110-38C7-97F5-D3741D7475A7}.Release|x64.Build.0 = Release|x64
		{17FBC3FB-A110-38C7-97F5-D3741D7475A7}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{17FBC3FB-A110-38C7-97F5-D3741D7475A7}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{17FBC3FB-A110-38C7-97F5-D3741D7475A7}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{17FBC3FB-A110-38C7-97F5-D3741D7475A7}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{E5089369-255E-3420-AD09-104F2959B72A}.Debug|x64.ActiveCfg = Debug|x64
		{E5089369-255E-3420-AD09-104F2959B72A}.Debug|x64.Build.0 = Debug|x64
		{E5089369-255E-3420-AD09-104F2959B72A}.Release|x64.ActiveCfg = Release|x64
		{E5089369-255E-3420-AD09-104F2959B72A}.Release|x64.Build.0 = Release|x64
		{E5089369-255E-3420-AD09-104F2959B72A}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{E5089369-255E-3420-AD09-104F2959B72A}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{E5089369-255E-3420-AD09-104F2959B72A}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{E5089369-255E-3420-AD09-104F2959B72A}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{C0A6828F-D7DF-3084-91A2-DFF8343D5737}.Debug|x64.ActiveCfg = Debug|x64
		{C0A6828F-D7DF-3084-91A2-DFF8343D5737}.Debug|x64.Build.0 = Debug|x64
		{C0A6828F-D7DF-3084-91A2-DFF8343D5737}.Release|x64.ActiveCfg = Release|x64
		{C0A6828F-D7DF-3084-91A2-DFF8343D5737}.Release|x64.Build.0 = Release|x64
		{C0A6828F-D7DF-3084-91A2-DFF8343D5737}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{C0A6828F-D7DF-3084-91A2-DFF8343D5737}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{C0A6828F-D7DF-3084-91A2-DFF8343D5737}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{C0A6828F-D7DF-3084-91A2-DFF8343D5737}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{5056683F-7A79-357B-A3C6-E0F657A7870C}.Debug|x64.ActiveCfg = Debug|x64
		{5056683F-7A79-357B-A3C6-E0F657A7870C}.Debug|x64.Build.0 = Debug|x64
		{5056683F-7A79-357B-A3C6-E0F657A7870C}.Release|x64.ActiveCfg = Release|x64
		{5056683F-7A79-357B-A3C6-E0F657A7870C}.Release|x64.Build.0 = Release|x64
		{5056683F-7A79-357B-A3C6-E0F657A7870C}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{5056683F-7A79-357B-A3C6-E0F657A7870C}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{5056683F-7A79-357B-A3C6-E0F657A7870C}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{5056683F-7A79-357B-A3C6-E0F657A7870C}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{19AFDCC1-1922-3D21-9362-875095820843}.Debug|x64.ActiveCfg = Debug|x64
		{19AFDCC1-1922-3D21-9362-875095820843}.Debug|x64.Build.0 = Debug|x64
		{19AFDCC1-1922-3D21-9362-875095820843}.Release|x64.ActiveCfg = Release|x64
		{19AFDCC1-1922-3D21-9362-875095820843}.Release|x64.Build.0 = Release|x64
		{19AFDCC1-1922-3D21-9362-875095820843}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{19AFDCC1-1922-3D21-9362-875095820843}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{19AFDCC1-1922-3D21-9362-875095820843}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{19AFDCC1-1922-3D21-9362-875095820843}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{C39EB322-2EC7-34D8-BCED-D36A59041D19}.Debug|x64.ActiveCfg = Debug|x64
		{C39EB322-2EC7-34D8-BCED-D36A59041D19}.Debug|x64.Build.0 = Debug|x64
		{C39EB322-2EC7-34D8-BCED-D36A59041D19}.Release|x64.ActiveCfg = Release|x64
		{C39EB322-2EC7-34D8-BCED-D36A59041D19}.Release|x64.Build.0 = Release|x64
		{C39EB322-2EC7-34D8-BCED-D36A59041D19}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{C39EB322-2EC7-34D8-BCED-D36A59041D19}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{C39EB322-2EC7-34D8-BCED-D36A59041D19}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{C39EB322-2EC7-34D8-BCED-D36A59041D19}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {896EA7A3-11BC-3C2D-8126-E41C8667B6BF}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
