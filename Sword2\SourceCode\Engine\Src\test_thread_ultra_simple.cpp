// Ultra simple thread system test - verify basic thread operations
#include "KWin32.h"
#include <thread>
#include <mutex>
#include <atomic>
#include <chrono>
#include <vector>
#include <stdio.h>
#include <stdarg.h>

// Simple debug implementation for testing
void g_DebugLog(const char* fmt, ...) {
    char buffer[1024];
    va_list va;
    
    va_start(va, fmt);
    vsnprintf(buffer, sizeof(buffer), fmt, va);
    va_end(va);
    
    printf("[DEBUG] %s\n", buffer);
}

// Global test data
std::mutex g_testMutex;
std::atomic<int> g_sharedCounter(0);
std::vector<int> g_testResults;

// Simple thread function
void SimpleThreadFunction(int threadId) {
    g_DebugLog("Thread %d started", threadId);
    
    for (int i = 0; i < 5; ++i) {
        // Test atomic operations
        int oldValue = g_sharedCounter.fetch_add(1);
        
        // Test mutex protection
        {
            std::lock_guard<std::mutex> lock(g_testMutex);
            g_testResults.push_back(threadId * 100 + i);
        }
        
        // Simulate some work
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
    
    g_DebugLog("Thread %d finished", threadId);
}

// Test ultra simple thread system functionality
ENGINE_API int TestUltraSimpleThreadSystem() {
    g_DebugLog("Starting ultra simple thread system test...");
    
    // Test 1: Basic std::thread operations
    g_DebugLog("=== Test 1: Basic std::thread Operations ===");
    
    std::atomic<bool> lambdaExecuted(false);
    
    std::thread testThread([&lambdaExecuted]() {
        g_DebugLog("Lambda thread started");
        lambdaExecuted.store(true);
        g_DebugLog("Lambda thread finished");
    });
    
    if (!testThread.joinable()) {
        g_DebugLog("ERROR: Thread should be joinable");
        return -1;
    }
    
    testThread.join();
    
    if (!lambdaExecuted.load()) {
        g_DebugLog("ERROR: Lambda should have been executed");
        return -2;
    }
    
    g_DebugLog("Basic thread operations passed");
    
    // Test 2: Multiple threads with synchronization
    g_DebugLog("=== Test 2: Multiple Threads with Synchronization ===");
    
    g_sharedCounter.store(0);
    g_testResults.clear();
    
    const int NUM_THREADS = 3;
    std::vector<std::thread> threads;
    
    // Create and start threads
    for (int i = 0; i < NUM_THREADS; ++i) {
        threads.emplace_back(SimpleThreadFunction, i);
    }
    
    // Wait for all threads to complete
    for (std::thread& thread : threads) {
        thread.join();
    }
    
    // Verify results
    if (g_sharedCounter.load() != NUM_THREADS * 5) {
        g_DebugLog("ERROR: Shared counter mismatch. Expected: %d, Got: %d", 
                   NUM_THREADS * 5, g_sharedCounter.load());
        return -3;
    }
    
    if (g_testResults.size() != NUM_THREADS * 5) {
        g_DebugLog("ERROR: Test results size mismatch. Expected: %d, Got: %d", 
                   NUM_THREADS * 5, (int)g_testResults.size());
        return -4;
    }
    
    g_DebugLog("Multiple threads test passed: counter=%d, results=%d", 
               g_sharedCounter.load(), (int)g_testResults.size());
    
    // Test 3: Mutex operations
    g_DebugLog("=== Test 3: Mutex Operations ===");
    
    std::mutex testMutex;
    std::atomic<int> mutexTestCounter(0);
    
    std::thread mutexThread1([&]() {
        for (int i = 0; i < 10; ++i) {
            std::lock_guard<std::mutex> lock(testMutex);
            int temp = mutexTestCounter.load();
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
            mutexTestCounter.store(temp + 1);
        }
    });
    
    std::thread mutexThread2([&]() {
        for (int i = 0; i < 10; ++i) {
            std::lock_guard<std::mutex> lock(testMutex);
            int temp = mutexTestCounter.load();
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
            mutexTestCounter.store(temp + 1);
        }
    });
    
    mutexThread1.join();
    mutexThread2.join();
    
    if (mutexTestCounter.load() != 20) {
        g_DebugLog("ERROR: Mutex test counter mismatch. Expected: 20, Got: %d", 
                   mutexTestCounter.load());
        return -5;
    }
    
    g_DebugLog("Mutex operations passed: counter=%d", mutexTestCounter.load());
    
    // Test 4: Atomic operations
    g_DebugLog("=== Test 4: Atomic Operations ===");
    
    std::atomic<int> atomicCounter(0);
    const int ATOMIC_INCREMENTS = 1000;
    
    std::thread atomicThread1([&]() {
        for (int i = 0; i < ATOMIC_INCREMENTS; ++i) {
            atomicCounter.fetch_add(1);
        }
    });
    
    std::thread atomicThread2([&]() {
        for (int i = 0; i < ATOMIC_INCREMENTS; ++i) {
            atomicCounter.fetch_add(1);
        }
    });
    
    atomicThread1.join();
    atomicThread2.join();
    
    if (atomicCounter.load() != ATOMIC_INCREMENTS * 2) {
        g_DebugLog("ERROR: Atomic counter mismatch. Expected: %d, Got: %d", 
                   ATOMIC_INCREMENTS * 2, atomicCounter.load());
        return -6;
    }
    
    g_DebugLog("Atomic operations passed: counter=%d", atomicCounter.load());
    
    // Test 5: Thread timing
    g_DebugLog("=== Test 5: Thread Timing ===");
    
    auto startTime = std::chrono::high_resolution_clock::now();
    
    std::thread timingThread([]() {
        std::this_thread::sleep_for(std::chrono::milliseconds(50));
    });
    
    timingThread.join();
    
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    
    if (duration.count() < 40 || duration.count() > 100) {
        g_DebugLog("ERROR: Thread timing unexpected. Duration: %lld ms", duration.count());
        return -7;
    }
    
    g_DebugLog("Thread timing passed: duration=%lld ms", duration.count());
    
    // Test 6: Hardware concurrency
    g_DebugLog("=== Test 6: Hardware Information ===");
    
    unsigned int hwConcurrency = std::thread::hardware_concurrency();
    g_DebugLog("Hardware concurrency: %u threads", hwConcurrency);
    
    if (hwConcurrency == 0) {
        g_DebugLog("WARNING: Hardware concurrency detection failed");
    } else if (hwConcurrency > 64) {
        g_DebugLog("WARNING: Unusually high hardware concurrency: %u", hwConcurrency);
    }
    
    g_DebugLog("Hardware information test passed");
    
    // Test 7: Thread ID
    g_DebugLog("=== Test 7: Thread ID ===");
    
    std::thread::id mainThreadId = std::this_thread::get_id();
    std::thread::id workerThreadId;
    
    std::thread idThread([&]() {
        workerThreadId = std::this_thread::get_id();
    });
    
    idThread.join();
    
    if (mainThreadId == workerThreadId) {
        g_DebugLog("ERROR: Thread IDs should be different");
        return -8;
    }
    
    g_DebugLog("Thread ID test passed");
    
    // Test 8: Exception safety
    g_DebugLog("=== Test 8: Exception Safety ===");
    
    std::atomic<bool> exceptionHandled(false);
    
    std::thread exceptionThread([&]() {
        try {
            // Simulate some work that might throw
            for (int i = 0; i < 10; ++i) {
                if (i == 5) {
                    // Don't actually throw, just simulate exception handling
                    exceptionHandled.store(true);
                    break;
                }
                std::this_thread::sleep_for(std::chrono::milliseconds(1));
            }
        } catch (...) {
            exceptionHandled.store(true);
        }
    });
    
    exceptionThread.join();
    
    if (!exceptionHandled.load()) {
        g_DebugLog("ERROR: Exception handling test failed");
        return -9;
    }
    
    g_DebugLog("Exception safety test passed");
    
    g_DebugLog("=== All ultra simple thread system tests passed! ===");
    return 0;
}

// Test main function
#ifdef _STANDALONE
int main() {
    g_DebugLog("Starting ultra simple thread system test suite...");
    
    int result = TestUltraSimpleThreadSystem();
    
    if (result == 0) {
        printf("SUCCESS: All ultra simple thread system tests passed!\n");
    } else {
        printf("FAILED: Ultra simple thread system test failed with code %d\n", result);
    }
    
    return result;
}
#endif
