# Sword3 Engine CMake配置

# 引擎源文件
set(ENGINE_SOURCES
    # 核心文件
    Src/KEngine.cpp
    Src/KWin32.cpp
    Src/KWin32App.cpp
    Src/KWin32Wnd.cpp
    Src/stdafx.cpp
    
    # 图形系统
    Src/KCanvas.cpp
    Src/KDDraw.cpp
    Src/KBitmap.cpp
    Src/KBitmap16.cpp
    Src/KBitmapConvert.cpp
    Src/KSprite.cpp
    Src/KSpriteCache.cpp
    Src/KSpriteCodec.cpp
    Src/KSpriteMaker.cpp
    Src/KPalette.cpp
    Src/KColors.cpp
    
    # 绘制系统
    Src/KDrawBase.cpp
    Src/KDrawBitmap.cpp
    Src/KDrawBitmap16.cpp
    Src/KDrawSprite.cpp
    Src/KDrawSpriteAlpha.cpp
    Src/KDrawFade.cpp
    Src/KDrawFont.cpp
    Src/KDrawDotFont.cpp
    
    # 音频系统
    Src/KDSound.cpp
    Src/KMusic.cpp
    Src/KWavSound.cpp
    Src/KWavMusic.cpp
    Src/KMp3Music.cpp
    Src/KMp4Audio.cpp
    Src/KMpgMusic.cpp
    Src/KWavFile.cpp
    Src/KWavCodec.cpp
    
    # 输入系统
    Src/KDInput.cpp
    Src/KKeyboard.cpp
    Src/KMouse.cpp
    Src/Kime.cpp
    
    # 文件系统
    Src/KFile.cpp
    Src/KFilePath.cpp
    Src/KFileDialog.cpp
    Src/KFileCopy.cpp
    Src/KPakFile.cpp
    Src/KPakData.cpp
    Src/KPakList.cpp
    Src/KPakMake.cpp
    Src/KPakTool.cpp
    Src/KZipFile.cpp
    Src/KZipData.cpp
    Src/KZipList.cpp
    Src/KZipCodec.cpp
    Src/XPackFile.cpp
    Src/ZPackFile.cpp
    Src/ZSPRPackFile.cpp
    
    # 图像文件格式
    Src/KBmpFile.cpp
    Src/KBmpFile24.cpp
    Src/KJpgFile.cpp
    Src/KGifFile.cpp
    Src/KPcxFile.cpp
    Src/KTgaFile32.cpp
    
    # 内存管理
    Src/KMemBase.cpp
    Src/KMemClass.cpp
    Src/KMemClass1.cpp
    Src/KMemManager.cpp
    Src/KMemStack.cpp
    
    # 数据结构
    Src/KList.cpp
    Src/KNode.cpp
    Src/KLinkArray.cpp
    Src/KSafeList.cpp
    Src/KSortList.cpp
    Src/KHashList.cpp
    Src/KHashNode.cpp
    Src/KHashTable.cpp
    Src/KBinTree.cpp
    Src/KSortBinTree.cpp
    Src/KFindBinTree.cpp
    Src/KOctree.cpp
    Src/KOctreeNode.cpp
    
    # 字符串处理
    Src/KStrBase.cpp
    Src/KStrList.cpp
    Src/KStrNode.cpp
    Src/KSG_StringProcess.cpp
    Src/KSG_MD5_String.cpp
    Src/md5.cpp
    
    # 脚本系统
    Src/KScript.cpp
    Src/KScriptCache.cpp
    Src/KScriptList.cpp
    Src/KScriptSet.cpp
    Src/KLuaScript.cpp
    Src/KLuaScriptSet.cpp
    Src/KStepLuaScript.cpp
    Src/KEicScript.cpp
    Src/KEicScriptSet.cpp
    Src/LuaFuns.cpp
    
    # 网络系统
    Src/KNetClient.cpp
    Src/KNetServer.cpp
    Src/KNetServerNode.cpp
    Src/KNetThread.cpp
    
    # 工具类
    Src/KTimer.cpp
    Src/KRandom.cpp
    Src/KDebug.cpp
    Src/KEvent.cpp
    Src/KMessage.cpp
    Src/KMsgNode.cpp
    Src/KIniFile.cpp
    Src/KTabFile.cpp
    Src/KTabFileCtrl.cpp
    Src/KScanDir.cpp
    Src/Text.cpp
    
    # 线程和同步
    Src/KThread.cpp
    Src/KMutex.cpp
    Src/KAutoMutex.cpp
    Src/KCriticalSection.h
    
    # 图形处理
    Src/KGraphics.cpp
    Src/KImageRes.cpp
    Src/KImageStore.cpp
    Src/KFont.cpp
    Src/KBmp2Spr.cpp
    
    # 编解码
    Src/KCodec.cpp
    Src/KCodecLha.cpp
    Src/KCodecLzo.cpp
    
    # 缓存系统
    Src/KCache.cpp
    Src/KSoundCache.cpp
    
    # 视频系统
    Src/KVideo.cpp
    Src/KAviFile.cpp
    Src/KMp4Movie.cpp
    Src/KMp4Video.cpp
    
    # 几何和数学
    Src/KPolygon.cpp
    Src/KPolyRelation.cpp
    
    # 其他
    Src/KDError.cpp
    Src/KLubCmpl_Blocker.cpp
)

# 引擎头文件
set(ENGINE_HEADERS
    Src/KEngine.h
    Src/KWin32.h
    Src/KWin32App.h
    Src/KWin32Wnd.h
    
    # 图形系统头文件
    Src/KCanvas.h
    Src/KDDraw.h
    Src/KBitmap.h
    Src/KBitmap16.h
    Src/KSprite.h
    Src/KDrawBase.h
    Src/KDrawSprite.h
    
    # 音频系统头文件
    Src/KDSound.h
    Src/KMusic.h
    Src/KWavSound.h
    Src/KMp3Music.h
    
    # 输入系统头文件
    Src/KDInput.h
    Src/KKeyboard.h
    Src/KMouse.h
    
    # 文件系统头文件
    Src/KFile.h
    Src/KFilePath.h
    Src/KPakFile.h
    
    # 其他核心头文件
    Src/KMemBase.h
    Src/KList.h
    Src/KTimer.h
    Src/KRandom.h
    Src/KDebug.h
    Src/KIniFile.h
)

# 创建引擎库
add_library(Engine STATIC ${ENGINE_SOURCES} ${ENGINE_HEADERS})

# 设置目标属性
set_target_properties(Engine PROPERTIES
    OUTPUT_NAME "Engine"
    DEBUG_POSTFIX "D"
)

# 包含目录
target_include_directories(Engine PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}/Src
    ${CMAKE_CURRENT_SOURCE_DIR}/Include
    ${CMAKE_SOURCE_DIR}/Sword2/SourceCode/Include
    ${CMAKE_SOURCE_DIR}/DevEnv/Include
)

# 预处理器定义
target_compile_definitions(Engine PRIVATE
    ENGINE_EXPORTS
    $<$<CONFIG:Debug>:_DEBUG>
    $<$<CONFIG:Release>:NDEBUG>
)

if(WIN32)
    target_compile_definitions(Engine PRIVATE
        WIN32
        _WINDOWS
        _CRT_SECURE_NO_WARNINGS
        _WINSOCK_DEPRECATED_NO_WARNINGS
        NOMINMAX
        WIN32_LEAN_AND_MEAN
        _WIN32_WINNT=0x0500
        WINVER=0x0500
        NTDDI_VERSION=0x05000000
        ""
    )

    # 添加编译器选项来解决Windows SDK兼容性问题
    target_compile_options(Engine PRIVATE
        /wd4819  # 禁用编码警告
        /wd4996  # 禁用弃用函数警告
        /wd4267  # 禁用size_t转换警告
        /wd4244  # 禁用类型转换警告
        /wd4311  # 禁用指针截断警告
        /wd4302  # 禁用指针截断警告
        /wd4312  # 禁用指针转换警告
        /wd4838  # 禁用收缩转换警告
        /wd4477  # 禁用printf格式警告
        /wd4313  # 禁用printf参数冲突警告
        /wd4005  # 禁用宏重定义警告
        /wd4602  # 禁用pragma警告
        /wd2146  # 禁用语法错误（临时）
    )

    # 强制包含预编译头文件
    target_compile_options(Engine PRIVATE
        /FI"EnginePrecompiled.h"
    )
    
    # Windows特定的链接库
    target_link_libraries(Engine PRIVATE
        winmm
        ws2_32
    )

    # 如果找到DirectX，链接DirectX库
    if(TARGET DirectX::DirectX)
        target_link_libraries(Engine PRIVATE DirectX::DirectX)
    endif()
else()
    target_compile_definitions(Engine PRIVATE
        __linux
        _STANDALONE
    )
    
    # Linux特定的链接库
    target_link_libraries(Engine PRIVATE
        pthread
        dl
        m
    )
endif()

# 安装配置
install(TARGETS Engine
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)

install(FILES ${ENGINE_HEADERS}
    DESTINATION include/Engine
)

# 显示引擎配置信息
message(STATUS "Engine configuration:")
message(STATUS "  Sources: ${CMAKE_CURRENT_LIST_DIR}")
message(STATUS "  Platform: ${CMAKE_SYSTEM_NAME}")
if(WIN32 AND DIRECTX_FOUND)
    message(STATUS "  DirectX: Found")
else()
    message(STATUS "  DirectX: Not found")
endif()
