//---------------------------------------------------------------------------
// Sword3 Engine - Modernized String System
// 现代化的字符串系统 - 使用std::string和现代C++算法
//---------------------------------------------------------------------------
#ifndef KStrBase_Modern_H
#define KStrBase_Modern_H

#include "KWin32.h"
#include <string>
#include <algorithm>
#include <cctype>
#include <vector>

//---------------------------------------------------------------------------
// 保持原有接口兼容性，内部使用现代std::string实现
//---------------------------------------------------------------------------

// 传统C风格接口（保持兼容性）
ENGINE_API int g_StrLen(LPCSTR lpStr);
ENGINE_API LPSTR g_StrEnd(LPCSTR lpStr);
ENGINE_API void g_StrCpy(LPSTR lpDest, LPCSTR lpSrc);
ENGINE_API void g_StrCpyLen(LPSTR lpDest, LPCSTR lpSrc, int nMaxLen);
ENGINE_API void g_StrCat(LPSTR lpDest, LPCSTR lpSrc);
ENGINE_API void g_StrCatLen(LPSTR lpDest, LPCSTR lpSrc, int nMaxLen);
ENGINE_API BOOL g_StrCmp(LPCSTR lpDest, LPCSTR lpSrc);
ENGINE_API BOOL g_StrCmpLen(LPCSTR lpDest, LPCSTR lpSrc, int nMaxLen);
ENGINE_API void g_StrUpper(LPSTR lpDest);
ENGINE_API void g_StrLower(LPSTR lpDest);
ENGINE_API void g_StrRep(LPSTR lpDest, LPSTR lpSrc, LPSTR lpRep);

//---------------------------------------------------------------------------
// 现代化扩展接口（推荐使用）
//---------------------------------------------------------------------------
namespace ModernString {
    
    // 字符串操作
    std::string ToUpper(const std::string& str);
    std::string ToLower(const std::string& str);
    std::string Trim(const std::string& str);
    std::string TrimLeft(const std::string& str);
    std::string TrimRight(const std::string& str);
    
    // 字符串查找和替换
    std::string Replace(const std::string& str, const std::string& from, const std::string& to);
    std::string ReplaceAll(const std::string& str, const std::string& from, const std::string& to);
    bool Contains(const std::string& str, const std::string& substr);
    bool StartsWith(const std::string& str, const std::string& prefix);
    bool EndsWith(const std::string& str, const std::string& suffix);
    
    // 字符串分割和连接
    std::vector<std::string> Split(const std::string& str, char delimiter);
    std::vector<std::string> Split(const std::string& str, const std::string& delimiter);
    std::string Join(const std::vector<std::string>& strings, const std::string& delimiter);
    
    // 类型转换
    int ToInt(const std::string& str, int defaultValue = 0);
    float ToFloat(const std::string& str, float defaultValue = 0.0f);
    bool ToBool(const std::string& str, bool defaultValue = false);
    std::string ToString(int value);
    std::string ToString(float value);
    std::string ToString(bool value);
    
    // 字符串格式化
    std::string Format(const char* format, ...);
    
    // 编码转换（为将来扩展预留）
    std::string UTF8ToANSI(const std::string& utf8Str);
    std::string ANSIToUTF8(const std::string& ansiStr);
    
    // 安全字符串操作
    void SafeCopy(char* dest, size_t destSize, const std::string& src);
    void SafeCat(char* dest, size_t destSize, const std::string& src);
}

//---------------------------------------------------------------------------
// 现代化字符串类（可选使用）
//---------------------------------------------------------------------------
class ENGINE_API ModernStr {
private:
    std::string m_str;
    
public:
    // 构造函数
    ModernStr() = default;
    ModernStr(const char* str) : m_str(str ? str : "") {}
    ModernStr(const std::string& str) : m_str(str) {}
    ModernStr(const ModernStr& other) = default;
    ModernStr(ModernStr&& other) noexcept = default;
    
    // 赋值操作符
    ModernStr& operator=(const char* str) { m_str = (str ? str : ""); return *this; }
    ModernStr& operator=(const std::string& str) { m_str = str; return *this; }
    ModernStr& operator=(const ModernStr& other) = default;
    ModernStr& operator=(ModernStr&& other) noexcept = default;
    
    // 访问操作
    const char* c_str() const { return m_str.c_str(); }
    const std::string& str() const { return m_str; }
    size_t length() const { return m_str.length(); }
    bool empty() const { return m_str.empty(); }
    
    // 字符串操作
    ModernStr& ToUpper() { m_str = ModernString::ToUpper(m_str); return *this; }
    ModernStr& ToLower() { m_str = ModernString::ToLower(m_str); return *this; }
    ModernStr& Trim() { m_str = ModernString::Trim(m_str); return *this; }
    ModernStr& Replace(const std::string& from, const std::string& to) { 
        m_str = ModernString::ReplaceAll(m_str, from, to); return *this; 
    }
    
    // 查询操作
    bool Contains(const std::string& substr) const { return ModernString::Contains(m_str, substr); }
    bool StartsWith(const std::string& prefix) const { return ModernString::StartsWith(m_str, prefix); }
    bool EndsWith(const std::string& suffix) const { return ModernString::EndsWith(m_str, suffix); }
    
    // 类型转换
    int ToInt(int defaultValue = 0) const { return ModernString::ToInt(m_str, defaultValue); }
    float ToFloat(float defaultValue = 0.0f) const { return ModernString::ToFloat(m_str, defaultValue); }
    bool ToBool(bool defaultValue = false) const { return ModernString::ToBool(m_str, defaultValue); }
    
    // 比较操作符
    bool operator==(const ModernStr& other) const { return m_str == other.m_str; }
    bool operator!=(const ModernStr& other) const { return m_str != other.m_str; }
    bool operator<(const ModernStr& other) const { return m_str < other.m_str; }
    
    // 连接操作符
    ModernStr operator+(const ModernStr& other) const { return ModernStr(m_str + other.m_str); }
    ModernStr& operator+=(const ModernStr& other) { m_str += other.m_str; return *this; }
};

//---------------------------------------------------------------------------
#endif // KStrBase_Modern_H
