//---------------------------------------------------------------------------
// Blade Engine (c) 1999-2000 by Kingsoft
//
// File:	KWin32.h
// Date:	2001.10.10
// Code:	Daphnis
// Desc:	Percompiled header files of Win32 Platform
//---------------------------------------------------------------------------
#ifndef KWin32_H
#define KWin32_H 

#ifdef _STANDALONE
	#define ENGINE_API
	#define _ASSERT(x)
#else
	#ifdef ENGINE_EXPORTS
		#define ENGINE_API __declspec(dllexport)
	#else
		#define ENGINE_API __declspec(dllimport)
	#endif
#endif

#ifndef __linux
	// 解决Windows SDK兼容性问题 - 强制使用Windows 2000兼容性
	#ifndef _WIN32_WINNT
	#define _WIN32_WINNT 0x0500
	#endif

	#ifndef WINVER
	#define WINVER 0x0500
	#endif

	#ifndef WIN32_LEAN_AND_MEAN
	#define WIN32_LEAN_AND_MEAN
	#endif

	#ifndef NOMINMAX
	#define NOMINMAX
	#endif

	// 强制禁用所有SAL注解和新版SDK特性
	#define _USE_DECLSPECS_FOR_SAL 0
	#define _SAL_VERSION 10
	#define _PREFAST_
	#define _SAL1_Source_(Name, args, annotes)
	#define _SAL1_1(Name, args, annotes)
	#define _SAL2_Source_(Name, args, annotes)
	#define _SAL2(Name, args, annotes)

	// 强制定义PVOID64为简单的void*，避免新版SDK的复杂定义
	#define PVOID64 void*

	// 禁用Buffer重写说明符
	#define Buffer
	#define _In_
	#define _Out_
	#define _Inout_
	#define _In_opt_
	#define _Out_opt_
	#define _Inout_opt_
	#define _In_z_
	#define _Out_z_
	#define _Inout_z_
	#define _In_reads_(size)
	#define _Out_writes_(size)
	#define _Inout_updates_(size)
	#define _In_reads_opt_(size)
	#define _Out_writes_opt_(size)
	#define _Inout_updates_opt_(size)
	#define _In_reads_bytes_(size)
	#define _Out_writes_bytes_(size)
	#define _Inout_updates_bytes_(size)
	#define _In_reads_bytes_opt_(size)
	#define _Out_writes_bytes_opt_(size)
	#define _Inout_updates_bytes_opt_(size)
	// 完全避免包含windows.h，手动定义需要的类型
	// 这是解决新版Windows SDK兼容性问题的最彻底方案

	// 基本Windows类型定义
	typedef unsigned long DWORD;
	typedef int BOOL;
	typedef unsigned char BYTE;
	typedef unsigned short WORD;
	typedef float FLOAT;
	typedef FLOAT *PFLOAT;
	typedef BOOL *PBOOL;
	typedef BOOL *LPBOOL;
	typedef BYTE *PBYTE;
	typedef BYTE *LPBYTE;
	typedef int *PINT;
	typedef int *LPINT;
	typedef WORD *PWORD;
	typedef WORD *LPWORD;
	typedef long *LPLONG;
	typedef DWORD *PDWORD;
	typedef DWORD *LPDWORD;
	typedef void *LPVOID;
	typedef const void *LPCVOID;
	typedef int INT;
	typedef unsigned int UINT;
	typedef unsigned int *PUINT;
	typedef char CHAR;
	typedef short SHORT;
	typedef long LONG;
	typedef CHAR *PCHAR;
	typedef CHAR *LPCH, *PCH;
	typedef const CHAR *LPCCH, *PCCH;
	typedef CHAR *NPSTR;
	typedef CHAR *LPSTR, *PSTR;
	typedef const CHAR *LPCSTR, *PCSTR;
	typedef wchar_t WCHAR;
	typedef WCHAR *PWCHAR;
	typedef WCHAR *LPWCH, *PWCH;
	typedef const WCHAR *LPCWCH, *PCWCH;
	typedef WCHAR *NWPSTR;
	typedef WCHAR *LPWSTR, *PWSTR;
	typedef const WCHAR *LPCWSTR, *PCWSTR;

	// 句柄类型
	typedef void *HANDLE;
	typedef HANDLE HWND;
	typedef HANDLE HDC;
	typedef HANDLE HINSTANCE;
	typedef HANDLE HMODULE;
	typedef HANDLE HKEY;
	typedef HANDLE HFILE;

	// 常量定义
	#ifndef TRUE
	#define TRUE 1
	#endif
	#ifndef FALSE
	#define FALSE 0
	#endif
	#ifndef NULL
	#define NULL 0
	#endif

	// 函数调用约定
	#ifndef WINAPI
	#define WINAPI __stdcall
	#endif
	#ifndef CALLBACK
	#define CALLBACK __stdcall
	#endif

	// 如果需要网络功能，包含winsock2
	#ifdef _STANDALONE
		#include <winsock2.h>
	#endif
	#define DIRECTINPUT_VERSION 0x800
//---------------------------------------------------------------------------
//---------------------------------------------------------------------------
	#if _MSC_VER > 1000
	#pragma once
	#endif // _MSC_VER > 1000
        #include <mmsystem.h>
        #include <dsound.h>
        #include <dinput.h>
        #include <ddraw.h>
#else
        #define LONG long
		#define INT int
		#define LPINT int *
        #define HWND unsigned long
        #define HANDLE unsigned long
        #define LPSTR char *
        #define LPTSTR char *
        #define LPCSTR const char *
		#define LPCTSTR const char *
        #define DWORD unsigned long
        #define LPVOID void *
        #define PVOID void *
        #define BOOL int
        #define TRUE 1
        #define FALSE 0
        #define BYTE unsigned char
        #define WORD unsigned short
	#define UINT unsigned int
	#define PBYTE unsigned char *
	#define LPBYTE unsigned char *
        #define LONG long
#define IN
#define OUT
typedef struct tagRECT
{
    LONG    left;
    LONG    top;
    LONG    right;
    LONG    bottom;
} RECT, *LPRECT;

typedef struct tagPOINT
{
    LONG  x;
    LONG  y;
} POINT, *PPOINT;

typedef struct _GUID {          // size is 16
    DWORD Data1;
    WORD   Data2;
    WORD   Data3;
    BYTE  Data4[8];
} GUID;

#define MAX_PATH 300
#define CALLBACK
#include <stdarg.h> 

#define ZeroMemory(x,y) memset(x, 0, y)
#define max(a,b)            (((a) > (b)) ? (a) : (b))
#define min(a,b)            (((a) < (b)) ? (a) : (b))
#define FILE_CURRENT	1
#define FILE_END		2
#define FILE_BEGIN		0

#include <pthread.h>
#define MAKEWORD(a, b)      ((WORD)(((BYTE)(a)) | ((WORD)((BYTE)(b))) << 8))
#define MAKELONG(a, b)      ((LONG)(((WORD)(a)) | ((DWORD)((WORD)(b))) << 16))
#define LOWORD(l)           ((WORD)(l))
#define HIWORD(l)           ((WORD)(((DWORD)(l) >> 16) & 0xFFFF))
#define LOBYTE(w)           ((BYTE)(w))
#define HIBYTE(w)           ((BYTE)(((WORD)(w) >> 8) & 0xFF))

#ifdef WIN32
	#define SUCCEEDED(x)		((x) > 0)
#else
	#define SUCCEEDED(x)		((long)x >= 0)
#endif


#include <unistd.h>
#define __stdcall
#define LPDWORD unsigned long *
#define SOCKET_ERROR -1
#endif
//---------------------------------------------------------------------------
// Insert your headers here
// WIN32_LEAN_AND_MEAN 已在 WindowsCompat.h 中定义
//---------------------------------------------------------------------------
#include <stdio.h>
#include <stdlib.h>


#ifndef ITOA
#define ITOA(NUMBER)  #NUMBER
#endif

#ifndef __TEXT_LINE__
#define __TEXT_LINE__(LINE) ITOA(LINE)
#endif

#ifndef KSG_ATTENTION
#define KSG_ATTENTION(MSG) __FILE__"("__TEXT_LINE__(__LINE__)") : ATTENTION "#MSG
#endif


//---------------------------------------------------------------------------
#endif
