//---------------------------------------------------------------------------
// Sword3 Engine - Modernized Win32 Platform Header
// 现代化的Windows平台头文件
//
// 策略：逐步现代化，使用现代C++标准库替代老的Windows API
//---------------------------------------------------------------------------
#ifndef KWin32_H
#define KWin32_H

// 现代化的API导出定义
#ifdef _STANDALONE
	#define ENGINE_API
	#define _ASSERT(x)
#else
	#ifdef ENGINE_EXPORTS
		#define ENGINE_API __declspec(dllexport)
	#else
		#define ENGINE_API __declspec(dllimport)
	#endif
#endif

// 平台检测
#ifndef __linux
	// === 现代化Windows支持 ===
	// 使用Windows 10兼容性，但避免有问题的新特性
	#ifndef _WIN32_WINNT
	#define _WIN32_WINNT 0x0A00  // Windows 10
	#endif

	#ifndef WINVER
	#define WINVER 0x0A00
	#endif

	// 基本Windows定义
	#ifndef WIN32_LEAN_AND_MEAN
	#define WIN32_LEAN_AND_MEAN
	#endif

	#ifndef NOMINMAX
	#define NOMINMAX
	#endif

	// 禁用有问题的SAL注解
	#define _USE_DECLSPECS_FOR_SAL 0
	#define _SAL_VERSION 10

	// === 现代化策略：阶段1 - 最小Windows支持 ===
	// 暂时避免包含复杂的Windows头文件，手动定义需要的类型

	// 基本Windows类型定义（避免包含windows.h）
	typedef unsigned long DWORD;
	typedef int BOOL;
	typedef unsigned char BYTE;
	typedef unsigned short WORD;
	typedef float FLOAT;
	typedef int INT;
	typedef unsigned int UINT;
	typedef char CHAR;
	typedef short SHORT;
	typedef long LONG;
	typedef void* LPVOID;
	typedef const void* LPCVOID;
	typedef char* LPSTR;
	typedef const char* LPCSTR;
	typedef wchar_t WCHAR;
	typedef wchar_t* LPWSTR;
	typedef const wchar_t* LPCWSTR;

	// 句柄类型
	typedef void* HANDLE;
	typedef HANDLE HWND;
	typedef HANDLE HDC;
	typedef HANDLE HINSTANCE;
	typedef HANDLE HMODULE;

	// 常量定义
	#ifndef TRUE
	#define TRUE 1
	#endif
	#ifndef FALSE
	#define FALSE 0
	#endif
	#ifndef NULL
	#define NULL 0
	#endif

	// 函数调用约定
	#ifndef WINAPI
	#define WINAPI __stdcall
	#endif
	#ifndef CALLBACK
	#define CALLBACK __stdcall
	#endif

	// 基本结构体定义
	typedef struct tagRECT {
		LONG left;
		LONG top;
		LONG right;
		LONG bottom;
	} RECT, *LPRECT;

	typedef struct tagPOINT {
		LONG x;
		LONG y;
	} POINT, *PPOINT;

	// DirectX前向声明（现代化：后续用现代图形API替代）
	struct IDirectDraw;
	struct IDirectDrawSurface;
	struct IDirectSound;
	struct IDirectSoundBuffer;
	struct IDirectInput;
	struct IDirectInputDevice;

	typedef IDirectDraw* LPDIRECTDRAW;
	typedef IDirectDrawSurface* LPDIRECTDRAWSURFACE;
	typedef IDirectSound* LPDIRECTSOUND;
	typedef IDirectSoundBuffer* LPDIRECTSOUNDBUFFER;
	typedef IDirectInput* LPDIRECTINPUT;
	typedef IDirectInputDevice* LPDIRECTINPUTDEVICE;

	#define DIRECTINPUT_VERSION 0x800

#else
	// === Linux平台定义 ===
	#define LONG long
	#define INT int
	#define LPINT int*
	#define HWND unsigned long
	#define HANDLE unsigned long
	#define LPSTR char*
	#define LPTSTR char*
	#define LPCSTR const char*
	#define LPCTSTR const char*
	#define DWORD unsigned long
	#define LPVOID void*
	#define PVOID void*
	#define BOOL int
	#define TRUE 1
	#define FALSE 0
	#define BYTE unsigned char
	#define WORD unsigned short
	#define UINT unsigned int
	#define PBYTE unsigned char*
	#define LPBYTE unsigned char*

	// Linux平台的结构体定义
	typedef struct tagRECT {
		LONG left;
		LONG top;
		LONG right;
		LONG bottom;
	} RECT, *LPRECT;

	typedef struct tagPOINT {
		LONG x;
		LONG y;
	} POINT, *PPOINT;

	// GUID结构体定义
	typedef struct _GUID {
		DWORD Data1;
		WORD Data2;
		WORD Data3;
		BYTE Data4[8];
	} GUID;

	// 实用宏定义
	#define MAKEWORD(a, b) ((WORD)(((BYTE)(a)) | ((WORD)((BYTE)(b))) << 8))
	#define MAKELONG(a, b) ((LONG)(((WORD)(a)) | ((DWORD)((WORD)(b))) << 16))
	#define LOWORD(l) ((WORD)(l))
	#define HIWORD(l) ((WORD)(((DWORD)(l) >> 16) & 0xFFFF))
	#define LOBYTE(w) ((BYTE)(w))
	#define HIBYTE(w) ((BYTE)(((WORD)(w) >> 8) & 0xFF))

	// 文件操作常量
	#define FILE_CURRENT 1
	#define FILE_END 2
	#define FILE_BEGIN 0
	#define MAX_PATH 260

	// 成功判断宏
	#define SUCCEEDED(x) ((LONG)(x) >= 0)

	// 网络相关（Linux平台）
	#include <pthread.h>
	#include <unistd.h>
	#define __stdcall
	#define LPDWORD unsigned long*
	#define SOCKET_ERROR -1
#endif

// === 通用定义（所有平台） ===
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// 内存操作宏
#define ZeroMemory(dest, size) memset(dest, 0, size)

// 数学宏（避免与std::max/min冲突）
#ifndef max
#define max(a,b) (((a) > (b)) ? (a) : (b))
#endif
#ifndef min
#define min(a,b) (((a) < (b)) ? (a) : (b))
#endif

// 调试和注意力宏
#ifndef ITOA
#define ITOA(NUMBER) #NUMBER
#endif

#ifndef __TEXT_LINE__
#define __TEXT_LINE__(LINE) ITOA(LINE)
#endif

#ifndef KSG_ATTENTION
#define KSG_ATTENTION(MSG) __FILE__"("__TEXT_LINE__(__LINE__)") : ATTENTION "#MSG
#endif

//---------------------------------------------------------------------------
#endif // KWin32_H
