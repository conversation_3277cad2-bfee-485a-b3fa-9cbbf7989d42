﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\S3Relay.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\Global.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\StdAfx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\DoScript.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\KG_RelayDatabase.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\ShareDataBase.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\NetConnect.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\NetServer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\HostConnect.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\HostServer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\TongConnect.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\TongServer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\ChatConnect.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\ChatServer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\ChannelMgr.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\PlayerRelationMgr.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\OfflineMsgStore.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\RoleProcess.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\Test.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\MapArrangeCentre.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\KGlobalValues.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\TaskCentre.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\KEconomyCentre.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\KIBInfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\KCustomData.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\KCustomRankData.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\Frame\RelayConfig.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\MasterAndPrentice.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\KRenameDaemon.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\MissionEx\kvariant.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\MissionEx\MissionEx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\MissionEx\MissionExManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\ScriptFuncComm.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\Outside\GmcConnection.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\Outside\GmcMsgProcessor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\Outside\BishopConnection.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\Outside\BishopMsgProcessor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\Outside\RoleDbConnection.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongCenter.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongDuty.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongMember.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongProcessor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongSync.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongTeam.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongTechControl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongTechRes.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongMagicAttribute.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\TongCenter\TongSaveLoad.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KPlayerTreasure.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KMailAttachmentImp.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KMailImp.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KPostOffice_Entry.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KPostOfficeImp.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\S3Relay.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\Global.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\StdAfx.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\NetConnect.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\NetServer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\HostConnect.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\HostServer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\TongConnect.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\TongServer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\ChatConnect.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\ChatServer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\ChannelMgr.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\PlayerRelationMgr.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\Frame\RelayConfig.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{0B3A53E7-52A2-3721-8EFE-549413474C42}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{9B634824-E985-3319-BF78-ED480F445829}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
