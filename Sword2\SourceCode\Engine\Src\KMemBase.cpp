//---------------------------------------------------------------------------
// Sword3 Engine (c) 1999-2000 by Kingsoft
//
// File:	KMemBase.cpp
// Date:	2000.08.08
// Code:	Wang<PERSON><PERSON>(Daphnis)
// Desc:	Memory base functions
//---------------------------------------------------------------------------
#include "KWin32.h"
#include "KDebug.h"
#include "KNode.h"
#include "KList.h"
#include "KMemBase.h"
#include <string.h>
//---------------------------------------------------------------------------
class KMemNode : public KNode
{
public:
	DWORD	m_dwMemSize;//�ڴ��С
	DWORD	m_dwMemSign;//�ڴ��־
};
//---------------------------------------------------------------------------
class KMemList : public KList
{
public:
	~KMemList()
	{
		KMemNode* pNode = (KMemNode*)GetHead();
		while (pNode)
		{
			g_DebugLog("KMemList::Leak Detected, Size = %d", pNode->m_dwMemSize);
			pNode = (KMemNode*)pNode->GetNext();
		}
	};
	void ShowUsage()
	{
		KMemNode* pNode = (KMemNode*)GetHead();
		DWORD dwMemSize = 0;
		while (pNode)
		{
			dwMemSize += pNode->m_dwMemSize;
			pNode = (KMemNode*)pNode->GetNext();
		}
		g_DebugLog("Memory Usage Size = %d KB", dwMemSize / 1024);
	}
};
static KMemList m_MemList;
//---------------------------------------------------------------------------
#define MEMSIGN 1234567890
//---------------------------------------------------------------------------
// ����:	g_MemInfo
// ����:	Memory Infomation
// ����:	void
// ����:	void
//---------------------------------------------------------------------------
ENGINE_API void g_MemInfo()
{
//	MEMORYSTATUS stat;
	
//	GlobalMemoryStatus(&stat);
	
//	g_DebugLog("Total Physical Memory = %d MB", stat.dwTotalPhys >> 20);
//	g_DebugLog("Total Virtual Memory = %d MB", stat.dwTotalVirtual >> 20);
//	g_DebugLog("%d percent of memory is in use.", stat.dwMemoryLoad);
}
//---------------------------------------------------------------------------
// ����:	g_MemAlloc
// ����:	�����ڴ�
// ����:	dwSize		�ڴ���С
// ����:	lpMem (lpMem = NULL ��ʾ����ʧ��)
//---------------------------------------------------------------------------
ENGINE_API LPVOID g_MemAlloc(DWORD dwSize)
{
//	HANDLE hHeap = GetProcessHeap();
	PBYTE  lpMem = NULL;
	DWORD  dwHeapSize = dwSize + sizeof(KMemNode);

//	lpMem = (PBYTE)HeapAlloc(hHeap, 0, dwHeapSize);
	lpMem = (PBYTE)new char[dwHeapSize];
	if (NULL == lpMem)
	{
		g_MessageBox("g_MemAlloc() Failed, Size = %d", dwSize);
		return NULL;
	}

	KMemNode* pNode = (KMemNode*)lpMem;
	pNode->m_pPrev = NULL;
	pNode->m_pNext = NULL;
	pNode->m_dwMemSize = dwSize;
	pNode->m_dwMemSign = MEMSIGN;
	m_MemList.AddHead(pNode);
	
	return (lpMem + sizeof(KMemNode));
//	return 0;
}
//---------------------------------------------------------------------------
// ����:	g_MemFree
// ����:	�ͷ��ڴ�
// ����:	lpMem		Ҫ�ͷŵ��ڴ�ָ��
// ����:	void
//---------------------------------------------------------------------------
ENGINE_API void g_MemFree(LPVOID lpMem)
{
//	HANDLE hHeap = GetProcessHeap();
	if (lpMem == NULL)
		return;
	lpMem = (PBYTE)lpMem - sizeof(KMemNode);
	KMemNode* pNode = (KMemNode *)lpMem;
	if (pNode->m_dwMemSign != MEMSIGN)
	{
		g_MessageBox("g_MemFree() Failed, Size = %d", pNode->m_dwMemSize);
		return;
	}
	pNode->Remove();
//	HeapFree(hHeap, 0, lpMem);
	delete[] lpMem;
}
//---------------------------------------------------------------------------
// ����:	MemoryCopy
// ����:	�ڴ濽��
// ����:	lpDest	:	Ŀ���ڴ��
//			lpSrc	:	Դ�ڴ��
//			dwLen	:	��������
// ����:	void
//---------------------------------------------------------------------------
ENGINE_API void g_MemCopy(PVOID lpDest, PVOID lpSrc, DWORD dwLen)
{
	// 使用标准C函数替换内联汇编，提供更好的兼容性
	memcpy(lpDest, lpSrc, dwLen);
}
//---------------------------------------------------------------------------
// ����:	MemoryCopyMmx
// ����:	�ڴ濽����MMX�汾��
// ����:	lpDest	:	Ŀ���ڴ��
//			lpSrc	:	Դ�ڴ��
//			dwLen	:	��������
// ����:	void
//---------------------------------------------------------------------------
ENGINE_API void g_MemCopyMmx(PVOID lpDest, PVOID lpSrc, DWORD dwLen)
{
	// 使用标准C函数替换MMX内联汇编，提供更好的兼容性
	memcpy(lpDest, lpSrc, dwLen);
}
//---------------------------------------------------------------------------
// ����:	MemoryComp
// ����:	�ڴ�Ƚ�
// ����:	lpDest	:	�ڴ��1
//			lpSrc	:	�ڴ��2
//			dwLen	:	�Ƚϳ���
// ����:	TRUE	:	��ͬ
//			FALSE	:	��ͬ	
//---------------------------------------------------------------------------
ENGINE_API BOOL g_MemComp(PVOID lpDest, PVOID lpSrc, DWORD dwLen)
{
	// 使用标准C函数替换内联汇编，提供更好的兼容性
	return (0 == memcmp(lpDest, lpSrc, dwLen));
}
//---------------------------------------------------------------------------
// ����:	MemoryFill
// ����:	�ڴ����
// ����:	lpDest	:	�ڴ��ַ
//			dwLen	:	�ڴ泤��
//			byFill	:	����ֽ�
// ����:	void
//---------------------------------------------------------------------------
ENGINE_API void g_MemFill(PVOID lpDest, DWORD dwLen, BYTE byFill)
{
	// 使用标准C函数替换内联汇编，提供更好的兼容性
	memset(lpDest, byFill, dwLen);
}
//---------------------------------------------------------------------------
// ����:	MemoryFill
// ����:	�ڴ����
// ����:	lpDest	:	�ڴ��ַ
//			dwLen	:	�ڴ泤��
//			wFill	:	�����
// ����:	void
//---------------------------------------------------------------------------
ENGINE_API void g_MemFill(PVOID lpDest, DWORD dwLen, WORD wFill)
{
	// 使用C++实现替换内联汇编
	WORD* pDest = (WORD*)lpDest;
	DWORD wordCount = dwLen / 2;

	// 填充WORD值
	for (DWORD i = 0; i < wordCount; i++) {
		pDest[i] = wFill;
	}

	// 处理剩余的字节
	if (dwLen & 1) {
		((BYTE*)lpDest)[dwLen - 1] = (BYTE)(wFill & 0xFF);
	}
}
//---------------------------------------------------------------------------
// ����:	MemoryFill
// ����:	�ڴ����
// ����:	lpDest	:	�ڴ��ַ
//			dwLen	:	�ڴ泤��
//			dwFill	:	�����
// ����:	void
//---------------------------------------------------------------------------
ENGINE_API void g_MemFill(PVOID lpDest, DWORD dwLen, DWORD dwFill)
{
	// 使用C++实现替换内联汇编
	DWORD* pDest = (DWORD*)lpDest;
	DWORD dwordCount = dwLen / 4;

	// 填充DWORD值
	for (DWORD i = 0; i < dwordCount; i++) {
		pDest[i] = dwFill;
	}

	// 处理剩余的字节
	DWORD remaining = dwLen & 3;
	if (remaining) {
		BYTE* pByteDest = (BYTE*)lpDest + (dwordCount * 4);
		BYTE fillByte = (BYTE)(dwFill & 0xFF);
		for (DWORD i = 0; i < remaining; i++) {
			pByteDest[i] = fillByte;
		}
	}
}
//---------------------------------------------------------------------------
// ����:	MemoryZero
// ����:	�ڴ�����
// ����:	lpDest	:	�ڴ��ַ
//			dwLen	:	�ڴ泤��
// ����:	void
//---------------------------------------------------------------------------
ENGINE_API void g_MemZero(PVOID lpDest, DWORD dwLen)
{
	// 使用标准C函数替换内联汇编，提供更好的兼容性
	memset(lpDest, 0, dwLen);
}
//---------------------------------------------------------------------------
// ����:	MemoryXore
// ����:	�ڴ����
// ����:	lpDest	:	�ڴ��ַ
//			dwLen	:	�ڴ泤��
//			dwXor	:	����ֽ�
// ����:	void
//---------------------------------------------------------------------------
ENGINE_API void g_MemXore(PVOID lpDest, DWORD dwLen, DWORD dwXor)
{
	// 使用C++实现替换内联汇编
	unsigned long *ptr = (unsigned long *)lpDest;
	while((long)dwLen > 0) {
		*ptr++ ^= dwXor;
		dwLen -= sizeof(unsigned long);
	}
}
