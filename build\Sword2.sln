﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{45C90F55-7628-38C4-BE81-1AD9E75B3593}"
	ProjectSection(ProjectDependencies) = postProject
		{D3A745C7-F449-38E6-A5E0-50254F647047} = {D3A745C7-F449-38E6-A5E0-50254F647047}
		{DCBB2576-63F8-3282-8324-6B83E2291047} = {DCBB2576-63F8-3282-8324-6B83E2291047}
		{1DEC4C72-CAE8-3361-BB54-E945B94A95D3} = {1DEC4C72-CAE8-3361-BB54-E945B94A95D3}
		{EAAD319E-6632-3F23-BE11-3737C8927111} = {EAAD319E-6632-3F23-BE11-3737C8927111}
		{9E0E1A90-73F3-3BCB-BCFC-1C8F59FE30D2} = {9E0E1A90-73F3-3BCB-BCFC-1C8F59FE30D2}
		{BC5521A1-621D-3C40-A4C8-930ADE16234B} = {BC5521A1-621D-3C40-A4C8-930ADE16234B}
		{F5730F5E-5A8C-36D0-B965-53150D6418B9} = {F5730F5E-5A8C-36D0-B965-53150D6418B9}
		{8EEE08AD-1CAE-3836-9DF2-9BE8F4BE821F} = {8EEE08AD-1CAE-3836-9DF2-9BE8F4BE821F}
		{27D7B65B-2C41-3904-A9D6-63B8C0B95A75} = {27D7B65B-2C41-3904-A9D6-63B8C0B95A75}
		{E6F9E5D8-561E-31E6-A73F-5193D85ABEC6} = {E6F9E5D8-561E-31E6-A73F-5193D85ABEC6}
		{DD555F75-C029-390C-A273-CC9C0F1282E0} = {DD555F75-C029-390C-A273-CC9C0F1282E0}
		{86782B81-090C-311F-9847-D8085B2DEC07} = {86782B81-090C-311F-9847-D8085B2DEC07}
		{A2E38CD9-1930-365C-A1D5-6FD8D6E22C62} = {A2E38CD9-1930-365C-A1D5-6FD8D6E22C62}
		{C4FD1033-04AF-354D-B771-7FB5E41AF8D3} = {C4FD1033-04AF-354D-B771-7FB5E41AF8D3}
		{BD1C447D-FAB8-348C-B804-86A110DFD4DB} = {BD1C447D-FAB8-348C-B804-86A110DFD4DB}
		{C6493F25-72E5-3F46-85DC-17DE14FDEF42} = {C6493F25-72E5-3F46-85DC-17DE14FDEF42}
		{955CD721-F28C-365F-B620-B2B4B9360A4E} = {955CD721-F28C-365F-B620-B2B4B9360A4E}
		{7036927C-AA11-3460-8F35-ED020429BD55} = {7036927C-AA11-3460-8F35-ED020429BD55}
		{A35C0092-ACAA-3B78-BBBE-8CFC7A5A149A} = {A35C0092-ACAA-3B78-BBBE-8CFC7A5A149A}
		{50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F} = {50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F}
		{101D8881-BA1E-3004-AD84-2DC490E1A462} = {101D8881-BA1E-3004-AD84-2DC490E1A462}
		{D2AF79E4-7C53-3128-8E48-21A5FB87A83D} = {D2AF79E4-7C53-3128-8E48-21A5FB87A83D}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ConfigSystemTest", "Sword2\SourceCode\Engine\ConfigSystemTest.vcxproj", "{D3A745C7-F449-38E6-A5E0-50254F647047}"
	ProjectSection(ProjectDependencies) = postProject
		{50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F} = {50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Core", "Sword2\SourceCode\Common\Core\Core.vcxproj", "{DCBB2576-63F8-3282-8324-6B83E2291047}"
	ProjectSection(ProjectDependencies) = postProject
		{F5730F5E-5A8C-36D0-B965-53150D6418B9} = {F5730F5E-5A8C-36D0-B965-53150D6418B9}
		{86782B81-090C-311F-9847-D8085B2DEC07} = {86782B81-090C-311F-9847-D8085B2DEC07}
		{50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F} = {50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "CoreTest", "Sword2\SourceCode\Engine\CoreTest.vcxproj", "{1DEC4C72-CAE8-3361-BB54-E945B94A95D3}"
	ProjectSection(ProjectDependencies) = postProject
		{50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F} = {50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "DataStructuresTest", "Sword2\SourceCode\Engine\DataStructuresTest.vcxproj", "{EAAD319E-6632-3F23-BE11-3737C8927111}"
	ProjectSection(ProjectDependencies) = postProject
		{50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F} = {50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Dawn", "Sword2\SourceCode\Client\Dawn\Dawn.vcxproj", "{9E0E1A90-73F3-3BCB-BCFC-1C8F59FE30D2}"
	ProjectSection(ProjectDependencies) = postProject
		{DCBB2576-63F8-3282-8324-6B83E2291047} = {DCBB2576-63F8-3282-8324-6B83E2291047}
		{F5730F5E-5A8C-36D0-B965-53150D6418B9} = {F5730F5E-5A8C-36D0-B965-53150D6418B9}
		{86782B81-090C-311F-9847-D8085B2DEC07} = {86782B81-090C-311F-9847-D8085B2DEC07}
		{A35C0092-ACAA-3B78-BBBE-8CFC7A5A149A} = {A35C0092-ACAA-3B78-BBBE-8CFC7A5A149A}
		{50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F} = {50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "DawnClient", "Sword2\SourceCode\Client\DawnClient\DawnClient.vcxproj", "{BC5521A1-621D-3C40-A4C8-930ADE16234B}"
	ProjectSection(ProjectDependencies) = postProject
		{50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F} = {50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Engine", "Sword2\SourceCode\Engine\Engine.vcxproj", "{F5730F5E-5A8C-36D0-B965-53150D6418B9}"
	ProjectSection(ProjectDependencies) = postProject
		{50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F} = {50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "FileSystemTest", "Sword2\SourceCode\Engine\FileSystemTest.vcxproj", "{8EEE08AD-1CAE-3836-9DF2-9BE8F4BE821F}"
	ProjectSection(ProjectDependencies) = postProject
		{50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F} = {50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "GameServer", "Sword2\SourceCode\Server\GameServer\GameServer.vcxproj", "{27D7B65B-2C41-3904-A9D6-63B8C0B95A75}"
	ProjectSection(ProjectDependencies) = postProject
		{DCBB2576-63F8-3282-8324-6B83E2291047} = {DCBB2576-63F8-3282-8324-6B83E2291047}
		{F5730F5E-5A8C-36D0-B965-53150D6418B9} = {F5730F5E-5A8C-36D0-B965-53150D6418B9}
		{86782B81-090C-311F-9847-D8085B2DEC07} = {86782B81-090C-311F-9847-D8085B2DEC07}
		{50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F} = {50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "GameSys", "Sword2\SourceCode\Common\GameSys\GameSys.vcxproj", "{E6F9E5D8-561E-31E6-A73F-5193D85ABEC6}"
	ProjectSection(ProjectDependencies) = postProject
		{DCBB2576-63F8-3282-8324-6B83E2291047} = {DCBB2576-63F8-3282-8324-6B83E2291047}
		{F5730F5E-5A8C-36D0-B965-53150D6418B9} = {F5730F5E-5A8C-36D0-B965-53150D6418B9}
		{86782B81-090C-311F-9847-D8085B2DEC07} = {86782B81-090C-311F-9847-D8085B2DEC07}
		{50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F} = {50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "HashTableTest", "Sword2\SourceCode\Engine\HashTableTest.vcxproj", "{DD555F75-C029-390C-A273-CC9C0F1282E0}"
	ProjectSection(ProjectDependencies) = postProject
		{50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F} = {50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{638B1978-AE4D-3A9A-AA86-7B06680B8586}"
	ProjectSection(ProjectDependencies) = postProject
		{45C90F55-7628-38C4-BE81-1AD9E75B3593} = {45C90F55-7628-38C4-BE81-1AD9E75B3593}
		{50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F} = {50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Lua5", "DevEnv\Lua5\Lua5.vcxproj", "{86782B81-090C-311F-9847-D8085B2DEC07}"
	ProjectSection(ProjectDependencies) = postProject
		{50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F} = {50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "MinimalTest", "Sword2\SourceCode\Engine\MinimalTest.vcxproj", "{A2E38CD9-1930-365C-A1D5-6FD8D6E22C62}"
	ProjectSection(ProjectDependencies) = postProject
		{50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F} = {50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "PACKAGE", "PACKAGE.vcxproj", "{F620ABEB-0117-3596-BF21-0D75A3A5C799}"
	ProjectSection(ProjectDependencies) = postProject
		{45C90F55-7628-38C4-BE81-1AD9E75B3593} = {45C90F55-7628-38C4-BE81-1AD9E75B3593}
		{50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F} = {50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Relay", "Sword2\SourceCode\Server\Relay\Relay.vcxproj", "{C4FD1033-04AF-354D-B771-7FB5E41AF8D3}"
	ProjectSection(ProjectDependencies) = postProject
		{DCBB2576-63F8-3282-8324-6B83E2291047} = {DCBB2576-63F8-3282-8324-6B83E2291047}
		{F5730F5E-5A8C-36D0-B965-53150D6418B9} = {F5730F5E-5A8C-36D0-B965-53150D6418B9}
		{86782B81-090C-311F-9847-D8085B2DEC07} = {86782B81-090C-311F-9847-D8085B2DEC07}
		{50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F} = {50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "RelayT", "Sword2\SourceCode\Server\RelayT\RelayT.vcxproj", "{BD1C447D-FAB8-348C-B804-86A110DFD4DB}"
	ProjectSection(ProjectDependencies) = postProject
		{DCBB2576-63F8-3282-8324-6B83E2291047} = {DCBB2576-63F8-3282-8324-6B83E2291047}
		{F5730F5E-5A8C-36D0-B965-53150D6418B9} = {F5730F5E-5A8C-36D0-B965-53150D6418B9}
		{86782B81-090C-311F-9847-D8085B2DEC07} = {86782B81-090C-311F-9847-D8085B2DEC07}
		{50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F} = {50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "SortListTest", "Sword2\SourceCode\Engine\SortListTest.vcxproj", "{C6493F25-72E5-3F46-85DC-17DE14FDEF42}"
	ProjectSection(ProjectDependencies) = postProject
		{50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F} = {50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Stat", "Sword2\SourceCode\Common\Stat\Stat.vcxproj", "{955CD721-F28C-365F-B620-B2B4B9360A4E}"
	ProjectSection(ProjectDependencies) = postProject
		{DCBB2576-63F8-3282-8324-6B83E2291047} = {DCBB2576-63F8-3282-8324-6B83E2291047}
		{F5730F5E-5A8C-36D0-B965-53150D6418B9} = {F5730F5E-5A8C-36D0-B965-53150D6418B9}
		{86782B81-090C-311F-9847-D8085B2DEC07} = {86782B81-090C-311F-9847-D8085B2DEC07}
		{50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F} = {50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "StringSystemTest", "Sword2\SourceCode\Engine\StringSystemTest.vcxproj", "{7036927C-AA11-3460-8F35-ED020429BD55}"
	ProjectSection(ProjectDependencies) = postProject
		{50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F} = {50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Support", "Sword2\SourceCode\Common\Support\Support.vcxproj", "{A35C0092-ACAA-3B78-BBBE-8CFC7A5A149A}"
	ProjectSection(ProjectDependencies) = postProject
		{DCBB2576-63F8-3282-8324-6B83E2291047} = {DCBB2576-63F8-3282-8324-6B83E2291047}
		{F5730F5E-5A8C-36D0-B965-53150D6418B9} = {F5730F5E-5A8C-36D0-B965-53150D6418B9}
		{86782B81-090C-311F-9847-D8085B2DEC07} = {86782B81-090C-311F-9847-D8085B2DEC07}
		{50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F} = {50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "lua", "DevEnv\Lua5\lua.vcxproj", "{101D8881-BA1E-3004-AD84-2DC490E1A462}"
	ProjectSection(ProjectDependencies) = postProject
		{86782B81-090C-311F-9847-D8085B2DEC07} = {86782B81-090C-311F-9847-D8085B2DEC07}
		{50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F} = {50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "luac", "DevEnv\Lua5\luac.vcxproj", "{D2AF79E4-7C53-3128-8E48-21A5FB87A83D}"
	ProjectSection(ProjectDependencies) = postProject
		{86782B81-090C-311F-9847-D8085B2DEC07} = {86782B81-090C-311F-9847-D8085B2DEC07}
		{50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F} = {50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{45C90F55-7628-38C4-BE81-1AD9E75B3593}.Debug|x64.ActiveCfg = Debug|x64
		{45C90F55-7628-38C4-BE81-1AD9E75B3593}.Debug|x64.Build.0 = Debug|x64
		{45C90F55-7628-38C4-BE81-1AD9E75B3593}.Release|x64.ActiveCfg = Release|x64
		{45C90F55-7628-38C4-BE81-1AD9E75B3593}.Release|x64.Build.0 = Release|x64
		{45C90F55-7628-38C4-BE81-1AD9E75B3593}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{45C90F55-7628-38C4-BE81-1AD9E75B3593}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{45C90F55-7628-38C4-BE81-1AD9E75B3593}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{45C90F55-7628-38C4-BE81-1AD9E75B3593}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{D3A745C7-F449-38E6-A5E0-50254F647047}.Debug|x64.ActiveCfg = Debug|x64
		{D3A745C7-F449-38E6-A5E0-50254F647047}.Debug|x64.Build.0 = Debug|x64
		{D3A745C7-F449-38E6-A5E0-50254F647047}.Release|x64.ActiveCfg = Release|x64
		{D3A745C7-F449-38E6-A5E0-50254F647047}.Release|x64.Build.0 = Release|x64
		{D3A745C7-F449-38E6-A5E0-50254F647047}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{D3A745C7-F449-38E6-A5E0-50254F647047}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{D3A745C7-F449-38E6-A5E0-50254F647047}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{D3A745C7-F449-38E6-A5E0-50254F647047}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{DCBB2576-63F8-3282-8324-6B83E2291047}.Debug|x64.ActiveCfg = Debug|x64
		{DCBB2576-63F8-3282-8324-6B83E2291047}.Debug|x64.Build.0 = Debug|x64
		{DCBB2576-63F8-3282-8324-6B83E2291047}.Release|x64.ActiveCfg = Release|x64
		{DCBB2576-63F8-3282-8324-6B83E2291047}.Release|x64.Build.0 = Release|x64
		{DCBB2576-63F8-3282-8324-6B83E2291047}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{DCBB2576-63F8-3282-8324-6B83E2291047}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{DCBB2576-63F8-3282-8324-6B83E2291047}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{DCBB2576-63F8-3282-8324-6B83E2291047}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{1DEC4C72-CAE8-3361-BB54-E945B94A95D3}.Debug|x64.ActiveCfg = Debug|x64
		{1DEC4C72-CAE8-3361-BB54-E945B94A95D3}.Debug|x64.Build.0 = Debug|x64
		{1DEC4C72-CAE8-3361-BB54-E945B94A95D3}.Release|x64.ActiveCfg = Release|x64
		{1DEC4C72-CAE8-3361-BB54-E945B94A95D3}.Release|x64.Build.0 = Release|x64
		{1DEC4C72-CAE8-3361-BB54-E945B94A95D3}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{1DEC4C72-CAE8-3361-BB54-E945B94A95D3}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{1DEC4C72-CAE8-3361-BB54-E945B94A95D3}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{1DEC4C72-CAE8-3361-BB54-E945B94A95D3}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{EAAD319E-6632-3F23-BE11-3737C8927111}.Debug|x64.ActiveCfg = Debug|x64
		{EAAD319E-6632-3F23-BE11-3737C8927111}.Debug|x64.Build.0 = Debug|x64
		{EAAD319E-6632-3F23-BE11-3737C8927111}.Release|x64.ActiveCfg = Release|x64
		{EAAD319E-6632-3F23-BE11-3737C8927111}.Release|x64.Build.0 = Release|x64
		{EAAD319E-6632-3F23-BE11-3737C8927111}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{EAAD319E-6632-3F23-BE11-3737C8927111}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{EAAD319E-6632-3F23-BE11-3737C8927111}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{EAAD319E-6632-3F23-BE11-3737C8927111}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{9E0E1A90-73F3-3BCB-BCFC-1C8F59FE30D2}.Debug|x64.ActiveCfg = Debug|x64
		{9E0E1A90-73F3-3BCB-BCFC-1C8F59FE30D2}.Debug|x64.Build.0 = Debug|x64
		{9E0E1A90-73F3-3BCB-BCFC-1C8F59FE30D2}.Release|x64.ActiveCfg = Release|x64
		{9E0E1A90-73F3-3BCB-BCFC-1C8F59FE30D2}.Release|x64.Build.0 = Release|x64
		{9E0E1A90-73F3-3BCB-BCFC-1C8F59FE30D2}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{9E0E1A90-73F3-3BCB-BCFC-1C8F59FE30D2}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{9E0E1A90-73F3-3BCB-BCFC-1C8F59FE30D2}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{9E0E1A90-73F3-3BCB-BCFC-1C8F59FE30D2}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{BC5521A1-621D-3C40-A4C8-930ADE16234B}.Debug|x64.ActiveCfg = Debug|x64
		{BC5521A1-621D-3C40-A4C8-930ADE16234B}.Debug|x64.Build.0 = Debug|x64
		{BC5521A1-621D-3C40-A4C8-930ADE16234B}.Release|x64.ActiveCfg = Release|x64
		{BC5521A1-621D-3C40-A4C8-930ADE16234B}.Release|x64.Build.0 = Release|x64
		{BC5521A1-621D-3C40-A4C8-930ADE16234B}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{BC5521A1-621D-3C40-A4C8-930ADE16234B}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{BC5521A1-621D-3C40-A4C8-930ADE16234B}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{BC5521A1-621D-3C40-A4C8-930ADE16234B}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{F5730F5E-5A8C-36D0-B965-53150D6418B9}.Debug|x64.ActiveCfg = Debug|x64
		{F5730F5E-5A8C-36D0-B965-53150D6418B9}.Debug|x64.Build.0 = Debug|x64
		{F5730F5E-5A8C-36D0-B965-53150D6418B9}.Release|x64.ActiveCfg = Release|x64
		{F5730F5E-5A8C-36D0-B965-53150D6418B9}.Release|x64.Build.0 = Release|x64
		{F5730F5E-5A8C-36D0-B965-53150D6418B9}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{F5730F5E-5A8C-36D0-B965-53150D6418B9}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{F5730F5E-5A8C-36D0-B965-53150D6418B9}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{F5730F5E-5A8C-36D0-B965-53150D6418B9}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{8EEE08AD-1CAE-3836-9DF2-9BE8F4BE821F}.Debug|x64.ActiveCfg = Debug|x64
		{8EEE08AD-1CAE-3836-9DF2-9BE8F4BE821F}.Debug|x64.Build.0 = Debug|x64
		{8EEE08AD-1CAE-3836-9DF2-9BE8F4BE821F}.Release|x64.ActiveCfg = Release|x64
		{8EEE08AD-1CAE-3836-9DF2-9BE8F4BE821F}.Release|x64.Build.0 = Release|x64
		{8EEE08AD-1CAE-3836-9DF2-9BE8F4BE821F}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{8EEE08AD-1CAE-3836-9DF2-9BE8F4BE821F}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{8EEE08AD-1CAE-3836-9DF2-9BE8F4BE821F}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{8EEE08AD-1CAE-3836-9DF2-9BE8F4BE821F}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{27D7B65B-2C41-3904-A9D6-63B8C0B95A75}.Debug|x64.ActiveCfg = Debug|x64
		{27D7B65B-2C41-3904-A9D6-63B8C0B95A75}.Debug|x64.Build.0 = Debug|x64
		{27D7B65B-2C41-3904-A9D6-63B8C0B95A75}.Release|x64.ActiveCfg = Release|x64
		{27D7B65B-2C41-3904-A9D6-63B8C0B95A75}.Release|x64.Build.0 = Release|x64
		{27D7B65B-2C41-3904-A9D6-63B8C0B95A75}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{27D7B65B-2C41-3904-A9D6-63B8C0B95A75}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{27D7B65B-2C41-3904-A9D6-63B8C0B95A75}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{27D7B65B-2C41-3904-A9D6-63B8C0B95A75}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{E6F9E5D8-561E-31E6-A73F-5193D85ABEC6}.Debug|x64.ActiveCfg = Debug|x64
		{E6F9E5D8-561E-31E6-A73F-5193D85ABEC6}.Debug|x64.Build.0 = Debug|x64
		{E6F9E5D8-561E-31E6-A73F-5193D85ABEC6}.Release|x64.ActiveCfg = Release|x64
		{E6F9E5D8-561E-31E6-A73F-5193D85ABEC6}.Release|x64.Build.0 = Release|x64
		{E6F9E5D8-561E-31E6-A73F-5193D85ABEC6}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{E6F9E5D8-561E-31E6-A73F-5193D85ABEC6}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{E6F9E5D8-561E-31E6-A73F-5193D85ABEC6}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{E6F9E5D8-561E-31E6-A73F-5193D85ABEC6}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{DD555F75-C029-390C-A273-CC9C0F1282E0}.Debug|x64.ActiveCfg = Debug|x64
		{DD555F75-C029-390C-A273-CC9C0F1282E0}.Debug|x64.Build.0 = Debug|x64
		{DD555F75-C029-390C-A273-CC9C0F1282E0}.Release|x64.ActiveCfg = Release|x64
		{DD555F75-C029-390C-A273-CC9C0F1282E0}.Release|x64.Build.0 = Release|x64
		{DD555F75-C029-390C-A273-CC9C0F1282E0}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{DD555F75-C029-390C-A273-CC9C0F1282E0}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{DD555F75-C029-390C-A273-CC9C0F1282E0}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{DD555F75-C029-390C-A273-CC9C0F1282E0}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{638B1978-AE4D-3A9A-AA86-7B06680B8586}.Debug|x64.ActiveCfg = Debug|x64
		{638B1978-AE4D-3A9A-AA86-7B06680B8586}.Release|x64.ActiveCfg = Release|x64
		{638B1978-AE4D-3A9A-AA86-7B06680B8586}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{638B1978-AE4D-3A9A-AA86-7B06680B8586}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{86782B81-090C-311F-9847-D8085B2DEC07}.Debug|x64.ActiveCfg = Debug|x64
		{86782B81-090C-311F-9847-D8085B2DEC07}.Debug|x64.Build.0 = Debug|x64
		{86782B81-090C-311F-9847-D8085B2DEC07}.Release|x64.ActiveCfg = Release|x64
		{86782B81-090C-311F-9847-D8085B2DEC07}.Release|x64.Build.0 = Release|x64
		{86782B81-090C-311F-9847-D8085B2DEC07}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{86782B81-090C-311F-9847-D8085B2DEC07}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{86782B81-090C-311F-9847-D8085B2DEC07}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{86782B81-090C-311F-9847-D8085B2DEC07}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{A2E38CD9-1930-365C-A1D5-6FD8D6E22C62}.Debug|x64.ActiveCfg = Debug|x64
		{A2E38CD9-1930-365C-A1D5-6FD8D6E22C62}.Debug|x64.Build.0 = Debug|x64
		{A2E38CD9-1930-365C-A1D5-6FD8D6E22C62}.Release|x64.ActiveCfg = Release|x64
		{A2E38CD9-1930-365C-A1D5-6FD8D6E22C62}.Release|x64.Build.0 = Release|x64
		{A2E38CD9-1930-365C-A1D5-6FD8D6E22C62}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{A2E38CD9-1930-365C-A1D5-6FD8D6E22C62}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{A2E38CD9-1930-365C-A1D5-6FD8D6E22C62}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{A2E38CD9-1930-365C-A1D5-6FD8D6E22C62}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{F620ABEB-0117-3596-BF21-0D75A3A5C799}.Debug|x64.ActiveCfg = Debug|x64
		{F620ABEB-0117-3596-BF21-0D75A3A5C799}.Release|x64.ActiveCfg = Release|x64
		{F620ABEB-0117-3596-BF21-0D75A3A5C799}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{F620ABEB-0117-3596-BF21-0D75A3A5C799}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{C4FD1033-04AF-354D-B771-7FB5E41AF8D3}.Debug|x64.ActiveCfg = Debug|x64
		{C4FD1033-04AF-354D-B771-7FB5E41AF8D3}.Debug|x64.Build.0 = Debug|x64
		{C4FD1033-04AF-354D-B771-7FB5E41AF8D3}.Release|x64.ActiveCfg = Release|x64
		{C4FD1033-04AF-354D-B771-7FB5E41AF8D3}.Release|x64.Build.0 = Release|x64
		{C4FD1033-04AF-354D-B771-7FB5E41AF8D3}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{C4FD1033-04AF-354D-B771-7FB5E41AF8D3}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{C4FD1033-04AF-354D-B771-7FB5E41AF8D3}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{C4FD1033-04AF-354D-B771-7FB5E41AF8D3}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{BD1C447D-FAB8-348C-B804-86A110DFD4DB}.Debug|x64.ActiveCfg = Debug|x64
		{BD1C447D-FAB8-348C-B804-86A110DFD4DB}.Debug|x64.Build.0 = Debug|x64
		{BD1C447D-FAB8-348C-B804-86A110DFD4DB}.Release|x64.ActiveCfg = Release|x64
		{BD1C447D-FAB8-348C-B804-86A110DFD4DB}.Release|x64.Build.0 = Release|x64
		{BD1C447D-FAB8-348C-B804-86A110DFD4DB}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{BD1C447D-FAB8-348C-B804-86A110DFD4DB}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{BD1C447D-FAB8-348C-B804-86A110DFD4DB}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{BD1C447D-FAB8-348C-B804-86A110DFD4DB}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{C6493F25-72E5-3F46-85DC-17DE14FDEF42}.Debug|x64.ActiveCfg = Debug|x64
		{C6493F25-72E5-3F46-85DC-17DE14FDEF42}.Debug|x64.Build.0 = Debug|x64
		{C6493F25-72E5-3F46-85DC-17DE14FDEF42}.Release|x64.ActiveCfg = Release|x64
		{C6493F25-72E5-3F46-85DC-17DE14FDEF42}.Release|x64.Build.0 = Release|x64
		{C6493F25-72E5-3F46-85DC-17DE14FDEF42}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{C6493F25-72E5-3F46-85DC-17DE14FDEF42}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{C6493F25-72E5-3F46-85DC-17DE14FDEF42}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{C6493F25-72E5-3F46-85DC-17DE14FDEF42}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{955CD721-F28C-365F-B620-B2B4B9360A4E}.Debug|x64.ActiveCfg = Debug|x64
		{955CD721-F28C-365F-B620-B2B4B9360A4E}.Debug|x64.Build.0 = Debug|x64
		{955CD721-F28C-365F-B620-B2B4B9360A4E}.Release|x64.ActiveCfg = Release|x64
		{955CD721-F28C-365F-B620-B2B4B9360A4E}.Release|x64.Build.0 = Release|x64
		{955CD721-F28C-365F-B620-B2B4B9360A4E}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{955CD721-F28C-365F-B620-B2B4B9360A4E}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{955CD721-F28C-365F-B620-B2B4B9360A4E}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{955CD721-F28C-365F-B620-B2B4B9360A4E}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{7036927C-AA11-3460-8F35-ED020429BD55}.Debug|x64.ActiveCfg = Debug|x64
		{7036927C-AA11-3460-8F35-ED020429BD55}.Debug|x64.Build.0 = Debug|x64
		{7036927C-AA11-3460-8F35-ED020429BD55}.Release|x64.ActiveCfg = Release|x64
		{7036927C-AA11-3460-8F35-ED020429BD55}.Release|x64.Build.0 = Release|x64
		{7036927C-AA11-3460-8F35-ED020429BD55}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{7036927C-AA11-3460-8F35-ED020429BD55}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{7036927C-AA11-3460-8F35-ED020429BD55}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{7036927C-AA11-3460-8F35-ED020429BD55}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{A35C0092-ACAA-3B78-BBBE-8CFC7A5A149A}.Debug|x64.ActiveCfg = Debug|x64
		{A35C0092-ACAA-3B78-BBBE-8CFC7A5A149A}.Debug|x64.Build.0 = Debug|x64
		{A35C0092-ACAA-3B78-BBBE-8CFC7A5A149A}.Release|x64.ActiveCfg = Release|x64
		{A35C0092-ACAA-3B78-BBBE-8CFC7A5A149A}.Release|x64.Build.0 = Release|x64
		{A35C0092-ACAA-3B78-BBBE-8CFC7A5A149A}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{A35C0092-ACAA-3B78-BBBE-8CFC7A5A149A}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{A35C0092-ACAA-3B78-BBBE-8CFC7A5A149A}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{A35C0092-ACAA-3B78-BBBE-8CFC7A5A149A}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F}.Debug|x64.ActiveCfg = Debug|x64
		{50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F}.Debug|x64.Build.0 = Debug|x64
		{50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F}.Release|x64.ActiveCfg = Release|x64
		{50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F}.Release|x64.Build.0 = Release|x64
		{50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{101D8881-BA1E-3004-AD84-2DC490E1A462}.Debug|x64.ActiveCfg = Debug|x64
		{101D8881-BA1E-3004-AD84-2DC490E1A462}.Debug|x64.Build.0 = Debug|x64
		{101D8881-BA1E-3004-AD84-2DC490E1A462}.Release|x64.ActiveCfg = Release|x64
		{101D8881-BA1E-3004-AD84-2DC490E1A462}.Release|x64.Build.0 = Release|x64
		{101D8881-BA1E-3004-AD84-2DC490E1A462}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{101D8881-BA1E-3004-AD84-2DC490E1A462}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{101D8881-BA1E-3004-AD84-2DC490E1A462}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{101D8881-BA1E-3004-AD84-2DC490E1A462}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{D2AF79E4-7C53-3128-8E48-21A5FB87A83D}.Debug|x64.ActiveCfg = Debug|x64
		{D2AF79E4-7C53-3128-8E48-21A5FB87A83D}.Debug|x64.Build.0 = Debug|x64
		{D2AF79E4-7C53-3128-8E48-21A5FB87A83D}.Release|x64.ActiveCfg = Release|x64
		{D2AF79E4-7C53-3128-8E48-21A5FB87A83D}.Release|x64.Build.0 = Release|x64
		{D2AF79E4-7C53-3128-8E48-21A5FB87A83D}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{D2AF79E4-7C53-3128-8E48-21A5FB87A83D}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{D2AF79E4-7C53-3128-8E48-21A5FB87A83D}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{D2AF79E4-7C53-3128-8E48-21A5FB87A83D}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {400609DB-847B-3EED-9547-57AC7D3EBBBB}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
