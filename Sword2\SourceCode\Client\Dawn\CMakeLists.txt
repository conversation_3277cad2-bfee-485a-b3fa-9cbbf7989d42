# Dawn客户端 CMakeLists.txt
# Dawn启动器/客户端

cmake_minimum_required(VERSION 3.16)

# 设置项目名称
set(DAWN_TARGET_NAME Dawn)

# 创建可执行文件目标
add_executable(${DAWN_TARGET_NAME} WIN32)

# 收集所有源文件
file(GLOB DAWN_SOURCES 
    "${CMAKE_CURRENT_SOURCE_DIR}/*.cpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/*.c"
)

file(GLOB DAWN_HEADERS 
    "${CMAKE_CURRENT_SOURCE_DIR}/*.h"
)

file(GLOB DAWN_RESOURCES
    "${CMAKE_CURRENT_SOURCE_DIR}/*.rc"
    "${CMAKE_CURRENT_SOURCE_DIR}/*.ico"
    "${CMAKE_CURRENT_SOURCE_DIR}/*.bmp"
)

# 添加源文件到目标
if(DAWN_SOURCES)
    target_sources(${DAWN_TARGET_NAME} PRIVATE 
        ${DAWN_SOURCES} 
        ${DAWN_HEADERS}
        ${DAWN_RESOURCES}
    )
    message(STATUS "Dawn: Found ${CMAKE_LIST_LENGTH} source files")
else()
    # 如果没有源文件，创建一个简单的main函数
    set(MAIN_SOURCE_FILE "${CMAKE_CURRENT_BINARY_DIR}/dawn_main.cpp")
    file(WRITE ${MAIN_SOURCE_FILE} 
"// Dawn client main entry point
#include <windows.h>

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow)
{
    MessageBox(NULL, L\"Dawn Client - Not implemented\", L\"Dawn\", MB_OK);
    return 0;
}
")
    target_sources(${DAWN_TARGET_NAME} PRIVATE ${MAIN_SOURCE_FILE})
    message(STATUS "Dawn: No sources found, created placeholder main")
endif()

# 设置包含目录
target_include_directories(${DAWN_TARGET_NAME} PRIVATE 
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_SOURCE_DIR}/Sword2/SourceCode/Include
    ${CMAKE_SOURCE_DIR}/Sword2/SourceCode/Engine/Include
)

# 链接依赖库
target_link_libraries(${DAWN_TARGET_NAME} PRIVATE
    Engine
    Core
    Support
)

# Windows特定库
if(WIN32)
    target_link_libraries(${DAWN_TARGET_NAME} PRIVATE
        user32
        gdi32
        shell32
        comctl32
        ole32
        oleaut32
        uuid
        winmm
    )
endif()

# 设置编译属性
set_target_properties(${DAWN_TARGET_NAME} PROPERTIES
    OUTPUT_NAME "Dawn"
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
)

# 平台特定设置
if(WIN32)
    target_compile_definitions(${DAWN_TARGET_NAME} PRIVATE
        WIN32
        _WINDOWS
        UNICODE
        _UNICODE
    )
    
    # 设置子系统为Windows
    set_target_properties(${DAWN_TARGET_NAME} PROPERTIES
        WIN32_EXECUTABLE TRUE
    )
endif()

message(STATUS "Dawn client configured")
