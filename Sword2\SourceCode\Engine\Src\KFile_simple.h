//---------------------------------------------------------------------------
// Sword3 Engine - Simplified Modern File System
// 简化的现代文件系统 - 使用标准C++流，避免std::filesystem依赖
//---------------------------------------------------------------------------
#ifndef KFile_Simple_H
#define KFile_Simple_H

#include "KWin32.h"
#include <fstream>
#include <memory>
#include <string>

//---------------------------------------------------------------------------
#define SEEK_ERROR 0xFFFFFFFF
//---------------------------------------------------------------------------

#ifndef __linux
class ENGINE_API KFile
#else
class KFile
#endif
{
private:
    std::unique_ptr<std::fstream> m_pFile;  // 现代化：使用std::fstream
    DWORD m_dwLen;   // File Size
    DWORD m_dwPos;   // File Pointer
    std::string m_filePath;  // 简化：使用std::string而不是std::filesystem::path
    
public:
    KFile();
    ~KFile();
    
    // 保持原有接口兼容性
    BOOL Open(LPSTR FileName);
    BOOL Create(LPSTR FileName);
    BOOL Append(LPSTR FileName);
    void Close();
    DWORD Read(LPVOID lpBuffer, DWORD dwReadBytes);
    DWORD Write(LPVOID lpBuffer, DWORD dwWriteBytes);
    DWORD Seek(LONG lDistance, DWORD dwMoveMethod);
    DWORD Tell();
    DWORD Size();
    
    // 现代化扩展接口
    bool IsOpen() const;
    bool IsEOF() const;
    const std::string& GetPath() const { return m_filePath; }
    
private:
    // 内部辅助函数
    void UpdateFileSize();
    bool FileExists(const std::string& path);
    void CreateDirectoryForFile(const std::string& filePath);
};

//---------------------------------------------------------------------------
#endif // KFile_Simple_H
