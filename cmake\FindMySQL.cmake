# FindMySQL.cmake
# 查找MySQL客户端库
#
# 定义的变量:
#   MYSQL_FOUND - 如果找到MySQL则为TRUE
#   MYSQL_INCLUDE_DIRS - MySQL头文件目录
#   MYSQL_LIBRARIES - MySQL库文件
#   MYSQL_VERSION - MySQL版本号
#   MYSQL_VERSION_MAJOR - MySQL主版本号
#   MYSQL_VERSION_MINOR - MySQL次版本号
#   MYSQL_VERSION_PATCH - MySQL补丁版本号

# 首先尝试使用pkg-config
find_package(PkgConfig QUIET)
if(PkgConfig_FOUND)
    pkg_check_modules(PC_MYSQL QUIET mysqlclient)
    if(PC_MYSQL_FOUND)
        set(MYSQL_VERSION ${PC_MYSQL_VERSION})
    endif()
endif()

# 查找MySQL头文件
find_path(MYSQL_INCLUDE_DIR
    NAMES mysql.h
    HINTS
        ${PC_MYSQL_INCLUDEDIR}
        ${PC_MYSQL_INCLUDE_DIRS}
    PATHS
        # 项目内部MySQL库 (优先使用新版本)
        ${CMAKE_SOURCE_DIR}/DevEnv/mysql8/include
        ${CMAKE_SOURCE_DIR}/DevEnv/mysql/Include
        ${CMAKE_SOURCE_DIR}/DevEnv/Include/mysql
        # 系统MySQL安装路径
        /usr/include/mysql
        /usr/local/include/mysql
        /usr/local/mysql/include
        /opt/mysql/include
        # Windows MySQL安装路径
        "$ENV{PROGRAMFILES}/MySQL/*/include"
        "$ENV{ProgramFiles\(x86\)}/MySQL/*/include"
        "$ENV{MYSQL_DIR}/include"
        "C:/Program Files/MySQL/*/include"
        "C:/Program Files (x86)/MySQL/*/include"
        "C:/mysql/include"
    DOC "MySQL include directory"
)

# 查找MySQL库文件
find_library(MYSQL_LIBRARY
    NAMES 
        mysqlclient
        mysqlclient_r
        libmysql
        libmysqlclient
    HINTS
        ${PC_MYSQL_LIBDIR}
        ${PC_MYSQL_LIBRARY_DIRS}
    PATHS
        # 项目内部MySQL库
        ${CMAKE_SOURCE_DIR}/DevEnv/mysql/lib/opt
        ${CMAKE_SOURCE_DIR}/DevEnv/mysql/lib/debug
        ${CMAKE_SOURCE_DIR}/DevEnv/mysql/lib
        ${CMAKE_SOURCE_DIR}/DevEnv/mysql8/lib
        ${CMAKE_SOURCE_DIR}/DevEnv/Lib
        # 系统MySQL安装路径
        /usr/lib/mysql
        /usr/local/lib/mysql
        /usr/local/mysql/lib
        /opt/mysql/lib
        # Windows MySQL安装路径
        "$ENV{PROGRAMFILES}/MySQL/*/lib"
        "$ENV{ProgramFiles\(x86\)}/MySQL/*/lib"
        "$ENV{MYSQL_DIR}/lib"
        "C:/Program Files/MySQL/*/lib"
        "C:/Program Files (x86)/MySQL/*/lib"
        "C:/mysql/lib"
    DOC "MySQL library"
)

# 尝试确定MySQL版本
if(MYSQL_INCLUDE_DIR AND NOT MYSQL_VERSION)
    # 查找mysql_version.h文件
    find_file(MYSQL_VERSION_HEADER
        NAMES mysql_version.h
        PATHS ${MYSQL_INCLUDE_DIR}
        NO_DEFAULT_PATH
    )
    
    if(MYSQL_VERSION_HEADER)
        # 读取版本信息
        file(READ ${MYSQL_VERSION_HEADER} MYSQL_VERSION_CONTENT)
        
        # 提取版本号
        string(REGEX MATCH "#define[ \t]+MYSQL_SERVER_VERSION[ \t]+\"([0-9]+)\\.([0-9]+)\\.([0-9]+)" 
               MYSQL_VERSION_MATCH ${MYSQL_VERSION_CONTENT})
        
        if(MYSQL_VERSION_MATCH)
            set(MYSQL_VERSION_MAJOR ${CMAKE_MATCH_1})
            set(MYSQL_VERSION_MINOR ${CMAKE_MATCH_2})
            set(MYSQL_VERSION_PATCH ${CMAKE_MATCH_3})
            set(MYSQL_VERSION "${MYSQL_VERSION_MAJOR}.${MYSQL_VERSION_MINOR}.${MYSQL_VERSION_PATCH}")
        else()
            # 尝试另一种格式
            string(REGEX MATCH "#define[ \t]+MYSQL_VERSION_ID[ \t]+([0-9]+)" 
                   MYSQL_VERSION_ID_MATCH ${MYSQL_VERSION_CONTENT})
            if(MYSQL_VERSION_ID_MATCH)
                set(MYSQL_VERSION_ID ${CMAKE_MATCH_1})
                math(EXPR MYSQL_VERSION_MAJOR "${MYSQL_VERSION_ID} / 10000")
                math(EXPR MYSQL_VERSION_MINOR "(${MYSQL_VERSION_ID} % 10000) / 100")
                math(EXPR MYSQL_VERSION_PATCH "${MYSQL_VERSION_ID} % 100")
                set(MYSQL_VERSION "${MYSQL_VERSION_MAJOR}.${MYSQL_VERSION_MINOR}.${MYSQL_VERSION_PATCH}")
            endif()
        endif()
    endif()
endif()

# 设置变量
set(MYSQL_INCLUDE_DIRS ${MYSQL_INCLUDE_DIR})
set(MYSQL_LIBRARIES ${MYSQL_LIBRARY})

# 添加必要的系统库
if(WIN32)
    list(APPEND MYSQL_LIBRARIES ws2_32 advapi32)
else()
    # Linux/Unix系统可能需要的库
    find_library(MYSQL_Z_LIBRARY z)
    find_library(MYSQL_SSL_LIBRARY ssl)
    find_library(MYSQL_CRYPTO_LIBRARY crypto)
    
    if(MYSQL_Z_LIBRARY)
        list(APPEND MYSQL_LIBRARIES ${MYSQL_Z_LIBRARY})
    endif()
    if(MYSQL_SSL_LIBRARY)
        list(APPEND MYSQL_LIBRARIES ${MYSQL_SSL_LIBRARY})
    endif()
    if(MYSQL_CRYPTO_LIBRARY)
        list(APPEND MYSQL_LIBRARIES ${MYSQL_CRYPTO_LIBRARY})
    endif()
endif()

# 检查是否找到
include(FindPackageHandleStandardArgs)
find_package_handle_standard_args(MySQL
    REQUIRED_VARS MYSQL_INCLUDE_DIR MYSQL_LIBRARY
    VERSION_VAR MYSQL_VERSION
    FAIL_MESSAGE "MySQL client library not found. Please install MySQL development package."
)

# 创建导入目标
if(MYSQL_FOUND AND NOT TARGET MySQL::MySQL)
    add_library(MySQL::MySQL UNKNOWN IMPORTED)
    set_target_properties(MySQL::MySQL PROPERTIES
        IMPORTED_LOCATION "${MYSQL_LIBRARY}"
        INTERFACE_INCLUDE_DIRECTORIES "${MYSQL_INCLUDE_DIRS}"
    )
    
    # 添加系统库依赖
    if(WIN32)
        set_target_properties(MySQL::MySQL PROPERTIES
            INTERFACE_LINK_LIBRARIES "ws2_32;advapi32"
        )
    else()
        set(MYSQL_SYSTEM_LIBS "")
        if(MYSQL_Z_LIBRARY)
            list(APPEND MYSQL_SYSTEM_LIBS ${MYSQL_Z_LIBRARY})
        endif()
        if(MYSQL_SSL_LIBRARY)
            list(APPEND MYSQL_SYSTEM_LIBS ${MYSQL_SSL_LIBRARY})
        endif()
        if(MYSQL_CRYPTO_LIBRARY)
            list(APPEND MYSQL_SYSTEM_LIBS ${MYSQL_CRYPTO_LIBRARY})
        endif()
        
        if(MYSQL_SYSTEM_LIBS)
            set_target_properties(MySQL::MySQL PROPERTIES
                INTERFACE_LINK_LIBRARIES "${MYSQL_SYSTEM_LIBS}"
            )
        endif()
    endif()
endif()

# 标记为高级变量
mark_as_advanced(
    MYSQL_INCLUDE_DIR
    MYSQL_LIBRARY
    MYSQL_VERSION_HEADER
    MYSQL_Z_LIBRARY
    MYSQL_SSL_LIBRARY
    MYSQL_CRYPTO_LIBRARY
)

# 显示结果
if(MYSQL_FOUND)
    message(STATUS "Found MySQL:")
    message(STATUS "  Version: ${MYSQL_VERSION}")
    message(STATUS "  Include dir: ${MYSQL_INCLUDE_DIR}")
    message(STATUS "  Library: ${MYSQL_LIBRARY}")
    
    # 版本警告
    if(MYSQL_VERSION_MAJOR LESS 5)
        message(WARNING "MySQL version ${MYSQL_VERSION} is very old and may have security issues. Consider upgrading to MySQL 8.0+")
    elseif(MYSQL_VERSION_MAJOR EQUAL 5 AND MYSQL_VERSION_MINOR LESS 7)
        message(WARNING "MySQL version ${MYSQL_VERSION} is old. Consider upgrading to MySQL 8.0+ for better performance and security")
    endif()
else()
    message(STATUS "MySQL not found")
endif()
