// Basic compilation test - verify compiler works correctly
#include "KWin32.h"
#include <stdio.h>

// Simple debug implementation for testing
void g_DebugLog(const char* fmt, ...) {
    printf("[DEBUG] %s\n", fmt);
}

// Simple hash function implementation
DWORD SimpleHash(const char* str) {
    if (!str) return 0;
    
    DWORD hash = 5381;
    while (*str) {
        hash = ((hash << 5) + hash) + *str;
        str++;
    }
    return hash;
}

// Simple configuration structure
struct SimpleConfig {
    int width;
    int height;
    int volume;
};

// Simple base class
class SimpleBase {
public:
    virtual ~SimpleBase() {}
    virtual bool Initialize() = 0;
    virtual void Update() = 0;
    virtual void Shutdown() = 0;
    virtual const char* GetName() const = 0;
};

// Simple derived class
class SimpleGraphics : public SimpleBase {
private:
    bool m_initialized;
    
public:
    SimpleGraphics() {
        m_initialized = false;
    }
    
    bool Initialize() {
        g_DebugLog("SimpleGraphics: Initializing");
        m_initialized = true;
        return true;
    }
    
    void Update() {
        // Simple update
    }
    
    void Shutdown() {
        g_DebugLog("SimpleGraphics: Shutting down");
        m_initialized = false;
    }
    
    const char* GetName() const {
        return "SimpleGraphics";
    }
    
    bool IsInitialized() const {
        return m_initialized;
    }
};

// Simple container class
class SimpleContainer {
private:
    SimpleBase* m_items[2];
    int m_count;
    bool m_initialized;
    
public:
    SimpleContainer() {
        m_count = 0;
        m_initialized = false;
        m_items[0] = NULL;
        m_items[1] = NULL;
    }
    
    ~SimpleContainer() {
        if (m_initialized) {
            Shutdown();
        }
    }
    
    bool Initialize() {
        g_DebugLog("SimpleContainer: Initializing");
        
        m_items[0] = new SimpleGraphics();
        m_count = 1;
        
        if (!m_items[0]->Initialize()) {
            g_DebugLog("ERROR: Failed to initialize item 0");
            return false;
        }
        
        m_initialized = true;
        g_DebugLog("SimpleContainer: Initialization complete");
        return true;
    }
    
    void Update() {
        if (!m_initialized) return;
        
        for (int i = 0; i < m_count; ++i) {
            if (m_items[i]) {
                m_items[i]->Update();
            }
        }
    }
    
    void Shutdown() {
        if (!m_initialized) return;
        
        g_DebugLog("SimpleContainer: Shutting down");
        
        for (int i = m_count - 1; i >= 0; --i) {
            if (m_items[i]) {
                m_items[i]->Shutdown();
                delete m_items[i];
                m_items[i] = NULL;
            }
        }
        
        m_count = 0;
        m_initialized = false;
        g_DebugLog("SimpleContainer: Shutdown complete");
    }
    
    bool IsInitialized() const {
        return m_initialized;
    }
    
    int GetCount() const {
        return m_count;
    }
};

// Test basic compilation functionality
ENGINE_API int TestBasicCompilation() {
    g_DebugLog("Starting basic compilation test...");
    
    // Test 1: Basic structure
    g_DebugLog("=== Test 1: Basic Structure ===");
    
    SimpleConfig config;
    config.width = 800;
    config.height = 600;
    config.volume = 75;
    
    if (config.width != 800 || config.height != 600 || config.volume != 75) {
        g_DebugLog("ERROR: Structure assignment failed");
        return -1;
    }
    
    g_DebugLog("Structure test passed");
    
    // Test 2: Basic class
    g_DebugLog("=== Test 2: Basic Class ===");
    
    SimpleGraphics graphics;
    
    if (graphics.IsInitialized()) {
        g_DebugLog("ERROR: Graphics should not be initialized initially");
        return -2;
    }
    
    if (!graphics.Initialize()) {
        g_DebugLog("ERROR: Graphics initialization failed");
        return -3;
    }
    
    if (!graphics.IsInitialized()) {
        g_DebugLog("ERROR: Graphics should be initialized");
        return -4;
    }
    
    graphics.Update();
    graphics.Shutdown();
    
    if (graphics.IsInitialized()) {
        g_DebugLog("ERROR: Graphics should not be initialized after shutdown");
        return -5;
    }
    
    g_DebugLog("Basic class test passed");
    
    // Test 3: Container class
    g_DebugLog("=== Test 3: Container Class ===");
    
    SimpleContainer container;
    
    if (container.IsInitialized()) {
        g_DebugLog("ERROR: Container should not be initialized initially");
        return -6;
    }
    
    if (!container.Initialize()) {
        g_DebugLog("ERROR: Container initialization failed");
        return -7;
    }
    
    if (!container.IsInitialized()) {
        g_DebugLog("ERROR: Container should be initialized");
        return -8;
    }
    
    if (container.GetCount() != 1) {
        g_DebugLog("ERROR: Container should have 1 item, got %d", container.GetCount());
        return -9;
    }
    
    container.Update();
    container.Shutdown();
    
    if (container.IsInitialized()) {
        g_DebugLog("ERROR: Container should not be initialized after shutdown");
        return -10;
    }
    
    g_DebugLog("Container class test passed");
    
    // Test 4: Hash function
    g_DebugLog("=== Test 4: Hash Function ===");
    
    DWORD hash1 = SimpleHash("test");
    DWORD hash2 = SimpleHash("test");
    DWORD hash3 = SimpleHash("different");
    
    if (hash1 != hash2) {
        g_DebugLog("ERROR: Hash function inconsistent");
        return -11;
    }
    
    if (hash1 == hash3) {
        g_DebugLog("WARNING: Hash collision detected (rare but possible)");
    }
    
    g_DebugLog("Hash function test passed: hash1=%u, hash3=%u", hash1, hash3);
    
    // Test 5: Multiple lifecycle
    g_DebugLog("=== Test 5: Multiple Lifecycle ===");
    
    for (int i = 0; i < 3; ++i) {
        SimpleContainer testContainer;
        
        if (!testContainer.Initialize()) {
            g_DebugLog("ERROR: Container %d initialization failed", i);
            return -12;
        }
        
        testContainer.Update();
        testContainer.Shutdown();
        
        g_DebugLog("Lifecycle %d completed", i + 1);
    }
    
    g_DebugLog("Multiple lifecycle test passed");
    
    g_DebugLog("=== All basic compilation tests passed! ===");
    return 0;
}

// Test main function
#ifdef _STANDALONE
int main() {
    g_DebugLog("Starting basic compilation test suite...");
    
    int result = TestBasicCompilation();
    
    if (result == 0) {
        printf("SUCCESS: All basic compilation tests passed!\n");
    } else {
        printf("FAILED: Basic compilation test failed with code %d\n", result);
    }
    
    return result;
}
#endif
