// Simple hash table test - verify basic hash table operations
#include "KWin32.h"
#include <map>
#include <vector>
#include <string>
#include <stdio.h>
#include <stdarg.h>

// Simple debug implementation for testing
void g_DebugLog(const char* fmt, ...) {
    char buffer[1024];
    va_list va;
    
    va_start(va, fmt);
    vsnprintf(buffer, sizeof(buffer), fmt, va);
    va_end(va);
    
    printf("[DEBUG] %s\n", buffer);
}

// Simple hash function
DWORD SimpleHash(const char* str) {
    if (!str) return 0;
    
    DWORD hash = 5381;
    while (*str) {
        hash = ((hash << 5) + hash) + *str;
        str++;
    }
    return hash;
}

// Simple hash table class
template<typename T>
class SimpleHashTable {
private:
    std::map<std::string, T> m_table;
    
public:
    SimpleHashTable() {}
    ~SimpleHashTable() { Clear(); }
    
    bool Add(const char* key, const T& value) {
        if (!key) return false;
        m_table[std::string(key)] = value;
        return true;
    }
    
    bool Find(const char* key, T& result) const {
        if (!key) return false;
        
        typename std::map<std::string, T>::const_iterator it = m_table.find(std::string(key));
        if (it != m_table.end()) {
            result = it->second;
            return true;
        }
        return false;
    }
    
    bool Contains(const char* key) const {
        if (!key) return false;
        return m_table.find(std::string(key)) != m_table.end();
    }
    
    bool Remove(const char* key) {
        if (!key) return false;
        
        typename std::map<std::string, T>::iterator it = m_table.find(std::string(key));
        if (it != m_table.end()) {
            m_table.erase(it);
            return true;
        }
        return false;
    }
    
    void Clear() {
        m_table.clear();
    }
    
    size_t Size() const {
        return m_table.size();
    }
    
    bool Empty() const {
        return m_table.empty();
    }
    
    std::vector<std::string> GetKeys() const {
        std::vector<std::string> keys;
        for (typename std::map<std::string, T>::const_iterator it = m_table.begin(); 
             it != m_table.end(); ++it) {
            keys.push_back(it->first);
        }
        return keys;
    }
    
    std::vector<T> GetValues() const {
        std::vector<T> values;
        for (typename std::map<std::string, T>::const_iterator it = m_table.begin(); 
             it != m_table.end(); ++it) {
            values.push_back(it->second);
        }
        return values;
    }
    
    void DebugPrint() const {
        g_DebugLog("SimpleHashTable: %d items", (int)Size());
        
        int count = 0;
        for (typename std::map<std::string, T>::const_iterator it = m_table.begin(); 
             it != m_table.end() && count < 10; ++it, ++count) {
            g_DebugLog("  [%d] '%s' (hash: %u)", count, it->first.c_str(), SimpleHash(it->first.c_str()));
        }
        
        if (Size() > 10) {
            g_DebugLog("  ... and %d more items", (int)Size() - 10);
        }
    }
};

// Test simple hash table functionality
ENGINE_API int TestSimpleHashTable() {
    g_DebugLog("Starting simple hash table test...");
    
    // Test 1: Basic operations
    g_DebugLog("=== Test 1: Basic Operations ===");
    
    SimpleHashTable<int> hashTable;
    
    // Test insertion
    if (!hashTable.Add("apple", 100)) {
        g_DebugLog("ERROR: Add failed for 'apple'");
        return -1;
    }
    
    if (!hashTable.Add("banana", 200)) {
        g_DebugLog("ERROR: Add failed for 'banana'");
        return -2;
    }
    
    if (!hashTable.Add("cherry", 300)) {
        g_DebugLog("ERROR: Add failed for 'cherry'");
        return -3;
    }
    
    if (!hashTable.Add("date", 400)) {
        g_DebugLog("ERROR: Add failed for 'date'");
        return -4;
    }
    
    g_DebugLog("Insertion passed: %d items", (int)hashTable.Size());
    
    // Test finding
    int result;
    if (!hashTable.Find("apple", result) || result != 100) {
        g_DebugLog("ERROR: Find failed for 'apple'");
        return -5;
    }
    
    if (!hashTable.Find("banana", result) || result != 200) {
        g_DebugLog("ERROR: Find failed for 'banana'");
        return -6;
    }
    
    if (hashTable.Find("orange", result)) {
        g_DebugLog("ERROR: Find should have failed for 'orange'");
        return -7;
    }
    
    g_DebugLog("Find operations passed");
    
    // Test contains
    if (!hashTable.Contains("cherry")) {
        g_DebugLog("ERROR: Contains failed for existing key");
        return -8;
    }
    
    if (hashTable.Contains("orange")) {
        g_DebugLog("ERROR: Contains should have failed for non-existing key");
        return -9;
    }
    
    g_DebugLog("Contains operations passed");
    
    // Test 2: Hash function
    g_DebugLog("=== Test 2: Hash Function ===");
    
    DWORD hash1 = SimpleHash("apple");
    DWORD hash2 = SimpleHash("apple");
    DWORD hash3 = SimpleHash("banana");
    
    if (hash1 != hash2) {
        g_DebugLog("ERROR: Hash function not consistent");
        return -10;
    }
    
    if (hash1 == hash3) {
        g_DebugLog("WARNING: Hash collision detected (this is normal but rare)");
    }
    
    g_DebugLog("Hash function passed: apple=%u, banana=%u", hash1, hash3);
    
    // Test 3: Key retrieval
    g_DebugLog("=== Test 3: Key Retrieval ===");
    
    std::vector<std::string> keys = hashTable.GetKeys();
    if (keys.size() != 4) {
        g_DebugLog("ERROR: Expected 4 keys, got %d", (int)keys.size());
        return -11;
    }
    
    // Check if all expected keys are present
    bool foundApple = false, foundBanana = false, foundCherry = false, foundDate = false;
    for (size_t i = 0; i < keys.size(); ++i) {
        if (keys[i] == "apple") foundApple = true;
        else if (keys[i] == "banana") foundBanana = true;
        else if (keys[i] == "cherry") foundCherry = true;
        else if (keys[i] == "date") foundDate = true;
    }
    
    if (!foundApple || !foundBanana || !foundCherry || !foundDate) {
        g_DebugLog("ERROR: Not all expected keys found");
        return -12;
    }
    
    g_DebugLog("Key retrieval passed");
    
    // Test 4: Value retrieval
    g_DebugLog("=== Test 4: Value Retrieval ===");
    
    std::vector<int> values = hashTable.GetValues();
    if (values.size() != 4) {
        g_DebugLog("ERROR: Expected 4 values, got %d", (int)values.size());
        return -13;
    }
    
    // Check if all expected values are present
    bool found100 = false, found200 = false, found300 = false, found400 = false;
    for (size_t i = 0; i < values.size(); ++i) {
        if (values[i] == 100) found100 = true;
        else if (values[i] == 200) found200 = true;
        else if (values[i] == 300) found300 = true;
        else if (values[i] == 400) found400 = true;
    }
    
    if (!found100 || !found200 || !found300 || !found400) {
        g_DebugLog("ERROR: Not all expected values found");
        return -14;
    }
    
    g_DebugLog("Value retrieval passed");
    
    // Test 5: Removal
    g_DebugLog("=== Test 5: Removal ===");
    
    if (!hashTable.Remove("banana")) {
        g_DebugLog("ERROR: Remove failed for existing key");
        return -15;
    }
    
    if (hashTable.Remove("orange")) {
        g_DebugLog("ERROR: Remove should have failed for non-existing key");
        return -16;
    }
    
    if (hashTable.Size() != 3) {
        g_DebugLog("ERROR: Size should be 3 after removal, got %d", (int)hashTable.Size());
        return -17;
    }
    
    if (hashTable.Contains("banana")) {
        g_DebugLog("ERROR: Key should not exist after removal");
        return -18;
    }
    
    g_DebugLog("Removal operations passed");
    
    // Test 6: Update existing key
    g_DebugLog("=== Test 6: Update Existing Key ===");
    
    if (!hashTable.Add("apple", 999)) {
        g_DebugLog("ERROR: Update failed for existing key");
        return -19;
    }
    
    if (!hashTable.Find("apple", result) || result != 999) {
        g_DebugLog("ERROR: Updated value not found. Got: %d", result);
        return -20;
    }
    
    if (hashTable.Size() != 3) {
        g_DebugLog("ERROR: Size should remain 3 after update, got %d", (int)hashTable.Size());
        return -21;
    }
    
    g_DebugLog("Update operations passed");
    
    // Test 7: Clear
    g_DebugLog("=== Test 7: Clear ===");
    
    hashTable.Clear();
    
    if (!hashTable.Empty()) {
        g_DebugLog("ERROR: Table should be empty after clear");
        return -22;
    }
    
    if (hashTable.Size() != 0) {
        g_DebugLog("ERROR: Size should be 0 after clear");
        return -23;
    }
    
    g_DebugLog("Clear operations passed");
    
    g_DebugLog("=== All simple hash table tests passed! ===");
    return 0;
}

// Test main function
#ifdef _STANDALONE
int main() {
    g_DebugLog("Starting simple hash table test suite...");
    
    int result = TestSimpleHashTable();
    
    if (result == 0) {
        printf("SUCCESS: All simple hash table tests passed!\n");
    } else {
        printf("FAILED: Simple hash table test failed with code %d\n", result);
    }
    
    return result;
}
#endif
