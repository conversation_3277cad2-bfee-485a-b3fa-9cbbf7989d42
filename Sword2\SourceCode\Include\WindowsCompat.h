#ifndef WINDOWS_COMPAT_H
#define WINDOWS_COMPAT_H

// Windows SDK兼容性头文件
// 解决新版Windows SDK与老代码的兼容性问题

// 强制定义这些宏来解决兼容性问题
#define WIN32_LEAN_AND_MEAN
#define NOMINMAX

// 包含Windows头文件
#include <winsock2.h>
#include <windows.h>
#include <ws2tcpip.h>

// 重新定义一些常用的宏，避免与STL冲突
#ifndef max
#define max(a,b) (((a) > (b)) ? (a) : (b))
#endif

#ifndef min
#define min(a,b) (((a) < (b)) ? (a) : (b))
#endif

// 解决stricmp等POSIX函数名称问题
#ifndef stricmp
#define stricmp _stricmp
#endif

#ifndef strnicmp
#define strnicmp _strnicmp
#endif

#ifndef strcmpi
#define strcmpi _strcmpi
#endif

#endif // WINDOWS_COMPAT_H
