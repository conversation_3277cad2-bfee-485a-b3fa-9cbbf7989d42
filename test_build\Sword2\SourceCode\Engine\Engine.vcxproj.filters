﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KEngine.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KWin32.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KWin32App.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KWin32Wnd.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\stdafx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KCanvas.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KDDraw.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KBitmap.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KBitmap16.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KBitmapConvert.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KSprite.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KSpriteCache.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KSpriteCodec.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KSpriteMaker.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KPalette.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KColors.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KDrawBase.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KDrawBitmap.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KDrawBitmap16.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KDrawSprite.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KDrawSpriteAlpha.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KDrawFade.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KDrawFont.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KDrawDotFont.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KDSound.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KMusic.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KWavSound.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KWavMusic.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KMp3Music.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KMp4Audio.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KMpgMusic.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KWavFile.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KWavCodec.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KDInput.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KKeyboard.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KMouse.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\Kime.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KFile.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KFilePath.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KFileDialog.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KFileCopy.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KPakFile.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KPakData.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KPakList.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KPakMake.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KPakTool.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KZipFile.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KZipData.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KZipList.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KZipCodec.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\XPackFile.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\ZPackFile.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\ZSPRPackFile.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KBmpFile.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KBmpFile24.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KJpgFile.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KGifFile.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KPcxFile.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KTgaFile32.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KMemBase.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KMemClass.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KMemClass1.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KMemManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KMemStack.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KList.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KNode.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KLinkArray.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KSafeList.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KSortList.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KHashList.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KHashNode.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KHashTable.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KBinTree.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KSortBinTree.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KFindBinTree.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KOctree.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KOctreeNode.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KStrBase.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KStrList.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KStrNode.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KSG_StringProcess.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KSG_MD5_String.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\md5.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KScript.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KScriptCache.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KScriptList.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KScriptSet.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KLuaScript.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KLuaScriptSet.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KStepLuaScript.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KEicScript.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KEicScriptSet.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\LuaFuns.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KNetClient.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KNetServer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KNetServerNode.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KNetThread.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KTimer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KRandom.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KDebug.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KEvent.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KMessage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KMsgNode.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KIniFile.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KTabFile.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KTabFileCtrl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KScanDir.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\Text.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KThread.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KMutex.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KAutoMutex.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KGraphics.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KImageRes.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KImageStore.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KFont.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KBmp2Spr.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KCodec.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KCodecLha.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KCodecLzo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KCache.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KSoundCache.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KVideo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KAviFile.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KMp4Movie.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KMp4Video.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KPolygon.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KPolyRelation.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KDError.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KLubCmpl_Blocker.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KCriticalSection.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KEngine.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KWin32.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KWin32App.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KWin32Wnd.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KCanvas.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KDDraw.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KBitmap.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KBitmap16.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KSprite.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KDrawBase.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KDrawSprite.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KDSound.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KMusic.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KWavSound.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KMp3Music.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KDInput.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KKeyboard.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KMouse.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KFile.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KFilePath.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KPakFile.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KMemBase.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KList.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KTimer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KRandom.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KDebug.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KIniFile.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\Sword2Code\Sword2\SourceCode\Engine\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{5BBBD2BF-96B6-3EE1-8EBA-AFDC2DC00F3A}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{44AB8148-4A3D-32F7-9B57-640603BD4364}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
