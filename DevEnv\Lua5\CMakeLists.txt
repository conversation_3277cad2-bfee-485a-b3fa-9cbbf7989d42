# Lua 5.1 CMake配置

# Lua核心源文件
set(LUA_CORE_SOURCES
    src/lapi.c
    src/lcode.c
    src/ldebug.c
    src/ldo.c
    src/ldump.c
    src/lfunc.c
    src/lgc.c
    src/llex.c
    src/lmem.c
    src/lobject.c
    src/lopcodes.c
    src/lparser.c
    src/lstate.c
    src/lstring.c
    src/ltable.c
    src/ltm.c
    src/lundump.c
    src/lvm.c
    src/lzio.c
)

# Lua库源文件
set(LUA_LIB_SOURCES
    src/lauxlib.c
    src/lbaselib.c
    src/ldblib.c
    src/liolib.c
    src/lmathlib.c
    src/loslib.c
    src/ltablib.c
    src/lstrlib.c
    src/loadlib.c
    src/linit.c
)

# Lua头文件 (从DevEnv/Include/Lua5目录)
set(LUA_HEADERS
    ${CMAKE_SOURCE_DIR}/DevEnv/Include/Lua5/lua.h
    ${CMAKE_SOURCE_DIR}/DevEnv/Include/Lua5/luaconf.h
    ${CMAKE_SOURCE_DIR}/DevEnv/Include/Lua5/lualib.h
    ${CMAKE_SOURCE_DIR}/DevEnv/Include/Lua5/lauxlib.h
    ${CMAKE_SOURCE_DIR}/DevEnv/Include/Lua5/lapi.h
    ${CMAKE_SOURCE_DIR}/DevEnv/Include/Lua5/lcode.h
    ${CMAKE_SOURCE_DIR}/DevEnv/Include/Lua5/ldebug.h
    ${CMAKE_SOURCE_DIR}/DevEnv/Include/Lua5/ldo.h
    ${CMAKE_SOURCE_DIR}/DevEnv/Include/Lua5/lfunc.h
    ${CMAKE_SOURCE_DIR}/DevEnv/Include/Lua5/lgc.h
    ${CMAKE_SOURCE_DIR}/DevEnv/Include/Lua5/llex.h
    ${CMAKE_SOURCE_DIR}/DevEnv/Include/Lua5/lmem.h
    ${CMAKE_SOURCE_DIR}/DevEnv/Include/Lua5/lobject.h
    ${CMAKE_SOURCE_DIR}/DevEnv/Include/Lua5/lopcodes.h
    ${CMAKE_SOURCE_DIR}/DevEnv/Include/Lua5/lparser.h
    ${CMAKE_SOURCE_DIR}/DevEnv/Include/Lua5/lstate.h
    ${CMAKE_SOURCE_DIR}/DevEnv/Include/Lua5/lstring.h
    ${CMAKE_SOURCE_DIR}/DevEnv/Include/Lua5/ltable.h
    ${CMAKE_SOURCE_DIR}/DevEnv/Include/Lua5/ltm.h
    ${CMAKE_SOURCE_DIR}/DevEnv/Include/Lua5/lundump.h
    ${CMAKE_SOURCE_DIR}/DevEnv/Include/Lua5/lvm.h
    ${CMAKE_SOURCE_DIR}/DevEnv/Include/Lua5/lzio.h
)

# 创建Lua静态库
add_library(Lua5 STATIC ${LUA_CORE_SOURCES} ${LUA_LIB_SOURCES} ${LUA_HEADERS})

# 设置目标属性
set_target_properties(Lua5 PROPERTIES
    OUTPUT_NAME "Lua5"
    DEBUG_POSTFIX "D"
    C_STANDARD 99
    C_STANDARD_REQUIRED ON
)

# 包含目录
target_include_directories(Lua5 PUBLIC
    ${CMAKE_SOURCE_DIR}/DevEnv/Include/Lua5
    ${CMAKE_CURRENT_SOURCE_DIR}/src
)

# 预处理器定义
target_compile_definitions(Lua5 PRIVATE
    $<$<CONFIG:Debug>:_DEBUG>
    $<$<CONFIG:Release>:NDEBUG>
)

if(WIN32)
    target_compile_definitions(Lua5 PRIVATE
        WIN32
        _WINDOWS
        _CRT_SECURE_NO_WARNINGS
    )
else()
    target_compile_definitions(Lua5 PRIVATE
        __linux
        _SERVER
        _STANDALONE
        C_C_VERSION
        LUA_USE_LINUX
    )
    
    # Linux特定的链接库
    target_link_libraries(Lua5 PRIVATE
        m
        dl
    )
endif()

# 编译选项
if(MSVC)
    target_compile_options(Lua5 PRIVATE
        /W3
        $<$<CONFIG:Debug>:/Od /Zi>
        $<$<CONFIG:Release>:/O2>
    )
else()
    target_compile_options(Lua5 PRIVATE
        -Wall
        -Wextra
        $<$<CONFIG:Debug>:-g -O0>
        $<$<CONFIG:Release>:-O3>
    )
endif()

# 创建Lua解释器可执行文件
add_executable(lua src/lua.c)
target_link_libraries(lua Lua5)

if(NOT WIN32)
    target_link_libraries(lua readline history ncurses)
endif()

set_target_properties(lua PROPERTIES
    OUTPUT_NAME "lua"
    DEBUG_POSTFIX "D"
)

# 创建Lua编译器可执行文件
add_executable(luac src/luac.c src/print.c)
target_link_libraries(luac Lua5)

set_target_properties(luac PROPERTIES
    OUTPUT_NAME "luac"
    DEBUG_POSTFIX "D"
)

# 安装配置
install(TARGETS Lua5 lua luac
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)

install(FILES 
    src/lua.h
    src/luaconf.h
    src/lualib.h
    src/lauxlib.h
    DESTINATION include/lua5.1
)

# 创建pkg-config文件
if(NOT WIN32)
    configure_file(
        ${CMAKE_CURRENT_SOURCE_DIR}/etc/lua.pc.in
        ${CMAKE_CURRENT_BINARY_DIR}/lua5.1.pc
        @ONLY
    )
    
    install(FILES ${CMAKE_CURRENT_BINARY_DIR}/lua5.1.pc
        DESTINATION lib/pkgconfig
    )
endif()

# 显示Lua配置信息
message(STATUS "Lua5.1 configuration:")
message(STATUS "  Version: 5.1")
message(STATUS "  Sources: ${CMAKE_CURRENT_LIST_DIR}")
message(STATUS "  Platform: ${CMAKE_SYSTEM_NAME}")

# 测试目标
if(BUILD_TESTS)
    enable_testing()
    
    # 基本Lua测试
    add_test(NAME lua_basic_test
        COMMAND lua -e "print('Hello from Lua!')"
    )
    
    # Lua编译测试
    add_test(NAME luac_compile_test
        COMMAND luac -o test.luac -
        INPUT_FILE ${CMAKE_CURRENT_SOURCE_DIR}/test/hello.lua
    )
endif()
