// Filesystem modernization test - verify KFile_simple
#include "KWin32.h"

// Forward declarations to avoid conflicts
void g_DebugLog(LPSTR Fmt, ...);

#include "KFile_simple.h"
#include <string>
#include <stdio.h>
#include <stdarg.h>

// Simple debug implementation for testing
void g_DebugLog(LPSTR Fmt, ...) {
    char buffer[1024];
    va_list va;

    va_start(va, Fmt);
    vsnprintf(buffer, sizeof(buffer), Fmt, va);
    va_end(va);

    printf("[DEBUG] %s\n", buffer);
}

// Test filesystem functionality
ENGINE_API int TestFileSystem() {
    g_DebugLog("Starting filesystem modernization test...");

    // Test 1: File creation and writing
    g_DebugLog("=== Test 1: File Creation and Writing ===");

    KFile testFile;
    if (!testFile.Create("sample.txt")) {
        g_DebugLog("ERROR: Failed to create test file");
        return -1;
    }
    
    // Write test data
    const char* testData = "Hello, Modern File System!\nThis is a test of the modernized KFile implementation.\n";
    DWORD bytesWritten = testFile.Write((LPVOID)testData, strlen(testData));
    
    if (bytesWritten != strlen(testData)) {
        g_DebugLog("ERROR: Write failed. Expected %d bytes, wrote %d bytes", 
                  (int)strlen(testData), bytesWritten);
        testFile.Close();
        return -2;
    }
    
    g_DebugLog("Successfully wrote %d bytes to file", bytesWritten);
    testFile.Close();
    
    // Test 2: File reading
    g_DebugLog("=== Test 2: File Reading ===");

    if (!testFile.Open("sample.txt")) {
        g_DebugLog("ERROR: Failed to open test file for reading");
        return -3;
    }
    
    DWORD fileSize = testFile.Size();
    g_DebugLog("File size: %d bytes", fileSize);
    
    if (fileSize != strlen(testData)) {
        g_DebugLog("ERROR: File size mismatch. Expected %d, got %d", 
                  (int)strlen(testData), fileSize);
        testFile.Close();
        return -4;
    }
    
    // Read data back
    char readBuffer[1024];
    ZeroMemory(readBuffer, sizeof(readBuffer));
    
    DWORD bytesRead = testFile.Read(readBuffer, fileSize);
    if (bytesRead != fileSize) {
        g_DebugLog("ERROR: Read failed. Expected %d bytes, read %d bytes", 
                  fileSize, bytesRead);
        testFile.Close();
        return -5;
    }
    
    // Verify data
    if (strncmp(readBuffer, testData, fileSize) != 0) {
        g_DebugLog("ERROR: Data verification failed");
        g_DebugLog("Expected: %s", testData);
        g_DebugLog("Got: %s", readBuffer);
        testFile.Close();
        return -6;
    }
    
    g_DebugLog("Successfully read and verified %d bytes", bytesRead);
    testFile.Close();
    
    // Test 3: File seeking
    g_DebugLog("=== Test 3: File Seeking ===");

    if (!testFile.Open("sample.txt")) {
        g_DebugLog("ERROR: Failed to open test file for seeking test");
        return -7;
    }
    
    // Seek to middle of file
    DWORD seekPos = testFile.Seek(10, FILE_BEGIN);
    if (seekPos == SEEK_ERROR) {
        g_DebugLog("ERROR: Seek failed");
        testFile.Close();
        return -8;
    }
    
    DWORD currentPos = testFile.Tell();
    if (currentPos != 10) {
        g_DebugLog("ERROR: Position mismatch after seek. Expected 10, got %d", currentPos);
        testFile.Close();
        return -9;
    }
    
    g_DebugLog("Seek test passed. Position: %d", currentPos);
    testFile.Close();
    
    g_DebugLog("Basic file operations tests completed successfully");

    g_DebugLog("=== All basic file system tests passed! ===");
    return 0;
}

// Test main function
#ifdef _STANDALONE
int main() {
    g_DebugLog("Starting filesystem modernization test suite...");
    
    int result = TestFileSystem();
    
    if (result == 0) {
        printf("SUCCESS: All filesystem tests passed!\n");
    } else {
        printf("FAILED: Filesystem test failed with code %d\n", result);
    }
    
    return result;
}
#endif
