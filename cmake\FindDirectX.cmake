# FindDirectX.cmake
# 查找DirectX SDK
#
# 定义的变量:
#   DIRECTX_FOUND - 如果找到DirectX则为TRUE
#   DIRECTX_INCLUDE_DIRS - DirectX头文件目录
#   DIRECTX_LIBRARIES - DirectX库文件
#   DIRECTX_D3D9_LIBRARY - Direct3D 9库
#   DIRECTX_DDRAW_LIBRARY - DirectDraw库
#   DIRECTX_DSOUND_LIBRARY - DirectSound库
#   DIRECTX_DINPUT_LIBRARY - DirectInput库

if(WIN32)
    # 查找DirectX头文件
    find_path(DIRECTX_INCLUDE_DIR
        NAMES d3d9.h
        PATHS
            ${CMAKE_SOURCE_DIR}/DevEnv/dx9/inc
            ${CMAKE_SOURCE_DIR}/DevEnv/dx9/Include
            "$ENV{DXSDK_DIR}/Include"
            "$ENV{PROGRAMFILES}/Microsoft DirectX SDK*/Include"
            "$ENV{ProgramFiles\(x86\)}/Microsoft DirectX SDK*/Include"
        DOC "DirectX include directory"
    )
    
    # 查找DirectX库文件目录
    if(CMAKE_SIZEOF_VOID_P EQUAL 8)
        set(DIRECTX_ARCH "x64")
    else()
        set(DIRECTX_ARCH "x86")
    endif()
    
    find_path(DIRECTX_LIBRARY_DIR
        NAMES d3d9.lib
        PATHS
            ${CMAKE_SOURCE_DIR}/DevEnv/dx9/lib/release
            ${CMAKE_SOURCE_DIR}/DevEnv/dx9/lib/debug
            ${CMAKE_SOURCE_DIR}/DevEnv/dx9/lib
            ${CMAKE_SOURCE_DIR}/DevEnv/dx9/Lib/${DIRECTX_ARCH}
            ${CMAKE_SOURCE_DIR}/DevEnv/dx9/Lib
            "$ENV{DXSDK_DIR}/Lib/${DIRECTX_ARCH}"
            "$ENV{DXSDK_DIR}/Lib"
            "$ENV{PROGRAMFILES}/Microsoft DirectX SDK*/Lib/${DIRECTX_ARCH}"
            "$ENV{ProgramFiles\(x86\)}/Microsoft DirectX SDK*/Lib/${DIRECTX_ARCH}"
        DOC "DirectX library directory"
    )
    
    # 查找各个DirectX库
    find_library(DIRECTX_D3D9_LIBRARY
        NAMES d3d9
        PATHS ${DIRECTX_LIBRARY_DIR}
        DOC "Direct3D 9 library"
    )
    
    find_library(DIRECTX_DDRAW_LIBRARY
        NAMES ddraw
        PATHS ${DIRECTX_LIBRARY_DIR}
        DOC "DirectDraw library"
    )
    
    find_library(DIRECTX_DSOUND_LIBRARY
        NAMES dsound
        PATHS ${DIRECTX_LIBRARY_DIR}
        DOC "DirectSound library"
    )
    
    find_library(DIRECTX_DINPUT_LIBRARY
        NAMES dinput8
        PATHS ${DIRECTX_LIBRARY_DIR}
        DOC "DirectInput library"
    )
    
    find_library(DIRECTX_DXGUID_LIBRARY
        NAMES dxguid
        PATHS ${DIRECTX_LIBRARY_DIR}
        DOC "DirectX GUID library"
    )
    
    # 设置变量
    set(DIRECTX_INCLUDE_DIRS ${DIRECTX_INCLUDE_DIR})
    set(DIRECTX_LIBRARIES
        ${DIRECTX_D3D9_LIBRARY}
        ${DIRECTX_DDRAW_LIBRARY}
        ${DIRECTX_DSOUND_LIBRARY}
        ${DIRECTX_DINPUT_LIBRARY}
        ${DIRECTX_DXGUID_LIBRARY}
    )
    
    # 移除空值
    list(REMOVE_ITEM DIRECTX_LIBRARIES "")
    
    # 检查是否找到
    include(FindPackageHandleStandardArgs)
    find_package_handle_standard_args(DirectX
        REQUIRED_VARS DIRECTX_INCLUDE_DIR DIRECTX_D3D9_LIBRARY
        FAIL_MESSAGE "DirectX SDK not found. Please install DirectX SDK or set DXSDK_DIR environment variable."
    )
    
    # 创建导入目标
    if(DIRECTX_FOUND AND NOT TARGET DirectX::DirectX)
        add_library(DirectX::DirectX INTERFACE IMPORTED)
        set_target_properties(DirectX::DirectX PROPERTIES
            INTERFACE_INCLUDE_DIRECTORIES "${DIRECTX_INCLUDE_DIRS}"
            INTERFACE_LINK_LIBRARIES "${DIRECTX_LIBRARIES}"
        )
    endif()
    
    # 标记为高级变量
    mark_as_advanced(
        DIRECTX_INCLUDE_DIR
        DIRECTX_LIBRARY_DIR
        DIRECTX_D3D9_LIBRARY
        DIRECTX_DDRAW_LIBRARY
        DIRECTX_DSOUND_LIBRARY
        DIRECTX_DINPUT_LIBRARY
        DIRECTX_DXGUID_LIBRARY
    )
    
else()
    # 非Windows平台
    set(DIRECTX_FOUND FALSE)
    message(STATUS "DirectX is only available on Windows")
endif()

# 显示结果
if(DIRECTX_FOUND)
    message(STATUS "Found DirectX:")
    message(STATUS "  Include dir: ${DIRECTX_INCLUDE_DIR}")
    message(STATUS "  Libraries: ${DIRECTX_LIBRARIES}")
else()
    message(STATUS "DirectX not found")
endif()
