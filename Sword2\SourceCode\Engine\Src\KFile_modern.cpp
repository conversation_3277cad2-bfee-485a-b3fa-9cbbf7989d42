//---------------------------------------------------------------------------
// Sword3 Engine - Modernized File System Implementation
// 现代化的文件系统实现
//---------------------------------------------------------------------------
#include "KFile_modern.h"
#include "KFilePath.h"
#include "KDebug.h"
#include <iostream>

//---------------------------------------------------------------------------
// Constructor
//---------------------------------------------------------------------------
KFile::KFile() {
    m_pFile = nullptr;
    m_dwLen = 0;
    m_dwPos = 0;
}

//---------------------------------------------------------------------------
// Destructor
//---------------------------------------------------------------------------
KFile::~KFile() {
    Close();
}

//---------------------------------------------------------------------------
// Open file for reading
//---------------------------------------------------------------------------
BOOL KFile::Open(LPSTR FileName) {
    char PathName[MAXPATH];
    
    // Close any existing file
    if (m_pFile && m_pFile->is_open()) {
        Close();
    }
    
    // Get full path name using existing function
    g_GetFullPath(PathName, FileName);
    
    try {
        // 现代化：使用std::filesystem::path
        m_filePath = std::filesystem::path(PathName);
        
        // Check if file exists
        if (!std::filesystem::exists(m_filePath)) {
            g_DebugLog("File does not exist: %s", PathName);
            return FALSE;
        }
        
        // 现代化：使用std::fstream
        m_pFile = std::make_unique<std::fstream>();
        m_pFile->open(m_filePath, std::ios::in | std::ios::binary);
        
        if (!m_pFile->is_open()) {
            g_DebugLog("Failed to open file: %s", PathName);
            m_pFile.reset();
            return FALSE;
        }
        
        // Update file size and reset position
        UpdateFileSize();
        m_dwPos = 0;
        
        g_DebugLog("Successfully opened file: %s (size: %d bytes)", PathName, m_dwLen);
        return TRUE;
        
    } catch (const std::exception& e) {
        g_DebugLog("Exception opening file %s: %s", PathName, e.what());
        m_pFile.reset();
        return FALSE;
    }
}

//---------------------------------------------------------------------------
// Create file for writing
//---------------------------------------------------------------------------
BOOL KFile::Create(LPSTR FileName) {
    char PathName[MAXPATH];
    
    // Close any existing file
    if (m_pFile && m_pFile->is_open()) {
        Close();
    }
    
    // Get full path name
    g_GetFullPath(PathName, FileName);
    
    try {
        // 现代化：使用std::filesystem::path
        m_filePath = std::filesystem::path(PathName);
        
        // Create directory if it doesn't exist
        std::filesystem::path parentPath = m_filePath.parent_path();
        if (!parentPath.empty() && !std::filesystem::exists(parentPath)) {
            std::filesystem::create_directories(parentPath);
        }
        
        // 现代化：使用std::fstream
        m_pFile = std::make_unique<std::fstream>();
        m_pFile->open(m_filePath, std::ios::out | std::ios::binary | std::ios::trunc);
        
        if (!m_pFile->is_open()) {
            g_DebugLog("Failed to create file: %s", PathName);
            m_pFile.reset();
            return FALSE;
        }
        
        // Initialize file size and position
        m_dwLen = 0;
        m_dwPos = 0;
        
        g_DebugLog("Successfully created file: %s", PathName);
        return TRUE;
        
    } catch (const std::exception& e) {
        g_DebugLog("Exception creating file %s: %s", PathName, e.what());
        m_pFile.reset();
        return FALSE;
    }
}

//---------------------------------------------------------------------------
// Open file for appending
//---------------------------------------------------------------------------
BOOL KFile::Append(LPSTR FileName) {
    char PathName[MAXPATH];
    
    // Close any existing file
    if (m_pFile && m_pFile->is_open()) {
        Close();
    }
    
    // Get full path name
    g_GetFullPath(PathName, FileName);
    
    try {
        // 现代化：使用std::filesystem::path
        m_filePath = std::filesystem::path(PathName);
        
        // Create directory if it doesn't exist
        std::filesystem::path parentPath = m_filePath.parent_path();
        if (!parentPath.empty() && !std::filesystem::exists(parentPath)) {
            std::filesystem::create_directories(parentPath);
        }
        
        // 现代化：使用std::fstream
        m_pFile = std::make_unique<std::fstream>();
        m_pFile->open(m_filePath, std::ios::out | std::ios::binary | std::ios::app);
        
        if (!m_pFile->is_open()) {
            g_DebugLog("Failed to open file for append: %s", PathName);
            m_pFile.reset();
            return FALSE;
        }
        
        // Update file size and set position to end
        UpdateFileSize();
        m_dwPos = m_dwLen;
        
        g_DebugLog("Successfully opened file for append: %s (size: %d bytes)", PathName, m_dwLen);
        return TRUE;
        
    } catch (const std::exception& e) {
        g_DebugLog("Exception opening file for append %s: %s", PathName, e.what());
        m_pFile.reset();
        return FALSE;
    }
}

//---------------------------------------------------------------------------
// Close file
//---------------------------------------------------------------------------
void KFile::Close() {
    if (m_pFile && m_pFile->is_open()) {
        m_pFile->close();
    }
    m_pFile.reset();
    m_dwLen = 0;
    m_dwPos = 0;
    m_filePath.clear();
}

//---------------------------------------------------------------------------
// Update file size using modern filesystem
//---------------------------------------------------------------------------
void KFile::UpdateFileSize() {
    try {
        if (std::filesystem::exists(m_filePath)) {
            auto fileSize = std::filesystem::file_size(m_filePath);
            m_dwLen = static_cast<DWORD>(fileSize);
        } else {
            m_dwLen = 0;
        }
    } catch (const std::exception& e) {
        g_DebugLog("Exception getting file size: %s", e.what());
        m_dwLen = 0;
    }
}

//---------------------------------------------------------------------------
// Check if file is open
//---------------------------------------------------------------------------
bool KFile::IsOpen() const {
    return m_pFile && m_pFile->is_open();
}

//---------------------------------------------------------------------------
// Check if at end of file
//---------------------------------------------------------------------------
bool KFile::IsEOF() const {
    return m_pFile ? m_pFile->eof() : true;
}

//---------------------------------------------------------------------------
// Get current file size
//---------------------------------------------------------------------------
DWORD KFile::Size() {
    if (IsOpen()) {
        UpdateFileSize();
    }
    return m_dwLen;
}

//---------------------------------------------------------------------------
// Get current file position
//---------------------------------------------------------------------------
DWORD KFile::Tell() {
    return m_dwPos;
}

//---------------------------------------------------------------------------
// Read data from file
//---------------------------------------------------------------------------
DWORD KFile::Read(LPVOID lpBuffer, DWORD dwReadBytes) {
    if (!IsOpen() || !lpBuffer || dwReadBytes == 0) {
        return 0;
    }

    try {
        // Read data using modern stream
        m_pFile->read(static_cast<char*>(lpBuffer), dwReadBytes);

        // Get actual bytes read
        DWORD bytesRead = static_cast<DWORD>(m_pFile->gcount());

        // Update position
        m_dwPos += bytesRead;

        return bytesRead;

    } catch (const std::exception& e) {
        g_DebugLog("Exception reading file: %s", e.what());
        return 0;
    }
}

//---------------------------------------------------------------------------
// Write data to file
//---------------------------------------------------------------------------
DWORD KFile::Write(LPVOID lpBuffer, DWORD dwWriteBytes) {
    if (!IsOpen() || !lpBuffer || dwWriteBytes == 0) {
        return 0;
    }

    try {
        // Write data using modern stream
        m_pFile->write(static_cast<const char*>(lpBuffer), dwWriteBytes);

        if (m_pFile->good()) {
            // Update position and file size
            m_dwPos += dwWriteBytes;
            if (m_dwPos > m_dwLen) {
                m_dwLen = m_dwPos;
            }
            return dwWriteBytes;
        } else {
            g_DebugLog("Error writing to file");
            return 0;
        }

    } catch (const std::exception& e) {
        g_DebugLog("Exception writing file: %s", e.what());
        return 0;
    }
}

//---------------------------------------------------------------------------
// Seek to position in file
//---------------------------------------------------------------------------
DWORD KFile::Seek(LONG lDistance, DWORD dwMoveMethod) {
    if (!IsOpen()) {
        return SEEK_ERROR;
    }

    try {
        std::ios::seekdir seekDir;

        // Convert Windows seek method to std::ios seekdir
        switch (dwMoveMethod) {
            case FILE_BEGIN:
                seekDir = std::ios::beg;
                break;
            case FILE_CURRENT:
                seekDir = std::ios::cur;
                break;
            case FILE_END:
                seekDir = std::ios::end;
                break;
            default:
                return SEEK_ERROR;
        }

        // Perform seek operation
        m_pFile->seekg(lDistance, seekDir);
        m_pFile->seekp(lDistance, seekDir);

        if (m_pFile->good()) {
            // Update position
            std::streampos pos = m_pFile->tellg();
            m_dwPos = static_cast<DWORD>(pos);
            return m_dwPos;
        } else {
            g_DebugLog("Error seeking in file");
            return SEEK_ERROR;
        }

    } catch (const std::exception& e) {
        g_DebugLog("Exception seeking file: %s", e.what());
        return SEEK_ERROR;
    }
}
