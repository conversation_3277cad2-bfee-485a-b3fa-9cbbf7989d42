﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{9964A574-EC5B-3E2E-9EC4-5382C57643AF}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>DawnClient</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\Sword2Code\test_build\bin\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">DawnClient.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">SO2ClientD</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\Sword2Code\test_build\bin\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">DawnClient.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">SO2Client</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\Sword2Code\test_build\bin\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">DawnClient.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">SO2Client</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\Sword2Code\test_build\bin\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">DawnClient.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">SO2Client</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\Include;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui;D:\Sword2Code\Sword2\SourceCode\Include\Support;D:\Sword2Code\Sword2\SourceCode\Common\Support;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_DAWNCLIENT;_USRDLL;DAWNCLIENT_EXPORTS;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_DAWNCLIENT;_USRDLL;DAWNCLIENT_EXPORTS;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\Include;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui;D:\Sword2Code\Sword2\SourceCode\Include\Support;D:\Sword2Code\Sword2\SourceCode\Common\Support;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\Include;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui;D:\Sword2Code\Sword2\SourceCode\Include\Support;D:\Sword2Code\Sword2\SourceCode\Common\Support;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/Sword2Code/test_build/lib/Debug/SO2ClientD.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/Sword2Code/test_build/bin/Debug/SO2ClientD.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\Include;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui;D:\Sword2Code\Sword2\SourceCode\Include\Support;D:\Sword2Code\Sword2\SourceCode\Common\Support;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_DAWNCLIENT;_USRDLL;DAWNCLIENT_EXPORTS;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_DAWNCLIENT;_USRDLL;DAWNCLIENT_EXPORTS;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\Include;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui;D:\Sword2Code\Sword2\SourceCode\Include\Support;D:\Sword2Code\Sword2\SourceCode\Common\Support;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\Include;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui;D:\Sword2Code\Sword2\SourceCode\Include\Support;D:\Sword2Code\Sword2\SourceCode\Common\Support;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/Sword2Code/test_build/lib/Release/SO2Client.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/Sword2Code/test_build/bin/Release/SO2Client.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\Include;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui;D:\Sword2Code\Sword2\SourceCode\Include\Support;D:\Sword2Code\Sword2\SourceCode\Common\Support;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_DAWNCLIENT;_USRDLL;DAWNCLIENT_EXPORTS;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_DAWNCLIENT;_USRDLL;DAWNCLIENT_EXPORTS;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\Include;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui;D:\Sword2Code\Sword2\SourceCode\Include\Support;D:\Sword2Code\Sword2\SourceCode\Common\Support;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\Include;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui;D:\Sword2Code\Sword2\SourceCode\Include\Support;D:\Sword2Code\Sword2\SourceCode\Common\Support;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/Sword2Code/test_build/lib/MinSizeRel/SO2Client.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/Sword2Code/test_build/bin/MinSizeRel/SO2Client.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\Include;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui;D:\Sword2Code\Sword2\SourceCode\Include\Support;D:\Sword2Code\Sword2\SourceCode\Common\Support;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_DAWNCLIENT;_USRDLL;DAWNCLIENT_EXPORTS;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_DAWNCLIENT;_USRDLL;DAWNCLIENT_EXPORTS;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\Include;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui;D:\Sword2Code\Sword2\SourceCode\Include\Support;D:\Sword2Code\Sword2\SourceCode\Common\Support;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\Include;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui;D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui;D:\Sword2Code\Sword2\SourceCode\Include\Support;D:\Sword2Code\Sword2\SourceCode\Common\Support;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/Sword2Code/test_build/lib/RelWithDebInfo/SO2Client.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/Sword2Code/test_build/bin/RelWithDebInfo/SO2Client.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/Sword2Code/Sword2/SourceCode/Client/DawnClient/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/Sword2Code -BD:/Sword2Code/test_build --check-stamp-file D:/Sword2Code/test_build/Sword2/SourceCode/Client/DawnClient/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\Sword2Code\test_build\Sword2\SourceCode\Client\DawnClient\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/Sword2Code/Sword2/SourceCode/Client/DawnClient/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/Sword2Code -BD:/Sword2Code/test_build --check-stamp-file D:/Sword2Code/test_build/Sword2/SourceCode/Client/DawnClient/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\Sword2Code\test_build\Sword2\SourceCode\Client\DawnClient\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule D:/Sword2Code/Sword2/SourceCode/Client/DawnClient/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/Sword2Code -BD:/Sword2Code/test_build --check-stamp-file D:/Sword2Code/test_build/Sword2/SourceCode/Client/DawnClient/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\Sword2Code\test_build\Sword2\SourceCode\Client\DawnClient\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule D:/Sword2Code/Sword2/SourceCode/Client/DawnClient/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/Sword2Code -BD:/Sword2Code/test_build --check-stamp-file D:/Sword2Code/test_build/Sword2/SourceCode/Client/DawnClient/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\Sword2Code\test_build\Sword2\SourceCode\Client\DawnClient\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\DawnClient.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\DawnUi.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\GlobalSetting.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ErrorCode.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\stdafx.cpp" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ClientFStringTable.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ClientGStringTable.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\StringTable.h" />
    <ResourceCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\DawnClient.rc" />
    <ResourceCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\S3Client.rc" />
    <None Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\VersionNo.rc2">
    </None>
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\resource.h" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\GameAD.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\dawnuieditor.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\BufferPool.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\ExceptionManager.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\SimpleSymbolEngine.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\SpecialFuncs.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\WndCloseBtn.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\WndDropDownList.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\WndList3.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\autolocatewnd.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kboundslot.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kcolor.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kcompositewidget.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kconststringmanager.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kevent.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\keventset.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kfont.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kimage.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kimagepart.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kpicframe.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kprocessbar.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kproperty.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kpropertyhelper.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kpropertyset.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\krect.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\krichtext.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\krolemodel.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kshadow.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\ksimpletext.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\ksubscriberslot.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwebpage.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtbutton.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtcheckbox.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtedit.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgteffect.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgteffectbutton.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtimage.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtimagepart.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtitemlistbox.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtlabel.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtlabelbutton.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtlistitem.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtmatrix.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtmixtypesetting.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtmsglistbox.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtobjectbox.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtpicframe.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtprocessbar.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtrichtext.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtrichtextbox.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtrolemodel.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtscrollbar.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtsliderbar.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgttree.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgttreeitem.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwidget.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwidgetfactories.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwidgetfactorymanager.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwidgetmanage.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwndanimation.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwndie.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwndlayoutmanager.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\mousehover.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\popupmenu.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\regexpr2.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\syntax2.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\textpic.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\uicursor.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\uiimage.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\uistatus.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndHelpWnd.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndbutton.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndedit.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndimage.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndlabeledbutton.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndlist.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndmessagelistbox.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndobjcontainer.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wnds.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndscrollbar.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndtext.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndwindow.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\gamespacechangednotify.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\globaleventset.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\function_def\wndmanager_func.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\function_def\wndwindow_func.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua51_script_ui.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua_tinker.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\trace_dlg.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\luafunctor.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\uischeme_analyzer.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\window_alloc.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\window_event_scan.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\window_factor.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\window_manager.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\window_msg_filter.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\window_template.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\shortcutkey.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uibase.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\KMainSkillTable.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\KeySequence.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiAntiEnthrallment.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiBaguaCompose.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiBaguaDepose.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiBattleFieldInfo.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiBattleInfoFrame.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiBattlePlayerInfo.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiBattleRankInfo.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiBesetItem.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiBroadCastMessage.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiButtomIB.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiCampFrame.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiCampHero.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiCampInfo.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiChannelItemView.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiChatForbid.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiClewInfo.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiComposeItem.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiComposeLingShi.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiComposeProgress.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiComposeSkillBase.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiComposeSkillFrame.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiCooling.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiDiceWnd.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiECard.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiEKey.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiEnhanceItem.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiEpurateStone.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiEpurateStuff.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiFairy.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiFairyMode.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiFairyTalk.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiFightSkillFrame.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiFollowInfo.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiForceUnlock.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiGatherProgress.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiGestConvention.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiGestConventionFrame.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiGestConventionRank.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiGmPanelFrame.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiGmPanelPad.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiGutTalk.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiHealthGame.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiHelpPopTips.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiHitCount.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiIbFrame.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiIbNavigation.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiIbOtherType.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiIbPreview.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiIbResult.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiIbSilver.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiInformationTimeCount.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiInputPos.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiItemDetail.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiKeyboard.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiLoading.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiMainboardOptions.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiMessageBox.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiMonsterHandbook.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiMsgCentreBar.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiMsgCentrePad.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiMultiRoles.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiNewGather.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiNewsMessage.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiOptionFrame.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiParadeItem.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiPlayerAttribute.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiPlayerAutoEat.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiPlayerBaseInfo.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiPlayerBuff.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiPlayerInfoFrame.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiPlayerTreasure.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiQuickBar.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiRoleButton.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiSceneMessage.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiSecretSkillPad.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiShortcutKey.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiSkillKeySequence.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiSkillPadSwitch.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiSkillProgress.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiStall.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiStallItem.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiStallManage.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiStrengthRank.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTeamOverview.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTimeGuage.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTongCommonWnd.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTongFrame.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTongFuli.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTongManagement.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTongTech.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTransformItemAttr.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTrembleItem.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTrusteeship.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiAgreement.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiRoleStatus.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uibodymoveselector.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uibrifeskill.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uichatcentre.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uichatstatus.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiconnectinfo.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uidetailSkill.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiescdlg.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uifaceselector.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uigame.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uigamespaceshadow.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uigetmoney.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uigetstring.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiinit.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiitem.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uilearnskill.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uilifeskillframe.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uilogin.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiloginbg.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiminimap.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiminirolestatus.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uimsgsel.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uinewplayer.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uioptions.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiplayerbar.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiplayermingli.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiplayvideo.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiproduce.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uireconnect.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uirepair.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiselplayer.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiselserver.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uishop.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiskillframe.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uistorebox.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uisysmsgcentre.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uitaskaccept.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uitaskdatafile.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uitaskfinish.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uitasknote.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uitasktrace.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiteammanage.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uitrade.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uitradeconfirmwnd.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiworldmap.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uichatphrase.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uishell.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uisoundsetting.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\netconnect\netconnectagent.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\login\login.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\textctrlcmd\textctrlcmd.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\igw\UiIGW.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\Plugin\pluginmanager.cpp" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\DawnClient.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\DawnUi.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ErrorCode.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\GlobalSetting.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\Include\DawnUiPlugin.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\Include\GlobalSetting.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\Include\WndWindow.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\Include\dawnuieditor.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\Include\kgui.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\Include\pluginmanager.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\Include\singleton.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\Plugin\DawnUiPlugin.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\Plugin\pluginmanager.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\Stdafx.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\igw\SDOA4Client.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\igw\SDOADx9.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\igw\UiIGW.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\login\login.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\netconnect\netconnectagent.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\netconnect\netmsgtargetobject.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\textctrlcmd\textctrlcmd.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\GameAD.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\dawnuieditor.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\BufferPool.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\ExceptionManager.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\KLinerGradientControl.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\Link.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\ObjectPool.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\SimpleSymbolEngine.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\SpecialFuncs.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\WndCloseBtn.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\WndDropDownList.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\WndList3.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\autolocatewnd.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kboundslot.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kcolor.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kcompositewidget.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kconststringmanager.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kcoreeventargs.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kcountedptr.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kevent.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\keventargs.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\keventset.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kfont.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kfreefunctionslot.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kfunctorcopyslot.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kimage.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kimagepart.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kinputeventargs.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kmemberfunctionslot.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kpicframe.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kpoint.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kprerequisites.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kprocessbar.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kproperty.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kpropertyhelper.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kpropertyset.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\krect.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\krepresentitem.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\krichtext.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\krolemodel.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kshadow.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\ksimpletext.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\ksize.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kslotfunctorbase.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\ksubscriberslot.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kvector.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwebpage.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtbutton.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtcheckbox.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtedit.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgteffect.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgteffectbutton.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtimage.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtimagepart.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtitemlistbox.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtlabel.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtlabelbutton.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtlistitem.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtmatrix.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtmixtypesetting.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtmsglistbox.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtobjectbox.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtpicframe.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtprocessbar.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtrichtext.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtrichtextbox.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtrolemodel.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtscrollbar.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgtsliderbar.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgttree.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwgttreeitem.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwidget.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwidgetfactories.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwidgetfactorymanager.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwidgetfactorytempalte.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwidgetmanage.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwidgetproperties.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwndanimation.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwndie.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\kwndlayoutmanager.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\mousehover.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\popupmenu.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\regexpr2.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\reimpl2.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\restack.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\singleton.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\syntax2.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\textpic.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\uicursor.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\uiimage.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\uistatus.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\windowtemplate.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndHelpWnd.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndbutton.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wnddatastruct.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndedit.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndimage.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndlabeledbutton.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndlist.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndmessage.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndmessagelistbox.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndobjcontainer.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wnds.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndscrollbar.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndtext.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\elem\wndwindow.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\globaleventset.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\function_def\wndmanager_func.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\function_def\wndwindow_func.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua51_iscript_ui.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua51_script_ui.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\lapi.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\lauxlib.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\lcode.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\ldebug.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\ldo.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\lfunc.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\lgc.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\llex.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\llimits.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\lmem.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\lobject.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\lopcodes.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\lparser.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\lstate.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\lstring.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\ltable.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\ltm.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\lua.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\luaconf.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\lualib.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\lundump.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\lvm.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua\lzio.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\lua_tinker\lua_tinker.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\resource.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\stdafx.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\lua51_script_ui\trace_dlg.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\neodll.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\LuaMarco.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\iwindow_manager.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\luafunctor.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\swindow.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\uischeme_analyzer.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\window_alloc.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\window_event_scan.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\window_factor.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\window_manager.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\window_msg_crack.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\window_msg_filter.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\script_ui\window_template.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\shortcutkey.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uibase.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\KMainSkillTable.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\KeySequence.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UIMainboardOptions.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiAntiEnthrallment.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiBaguaCompose.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiBaguaDepose.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiBattleFieldInfo.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiBattleInfoFrame.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiBattleInfoPad.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiBattlePlayerInfo.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiBattleRankInfo.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiBesetItem.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiBroadCastMessage.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiButtomIB.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiCampFrame.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiCampHero.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiCampInfo.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiChannelItemView.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiChatForbid.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiClewInfo.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiComposeItem.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiComposeLingShi.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiComposeProgress.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiComposeSkillBase.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiComposeSkillFrame.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiCooling.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiDiceWnd.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiECard.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiEKey.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiEnhanceItem.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiEpurateStone.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiEpurateStuff.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiFairy.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiFairyMode.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiFairyTalk.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiFightSkillFrame.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiFollowInfo.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiForceUnlock.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiGatherProgress.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiGestConvention.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiGestConventionFrame.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiGestConventionRank.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiGestInfoPad.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiGmPanelFrame.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiGmPanelPad.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiGutTalk.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiHealthGame.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiHelpPopTips.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiHitCount.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiIbFrame.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiIbNavigation.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiIbOtherType.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiIbPreview.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiIbResult.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiIbSilver.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiInformationTimeCount.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiInputPos.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiItemDetail.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiKeyboard.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiLoading.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiMessageBox.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiMonsterHandbook.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiMsgCentreBar.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiMultiRoles.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiNewGather.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiNewsMessage.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiOptionFrame.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiParadeItem.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiPlayerAttribute.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiPlayerAutoEat.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiPlayerBaseInfo.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiPlayerBuff.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiPlayerInfoFrame.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiPlayerInfoPad.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiPlayerTreasure.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiQuickBar.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiRoleButton.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiRoleStatus.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiSceneMessage.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiSecretSkillPad.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiShortcutKey.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiSkillKeySequence.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiSkillPadSwitch.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiSkillProgress.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiStall.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiStallItem.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiStallManage.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiStrengthRank.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTeamOverview.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTimeGuage.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTongCommonWnd.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTongFrame.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTongFuli.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTongManagement.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTongTech.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTransformItemAttr.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTrembleItem.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\UiTrusteeship.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiAgreement.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uibodymoveselector.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uibrifeskill.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uichatcentre.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uichatstatus.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiconnectinfo.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uidetailskill.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiescdlg.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uifaceselector.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uigame.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uigamespaceshadow.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uigetmoney.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uigetstring.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiinit.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiitem.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uilearnskill.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uilifeskillframe.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uilogin.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiloginbg.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiminimap.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiminirolestatus.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uimsgcentrepad.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uimsgsel.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uinewplayer.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uioptions.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiplayerbar.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiplayermingli.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiplayvideo.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiproduce.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uireconnect.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uirepair.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiselplayer.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiselserver.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uishop.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiskillframe.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uistorebox.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uisysmsgcentre.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uitaskaccept.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uitaskdatafile.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uitaskfinish.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uitasknote.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uitasktrace.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiteammanage.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uitrade.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uitradeconfirmwnd.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uicase\uiworldmap.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uichatphrase.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uishell.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\ui\uisoundsetting.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\wxeditor\adjustproperty.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\wxeditor\editortouiimpl.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\wxeditor\globaldll.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\wxeditor\layertree.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\wxeditor\menuaction.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\wxeditor\namegenerator.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\wxeditor\qtpropertybrowser\qteditorfactory.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\wxeditor\qtpropertybrowser\qtpropertybrowser.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\wxeditor\qtpropertybrowser\qtpropertybrowserutils_p.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\wxeditor\qtpropertybrowser\qtpropertymanager.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\wxeditor\qtpropertybrowser\qttreepropertybrowser.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\wxeditor\qtpropertybrowser\qtvariantproperty.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\wxeditor\qtwinbridge\qmfcapp.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\wxeditor\qtwinbridge\qwinwidget.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\wxeditor\toolbox.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\wxeditor\widgetfileparser.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\wxeditor\widgetpropertysetting.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\wxeditor\wxdawnuiplugin.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\DawnClient\wxeditor\wxeditor.h" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\Sword2Code\test_build\ZERO_CHECK.vcxproj">
      <Project>{5056683F-7A79-357B-A3C6-E0F657A7870C}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>