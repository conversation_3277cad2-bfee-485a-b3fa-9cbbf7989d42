﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{E5089369-255E-3420-AD09-104F2959B72A}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>Stat</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\Sword2Code\test_build\lib\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Stat.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Stat</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\Sword2Code\test_build\lib\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Stat.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Stat</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\Sword2Code\test_build\lib\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Stat.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Stat</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\Sword2Code\test_build\lib\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Stat.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Stat</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.lib</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Common\Stat;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/Sword2Code/DevEnv/mysql/Include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;HAVE_MYSQL;_LIB;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;HAVE_MYSQL;_LIB;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Common\Stat;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;D:\Sword2Code\DevEnv\mysql\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Common\Stat;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;D:\Sword2Code\DevEnv\mysql\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Common\Stat;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/Sword2Code/DevEnv/mysql/Include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;HAVE_MYSQL;_LIB;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;HAVE_MYSQL;_LIB;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Common\Stat;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;D:\Sword2Code\DevEnv\mysql\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Common\Stat;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;D:\Sword2Code\DevEnv\mysql\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Common\Stat;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/Sword2Code/DevEnv/mysql/Include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;HAVE_MYSQL;_LIB;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;HAVE_MYSQL;_LIB;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Common\Stat;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;D:\Sword2Code\DevEnv\mysql\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Common\Stat;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;D:\Sword2Code\DevEnv\mysql\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Common\Stat;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/Sword2Code/DevEnv/mysql/Include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;HAVE_MYSQL;_LIB;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;HAVE_MYSQL;_LIB;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Common\Stat;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;D:\Sword2Code\DevEnv\mysql\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Common\Stat;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;D:\Sword2Code\DevEnv\mysql\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\Sword2Code\Sword2\SourceCode\Common\Stat\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/Sword2Code/Sword2/SourceCode/Common/Stat/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/Sword2Code -BD:/Sword2Code/test_build --check-stamp-file D:/Sword2Code/test_build/Sword2/SourceCode/Common/Stat/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\Sword2Code\test_build\Sword2\SourceCode\Common\Stat\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/Sword2Code/Sword2/SourceCode/Common/Stat/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/Sword2Code -BD:/Sword2Code/test_build --check-stamp-file D:/Sword2Code/test_build/Sword2/SourceCode/Common/Stat/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\Sword2Code\test_build\Sword2\SourceCode\Common\Stat\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule D:/Sword2Code/Sword2/SourceCode/Common/Stat/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/Sword2Code -BD:/Sword2Code/test_build --check-stamp-file D:/Sword2Code/test_build/Sword2/SourceCode/Common/Stat/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\Sword2Code\test_build\Sword2\SourceCode\Common\Stat\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule D:/Sword2Code/Sword2/SourceCode/Common/Stat/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/Sword2Code -BD:/Sword2Code/test_build --check-stamp-file D:/Sword2Code/test_build/Sword2/SourceCode/Common/Stat/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\Sword2Code\test_build\Sword2\SourceCode\Common\Stat\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Stat\Buffer.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Stat\Stat.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Stat\StatProcessBuyItem.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Stat\StatProcessCurrency.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Stat\StatProcessOnlines.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Stat\StatSettings.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Stat\stdafx.cpp" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Stat\Buffer.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Stat\KStatDef.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Stat\Stat.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Stat\StatProcessBuyItem.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Stat\StatProcessCurrency.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Stat\StatProcessOnlines.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Stat\StatSettings.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Stat\stat_i.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Common\Stat\stdafx.h" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\Sword2Code\test_build\ZERO_CHECK.vcxproj">
      <Project>{5056683F-7A79-357B-A3C6-E0F657A7870C}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\Sword2Code\test_build\Sword2\SourceCode\Common\Core\Core.vcxproj">
      <Project>{CFD13F0C-2493-3B4A-82F5-55C7C73BF55A}</Project>
      <Name>Core</Name>
    </ProjectReference>
    <ProjectReference Include="D:\Sword2Code\test_build\Sword2\SourceCode\Engine\Engine.vcxproj">
      <Project>{1946B23A-828A-3EB6-83E5-5A3787A50438}</Project>
      <Name>Engine</Name>
    </ProjectReference>
    <ProjectReference Include="D:\Sword2Code\test_build\DevEnv\Lua5\Lua5.vcxproj">
      <Project>{C9F953A3-9235-3E02-90C6-F745942FD865}</Project>
      <Name>Lua5</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>