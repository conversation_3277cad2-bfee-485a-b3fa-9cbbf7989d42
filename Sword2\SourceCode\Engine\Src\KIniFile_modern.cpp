//---------------------------------------------------------------------------
// Sword3 Engine - Modernized Configuration File System Implementation
// 现代化的配置文件系统实现
//---------------------------------------------------------------------------
#include "KIniFile_modern.h"
#include <fstream>
#include <sstream>
#include <algorithm>
#include <cctype>

// Forward declaration for debug function
extern void g_DebugLog(const char* fmt, ...);

// 简化的字符串操作函数（避免复杂依赖）
namespace SimpleString {
    std::string Trim(const std::string& str) {
        auto start = std::find_if(str.begin(), str.end(), 
                                 [](unsigned char c) { return !std::isspace(c); });
        auto end = std::find_if(str.rbegin(), str.rend(), 
                               [](unsigned char c) { return !std::isspace(c); }).base();
        return std::string(start, end);
    }
    
    std::string ToLower(const std::string& str) {
        std::string result = str;
        std::transform(result.begin(), result.end(), result.begin(), 
                      [](unsigned char c) { return std::tolower(c); });
        return result;
    }
    
    int ToInt(const std::string& str, int defaultValue) {
        try {
            return std::stoi(Trim(str));
        } catch (...) {
            return defaultValue;
        }
    }
    
    float ToFloat(const std::string& str, float defaultValue) {
        try {
            return std::stof(Trim(str));
        } catch (...) {
            return defaultValue;
        }
    }
    
    bool ToBool(const std::string& str, bool defaultValue) {
        std::string trimmed = ToLower(Trim(str));
        if (trimmed == "true" || trimmed == "1" || trimmed == "yes" || trimmed == "on") {
            return true;
        } else if (trimmed == "false" || trimmed == "0" || trimmed == "no" || trimmed == "off") {
            return false;
        }
        return defaultValue;
    }
    
    std::string ToString(int value) {
        return std::to_string(value);
    }
    
    std::string ToString(float value) {
        return std::to_string(value);
    }
    
    std::string ToString(bool value) {
        return value ? "true" : "false";
    }
    
    std::vector<std::string> Split(const std::string& str, char delimiter) {
        std::vector<std::string> result;
        std::stringstream ss(str);
        std::string item;
        
        while (std::getline(ss, item, delimiter)) {
            result.push_back(Trim(item));
        }
        
        return result;
    }
}

//---------------------------------------------------------------------------
// 构造函数和析构函数
//---------------------------------------------------------------------------
KIniFile::KIniFile() : m_modified(false) {
}

KIniFile::KIniFile(const std::string& filename) : m_filename(filename), m_modified(false) {
    LoadFromFile(filename);
}

KIniFile::~KIniFile() {
    // 如果有修改且有文件名，可以选择自动保存
    // 这里为了安全起见，不自动保存
}

//---------------------------------------------------------------------------
// 兼容性接口实现
//---------------------------------------------------------------------------
BOOL KIniFile::Load(LPCSTR FileName) {
    if (!FileName) return FALSE;
    return LoadFromFile(std::string(FileName)) ? TRUE : FALSE;
}

BOOL KIniFile::Save(LPCSTR FileName) {
    if (!FileName) return FALSE;
    return SaveToFile(std::string(FileName)) ? TRUE : FALSE;
}

void KIniFile::Clear() {
    m_sections.clear();
    m_modified = true;
}

BOOL KIniFile::IsSectionExist(LPCSTR lpSection) {
    if (!lpSection) return FALSE;
    return HasSection(std::string(lpSection)) ? TRUE : FALSE;
}

void KIniFile::EraseSection(LPCSTR lpSection) {
    if (lpSection) {
        RemoveSection(std::string(lpSection));
    }
}

void KIniFile::EraseKey(LPCSTR lpSection, LPCSTR lpKey) {
    if (lpSection && lpKey) {
        RemoveKey(std::string(lpSection), std::string(lpKey));
    }
}

BOOL KIniFile::GetString(LPCSTR lpSection, LPCSTR lpKeyName, LPCSTR lpDefault, 
                        LPSTR lpRString, DWORD dwSize) {
    if (!lpSection || !lpKeyName || !lpRString || dwSize == 0) return FALSE;
    
    std::string defaultValue = lpDefault ? lpDefault : "";
    std::string result = GetString(std::string(lpSection), std::string(lpKeyName), defaultValue);
    
    strncpy(lpRString, result.c_str(), dwSize - 1);
    lpRString[dwSize - 1] = '\0';
    
    return TRUE;
}

BOOL KIniFile::GetInteger(LPCSTR lpSection, LPCSTR lpKeyName, int nDefault, int* pnValue) {
    if (!lpSection || !lpKeyName || !pnValue) return FALSE;
    
    *pnValue = GetInt(std::string(lpSection), std::string(lpKeyName), nDefault);
    return TRUE;
}

BOOL KIniFile::GetFloat(LPCSTR lpSection, LPCSTR lpKeyName, float fDefault, float* pfValue) {
    if (!lpSection || !lpKeyName || !pfValue) return FALSE;
    
    *pfValue = GetFloat(std::string(lpSection), std::string(lpKeyName), fDefault);
    return TRUE;
}

void KIniFile::GetBool(LPCSTR lpSection, LPCSTR lpKeyName, BOOL* pBool) {
    if (!lpSection || !lpKeyName || !pBool) return;
    
    *pBool = GetBool(std::string(lpSection), std::string(lpKeyName), false) ? TRUE : FALSE;
}

void KIniFile::WriteString(LPCSTR lpSection, LPCSTR lpKeyName, LPCSTR lpString) {
    if (lpSection && lpKeyName && lpString) {
        SetString(std::string(lpSection), std::string(lpKeyName), std::string(lpString));
    }
}

void KIniFile::WriteInteger(LPCSTR lpSection, LPCSTR lpKeyName, int Value) {
    if (lpSection && lpKeyName) {
        SetInt(std::string(lpSection), std::string(lpKeyName), Value);
    }
}

void KIniFile::WriteFloat(LPCSTR lpSection, LPCSTR lpKeyName, float fValue) {
    if (lpSection && lpKeyName) {
        SetFloat(std::string(lpSection), std::string(lpKeyName), fValue);
    }
}

void KIniFile::WriteBool(LPCSTR lpSection, LPCSTR lpKeyName, BOOL bValue) {
    if (lpSection && lpKeyName) {
        SetBool(std::string(lpSection), std::string(lpKeyName), bValue != FALSE);
    }
}

//---------------------------------------------------------------------------
// 扩展的兼容性接口
//---------------------------------------------------------------------------
void KIniFile::GetInteger2(LPCSTR lpSection, LPCSTR lpKeyName, int* pnValue1, int* pnValue2) {
    if (!lpSection || !lpKeyName || !pnValue1 || !pnValue2) return;
    
    std::vector<int> values = GetIntArray(std::string(lpSection), std::string(lpKeyName), ',');
    *pnValue1 = values.size() > 0 ? values[0] : 0;
    *pnValue2 = values.size() > 1 ? values[1] : 0;
}

void KIniFile::GetFloat2(LPCSTR lpSection, LPCSTR lpKeyName, float* pfValue1, float* pfValue2) {
    if (!lpSection || !lpKeyName || !pfValue1 || !pfValue2) return;
    
    std::vector<float> values = GetFloatArray(std::string(lpSection), std::string(lpKeyName), ',');
    *pfValue1 = values.size() > 0 ? values[0] : 0.0f;
    *pfValue2 = values.size() > 1 ? values[1] : 0.0f;
}

void KIniFile::GetFloat3(LPCSTR lpSection, LPCSTR lpKeyName, float* pFloat) {
    if (!lpSection || !lpKeyName || !pFloat) return;
    
    std::vector<float> values = GetFloatArray(std::string(lpSection), std::string(lpKeyName), ',');
    for (size_t i = 0; i < 3 && i < values.size(); ++i) {
        pFloat[i] = values[i];
    }
}

void KIniFile::GetFloat4(LPCSTR lpSection, LPCSTR lpKeyName, float* pRect) {
    if (!lpSection || !lpKeyName || !pRect) return;
    
    std::vector<float> values = GetFloatArray(std::string(lpSection), std::string(lpKeyName), ',');
    for (size_t i = 0; i < 4 && i < values.size(); ++i) {
        pRect[i] = values[i];
    }
}

void KIniFile::GetRect(LPCSTR lpSection, LPCSTR lpKeyName, RECT* pRect) {
    if (!lpSection || !lpKeyName || !pRect) return;
    
    std::vector<int> values = GetIntArray(std::string(lpSection), std::string(lpKeyName), ',');
    pRect->left = values.size() > 0 ? values[0] : 0;
    pRect->top = values.size() > 1 ? values[1] : 0;
    pRect->right = values.size() > 2 ? values[2] : 0;
    pRect->bottom = values.size() > 3 ? values[3] : 0;
}

void KIniFile::WriteInteger2(LPCSTR lpSection, LPCSTR lpKeyName, int Value1, int Value2) {
    if (lpSection && lpKeyName) {
        std::vector<int> values = {Value1, Value2};
        SetIntArray(std::string(lpSection), std::string(lpKeyName), values, ',');
    }
}

void KIniFile::WriteFloat2(LPCSTR lpSection, LPCSTR lpKeyName, float fValue1, float fValue2) {
    if (lpSection && lpKeyName) {
        std::vector<float> values = {fValue1, fValue2};
        SetFloatArray(std::string(lpSection), std::string(lpKeyName), values, ',');
    }
}

BOOL KIniFile::GetNextSection(LPCSTR pSection, LPSTR pNextSection) {
    if (!pNextSection) return FALSE;
    
    std::vector<std::string> sections = GetSectionNames();
    if (sections.empty()) return FALSE;
    
    if (!pSection || strlen(pSection) == 0) {
        // 返回第一个节
        strcpy(pNextSection, sections[0].c_str());
        return TRUE;
    }
    
    // 查找当前节的下一个节
    std::string currentSection(pSection);
    for (size_t i = 0; i < sections.size(); ++i) {
        if (sections[i] == currentSection && i + 1 < sections.size()) {
            strcpy(pNextSection, sections[i + 1].c_str());
            return TRUE;
        }
    }
    
    return FALSE;
}

BOOL KIniFile::GetNextKey(LPCSTR pSection, LPCSTR pKey, LPSTR pNextKey) {
    if (!pSection || !pNextKey) return FALSE;
    
    std::vector<std::string> keys = GetKeyNames(std::string(pSection));
    if (keys.empty()) return FALSE;
    
    if (!pKey || strlen(pKey) == 0) {
        // 返回第一个键
        strcpy(pNextKey, keys[0].c_str());
        return TRUE;
    }
    
    // 查找当前键的下一个键
    std::string currentKey(pKey);
    for (size_t i = 0; i < keys.size(); ++i) {
        if (keys[i] == currentKey && i + 1 < keys.size()) {
            strcpy(pNextKey, keys[i + 1].c_str());
            return TRUE;
        }
    }
    
    return FALSE;
}

int KIniFile::GetSectionCount() {
    return static_cast<int>(m_sections.size());
}

// 现代化接口实现
//---------------------------------------------------------------------------
bool KIniFile::LoadFromFile(const std::string& filename) {
    std::ifstream file(filename);
    if (!file.is_open()) {
        g_DebugLog("Failed to open ini file: %s", filename.c_str());
        return false;
    }

    Clear();
    m_filename = filename;

    std::string line;
    std::string currentSection;

    while (std::getline(file, line)) {
        if (!ParseLine(line, currentSection)) {
            // 解析错误，但继续处理其他行
        }
    }

    m_modified = false;
    g_DebugLog("Loaded ini file: %s (%d sections)", filename.c_str(), (int)m_sections.size());
    return true;
}

bool KIniFile::SaveToFile(const std::string& filename) {
    std::string saveFilename = filename.empty() ? m_filename : filename;
    if (saveFilename.empty()) {
        g_DebugLog("No filename specified for saving ini file");
        return false;
    }

    std::ofstream file(saveFilename);
    if (!file.is_open()) {
        g_DebugLog("Failed to create ini file: %s", saveFilename.c_str());
        return false;
    }

    // 写入所有节和键值对
    for (const auto& section : m_sections) {
        file << "[" << section.first << "]" << std::endl;

        for (const auto& keyValue : section.second) {
            file << keyValue.first << "=" << EscapeValue(keyValue.second) << std::endl;
        }

        file << std::endl; // 节之间空一行
    }

    if (!filename.empty()) {
        m_filename = filename;
    }
    m_modified = false;

    g_DebugLog("Saved ini file: %s (%d sections)", saveFilename.c_str(), (int)m_sections.size());
    return true;
}

// 节操作
bool KIniFile::HasSection(const std::string& section) const {
    return m_sections.find(NormalizeSection(section)) != m_sections.end();
}

bool KIniFile::CreateSection(const std::string& section) {
    std::string normalizedSection = NormalizeSection(section);
    if (m_sections.find(normalizedSection) == m_sections.end()) {
        m_sections[normalizedSection] = KeyValueMap();
        m_modified = true;
        return true;
    }
    return false;
}

bool KIniFile::RemoveSection(const std::string& section) {
    std::string normalizedSection = NormalizeSection(section);
    auto it = m_sections.find(normalizedSection);
    if (it != m_sections.end()) {
        m_sections.erase(it);
        m_modified = true;
        return true;
    }
    return false;
}

std::vector<std::string> KIniFile::GetSectionNames() const {
    std::vector<std::string> names;
    for (const auto& section : m_sections) {
        names.push_back(section.first);
    }
    return names;
}

// 键操作
bool KIniFile::HasKey(const std::string& section, const std::string& key) const {
    std::string normalizedSection = NormalizeSection(section);
    std::string normalizedKey = NormalizeKey(key);

    auto sectionIt = m_sections.find(normalizedSection);
    if (sectionIt != m_sections.end()) {
        return sectionIt->second.find(normalizedKey) != sectionIt->second.end();
    }
    return false;
}

bool KIniFile::RemoveKey(const std::string& section, const std::string& key) {
    std::string normalizedSection = NormalizeSection(section);
    std::string normalizedKey = NormalizeKey(key);

    auto sectionIt = m_sections.find(normalizedSection);
    if (sectionIt != m_sections.end()) {
        auto keyIt = sectionIt->second.find(normalizedKey);
        if (keyIt != sectionIt->second.end()) {
            sectionIt->second.erase(keyIt);
            m_modified = true;
            return true;
        }
    }
    return false;
}

std::vector<std::string> KIniFile::GetKeyNames(const std::string& section) const {
    std::vector<std::string> names;
    std::string normalizedSection = NormalizeSection(section);

    auto sectionIt = m_sections.find(normalizedSection);
    if (sectionIt != m_sections.end()) {
        for (const auto& keyValue : sectionIt->second) {
            names.push_back(keyValue.first);
        }
    }
    return names;
}

// 类型安全的读取接口
std::string KIniFile::GetString(const std::string& section, const std::string& key,
                               const std::string& defaultValue) const {
    std::string normalizedSection = NormalizeSection(section);
    std::string normalizedKey = NormalizeKey(key);

    auto sectionIt = m_sections.find(normalizedSection);
    if (sectionIt != m_sections.end()) {
        auto keyIt = sectionIt->second.find(normalizedKey);
        if (keyIt != sectionIt->second.end()) {
            return UnescapeValue(keyIt->second);
        }
    }
    return defaultValue;
}

int KIniFile::GetInt(const std::string& section, const std::string& key, int defaultValue) const {
    std::string value = GetString(section, key, "");
    return value.empty() ? defaultValue : SimpleString::ToInt(value, defaultValue);
}

float KIniFile::GetFloat(const std::string& section, const std::string& key, float defaultValue) const {
    std::string value = GetString(section, key, "");
    return value.empty() ? defaultValue : SimpleString::ToFloat(value, defaultValue);
}

bool KIniFile::GetBool(const std::string& section, const std::string& key, bool defaultValue) const {
    std::string value = GetString(section, key, "");
    return value.empty() ? defaultValue : SimpleString::ToBool(value, defaultValue);
}

// 数组读取接口
std::vector<int> KIniFile::GetIntArray(const std::string& section, const std::string& key,
                                      char delimiter) const {
    std::vector<int> result;
    std::string value = GetString(section, key, "");
    if (!value.empty()) {
        std::vector<std::string> parts = SimpleString::Split(value, delimiter);
        for (const auto& part : parts) {
            result.push_back(SimpleString::ToInt(part, 0));
        }
    }
    return result;
}

std::vector<float> KIniFile::GetFloatArray(const std::string& section, const std::string& key,
                                          char delimiter) const {
    std::vector<float> result;
    std::string value = GetString(section, key, "");
    if (!value.empty()) {
        std::vector<std::string> parts = SimpleString::Split(value, delimiter);
        for (const auto& part : parts) {
            result.push_back(SimpleString::ToFloat(part, 0.0f));
        }
    }
    return result;
}

std::vector<std::string> KIniFile::GetStringArray(const std::string& section, const std::string& key,
                                                 char delimiter) const {
    std::string value = GetString(section, key, "");
    return value.empty() ? std::vector<std::string>() : SimpleString::Split(value, delimiter);
}

// 类型安全的写入接口
void KIniFile::SetString(const std::string& section, const std::string& key, const std::string& value) {
    EnsureSection(section);
    std::string normalizedSection = NormalizeSection(section);
    std::string normalizedKey = NormalizeKey(key);

    m_sections[normalizedSection][normalizedKey] = value;
    m_modified = true;
}

void KIniFile::SetInt(const std::string& section, const std::string& key, int value) {
    SetString(section, key, SimpleString::ToString(value));
}

void KIniFile::SetFloat(const std::string& section, const std::string& key, float value) {
    SetString(section, key, SimpleString::ToString(value));
}

void KIniFile::SetBool(const std::string& section, const std::string& key, bool value) {
    SetString(section, key, SimpleString::ToString(value));
}

// 数组写入接口
void KIniFile::SetIntArray(const std::string& section, const std::string& key,
                          const std::vector<int>& values, char delimiter) {
    std::ostringstream oss;
    for (size_t i = 0; i < values.size(); ++i) {
        if (i > 0) oss << delimiter;
        oss << values[i];
    }
    SetString(section, key, oss.str());
}

void KIniFile::SetFloatArray(const std::string& section, const std::string& key,
                            const std::vector<float>& values, char delimiter) {
    std::ostringstream oss;
    for (size_t i = 0; i < values.size(); ++i) {
        if (i > 0) oss << delimiter;
        oss << values[i];
    }
    SetString(section, key, oss.str());
}

void KIniFile::SetStringArray(const std::string& section, const std::string& key,
                             const std::vector<std::string>& values, char delimiter) {
    std::ostringstream oss;
    for (size_t i = 0; i < values.size(); ++i) {
        if (i > 0) oss << delimiter;
        oss << values[i];
    }
    SetString(section, key, oss.str());
}

// 调试和输出
void KIniFile::DebugPrint() const {
    g_DebugLog("KIniFile: %s (%d sections)", m_filename.c_str(), (int)m_sections.size());

    for (const auto& section : m_sections) {
        g_DebugLog("  [%s] (%d keys)", section.first.c_str(), (int)section.second.size());

        int keyCount = 0;
        for (const auto& keyValue : section.second) {
            if (keyCount < 5) { // 只显示前5个键
                g_DebugLog("    %s = %s", keyValue.first.c_str(), keyValue.second.c_str());
            }
            keyCount++;
        }

        if (keyCount > 5) {
            g_DebugLog("    ... and %d more keys", keyCount - 5);
        }
    }
}

std::string KIniFile::ToString() const {
    std::ostringstream oss;

    for (const auto& section : m_sections) {
        oss << "[" << section.first << "]" << std::endl;

        for (const auto& keyValue : section.second) {
            oss << keyValue.first << "=" << EscapeValue(keyValue.second) << std::endl;
        }

        oss << std::endl;
    }

    return oss.str();
}

// 内部辅助函数
//---------------------------------------------------------------------------
std::string KIniFile::NormalizeSection(const std::string& section) const {
    return SimpleString::Trim(section);
}

std::string KIniFile::NormalizeKey(const std::string& key) const {
    return SimpleString::Trim(key);
}

bool KIniFile::ParseLine(const std::string& line, std::string& currentSection) {
    std::string trimmedLine = SimpleString::Trim(line);

    // 跳过空行和注释
    if (trimmedLine.empty() || trimmedLine[0] == ';' || trimmedLine[0] == '#') {
        return true;
    }

    // 检查是否是节标题
    if (trimmedLine[0] == '[' && trimmedLine.back() == ']') {
        currentSection = trimmedLine.substr(1, trimmedLine.length() - 2);
        currentSection = NormalizeSection(currentSection);
        EnsureSection(currentSection);
        return true;
    }

    // 检查是否是键值对
    size_t equalPos = trimmedLine.find('=');
    if (equalPos != std::string::npos && !currentSection.empty()) {
        std::string key = SimpleString::Trim(trimmedLine.substr(0, equalPos));
        std::string value = SimpleString::Trim(trimmedLine.substr(equalPos + 1));

        if (!key.empty()) {
            m_sections[currentSection][NormalizeKey(key)] = UnescapeValue(value);
            return true;
        }
    }

    return false; // 解析失败
}

std::string KIniFile::EscapeValue(const std::string& value) const {
    // 简单的转义实现
    std::string result = value;
    // 可以在这里添加更复杂的转义逻辑
    return result;
}

std::string KIniFile::UnescapeValue(const std::string& value) const {
    // 简单的反转义实现
    std::string result = value;
    // 可以在这里添加更复杂的反转义逻辑
    return result;
}

void KIniFile::EnsureSection(const std::string& section) {
    std::string normalizedSection = NormalizeSection(section);
    if (m_sections.find(normalizedSection) == m_sections.end()) {
        m_sections[normalizedSection] = KeyValueMap();
    }
}

// 模板特化实现
template<>
std::string KIniFile::Get<std::string>(const std::string& section, const std::string& key,
                                      const std::string& defaultValue) const {
    return GetString(section, key, defaultValue);
}

template<>
int KIniFile::Get<int>(const std::string& section, const std::string& key,
                      const int& defaultValue) const {
    return GetInt(section, key, defaultValue);
}

template<>
float KIniFile::Get<float>(const std::string& section, const std::string& key,
                          const float& defaultValue) const {
    return GetFloat(section, key, defaultValue);
}

template<>
bool KIniFile::Get<bool>(const std::string& section, const std::string& key,
                        const bool& defaultValue) const {
    return GetBool(section, key, defaultValue);
}

template<>
void KIniFile::Set<std::string>(const std::string& section, const std::string& key,
                               const std::string& value) {
    SetString(section, key, value);
}

template<>
void KIniFile::Set<int>(const std::string& section, const std::string& key, const int& value) {
    SetInt(section, key, value);
}

template<>
void KIniFile::Set<float>(const std::string& section, const std::string& key, const float& value) {
    SetFloat(section, key, value);
}

template<>
void KIniFile::Set<bool>(const std::string& section, const std::string& key, const bool& value) {
    SetBool(section, key, value);
}

// 工厂函数实现
KSmartIniFile CreateIniFile() {
    return std::make_shared<KIniFile>();
}

KSmartIniFile CreateIniFile(const std::string& filename) {
    return std::make_shared<KIniFile>(filename);
}

//---------------------------------------------------------------------------
