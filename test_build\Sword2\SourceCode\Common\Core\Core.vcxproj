﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{CFD13F0C-2493-3B4A-82F5-55C7C73BF55A}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>Core</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\Sword2Code\test_build\lib\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Core.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">CoreD</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\Sword2Code\test_build\lib\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Core.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Core</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\Sword2Code\test_build\lib\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Core.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Core</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\Sword2Code\test_build\lib\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Core.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Core</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.lib</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_DEBUG;_STANDALONE;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_STANDALONE;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_STANDALONE;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_STANDALONE;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_STANDALONE;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_STANDALONE;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_STANDALONE;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_STANDALONE;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/Sword2Code/Sword2/SourceCode/Common/Core/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/Sword2Code -BD:/Sword2Code/test_build --check-stamp-file D:/Sword2Code/test_build/Sword2/SourceCode/Common/Core/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\Sword2Code\test_build\Sword2\SourceCode\Common\Core\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/Sword2Code/Sword2/SourceCode/Common/Core/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/Sword2Code -BD:/Sword2Code/test_build --check-stamp-file D:/Sword2Code/test_build/Sword2/SourceCode/Common/Core/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\Sword2Code\test_build\Sword2\SourceCode\Common\Core\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule D:/Sword2Code/Sword2/SourceCode/Common/Core/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/Sword2Code -BD:/Sword2Code/test_build --check-stamp-file D:/Sword2Code/test_build/Sword2/SourceCode/Common/Core/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\Sword2Code\test_build\Sword2\SourceCode\Common\Core\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule D:/Sword2Code/Sword2/SourceCode/Common/Core/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/Sword2Code -BD:/Sword2Code/test_build --check-stamp-file D:/Sword2Code/test_build/Sword2/SourceCode/Common/Core/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\Sword2Code\test_build\Sword2\SourceCode\Common\Core\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KCore.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KMath.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\stdafx.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KCharacterSet.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KObserver.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KPlayer.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KPlayerEvent.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KPlayerEventMan.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KPlayerInteractive.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KPlayerPK.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KPlayerSyncCheck.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KPlayerTeam.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KPlayerTrade.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KStall.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KTeam.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KPlayerTitle.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KPlayerSkillState.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AI\KActiveAttack2AI.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AI\KActiveAttackAI.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AI\KAIBase.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AI\KAIManager.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AI\KClientAI.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AI\KDialogAI.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AI\KEscapeAI.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AI\KFollowerPassivitAI.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AI\KGroupAttackAI.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AI\KHostilityAI.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AI\KPassivityAttackAI.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AI\KPickCorpseAI.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AI\KNpcAIControl.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AI\KDeludeAI.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AI\KCorpseRevivalAI.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AI\KAreaActiveAttackAI.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AI\KAIClientAutoFightEmei.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AI\KClientNpcAI.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AI\KPetAttackAI.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KFaction.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KPlayerFaction.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KNpc.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KNpcCommand.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KNpcExp.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KNpcFindPath.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KNpcScriptControl.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KNpcSoundControl.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KNpcTemplate.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KCharacter.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KNpcAttribute.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KNpcEventsBase.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KNpcEventAttribute.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KNpcGroup.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Character\CharacterConfig.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Character\AutoMove\KAutoMove.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Character\Paint\KCharacterResNode.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Character\Paint\KNpcResNode.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Character\Paint\KResListSvr.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Character\KPlayerAddendaAttribute.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Character\KPlayerSpecialAttribute.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Character\ColdDown.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Character\KPlayerRelation.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Character\KPlayerDining.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Character\KMultiRolesMgr.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Character\KPlayerReborn.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Character\KRebornData.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Character\KRebornManager.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KTitle.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KTitleManager.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KObjectPosition.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KExtendProtocolProcess.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Net\KNetCommand.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KNewProtocolProcess.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KProtocol.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KProtocolProcess.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\CoreServerShell.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KChannelProtocol.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KLifeSkill.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KLifeSkillList.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KMissle.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KMissleManager.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KMissleSet.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KSkill.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KSkillList.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KSkillManager.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KComposeSkill.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KGatherSkill.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KLifeSkillRes.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KRegion.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KSubWorld.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KSubWorldSet.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KWorldMsg.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KSortScript.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\ScriptFuns.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\LibScriptFuns.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\LuaFuns.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Script\ItemScript.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Script\TaskScript.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Script\NpcScript.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Script\TongScript.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KBuySell.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KChannelTrade.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KMessage.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KSpecialAttribute.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KUiInteractive.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KInventory.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KItem.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Item\KItemAttribute.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KItemList.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KItemSet.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KItemTemplate.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KMagicDesc.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Item\KMysteryBook.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KViewItem.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KItemEightDiagram.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KItemEnchase.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KItemEpurate.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KItemEnhance.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Item\KAddendaAttribute.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Item\KBookDesc.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Item\KSuitEquip.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Item\KGuAttribute.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Item\LingShiAttr.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Item\LingShiCreator.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Item\LingShiComposeData.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Item\LingShiCompose.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Item\ItemValue.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Item\MakeEquipSocket.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Item\MakeEquipLingQi.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Item\ClientItemParser.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Item\KEquipComposeMagic.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KObj.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KObjectSet.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KObjectTemplate.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KMission.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KPlayerTask.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KTaskFuns.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KTaskNpcGroup.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KNormalTask.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KTaskNoteManager.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KCommerceTask.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KFactionTask.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KGMCommand.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Trigger\KTrigger.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Trigger\KTriggerManager.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Trigger\KTriggerSet.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Trigger\KTriggerTemplate.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KGameSpaceSoundSetting.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KMapMusic.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KPiPClientCtrl.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KPiPCore.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\CompetitionListMgr.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO\minilzo.c" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KChatChannels.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KChatChannel.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KChannelAlias.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KChatCtrl.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KGlobalValue.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KRecipe.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KComposeManager.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KWeatherMgr.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\LogCentre.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KSG_ExtPoint.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KBlueBoss.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KSuperBoss.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KOfflineLiveAction.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KCorpseRevival.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Camp\CampFile.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Camp\Camp.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Camp\CampManager.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Camp\CampRelation.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Camp\KPlayerCampInfo.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KClientPing.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\IB\KItemBillingObject.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\IB\KBillingPage.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\IB\KBillingItem.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\IB\KBillingNumberable.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\IB\KItemBillingManager.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KCustomData.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KCustomScriptData.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KRelayShareData.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\MissionEx\MissionExData.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\MissionEx\MissionExDataManager.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AffairData.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Npc\KMonsterHandbook.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KEventObserver.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KNpcEvent2Magic.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\PetSystem\KPetAttribute.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\PetSystem\KPetFeed.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\PetSystem\KPetLevelUp.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\PetSystem\KPetManager.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\PetSystem\KPetSet.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\PetSystem\KPetSummon.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Common\kobjectpool.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KPlayerSelectState.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Money.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\SaveLoad\KItemSaveLoad.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\SaveLoad\KFamilySaveLoad.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\SaveLoad\KRoleSaveLoad.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\SaveLoad\KPlayerSaveLoadMgr.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KPlayerMomentum.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KGestConvention.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KGestConventionRank.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Item\KRebornSmelt.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KPossessionItem.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KPossessionMoney.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KPossessionTrace.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\WeaponEffect\WeaponEffect.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\WeaponEffect\WeaponEffectManager.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\WeaponEffect\WeaponEffectSet.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\SaveLoad\SaveLoadAux.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongCenter.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongDuty.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongManager.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongMember.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongProcessor.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongSync.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongTeam.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongDefine.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongTechControl.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongTechRes.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongMagicAttribute.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KPlayerTreasure.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KMailAttachmentImp.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KMailImp.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KPostOffice_Entry.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KPostOfficeImp.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager\KAreaCampMgr.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager\KAreaCamp.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager\KCampMsgProcessor.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager\KAreaCampTongSort.cpp" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\Sword2Code\test_build\ZERO_CHECK.vcxproj">
      <Project>{5056683F-7A79-357B-A3C6-E0F657A7870C}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\Sword2Code\test_build\Sword2\SourceCode\Engine\Engine.vcxproj">
      <Project>{1946B23A-828A-3EB6-83E5-5A3787A50438}</Project>
      <Name>Engine</Name>
    </ProjectReference>
    <ProjectReference Include="D:\Sword2Code\test_build\DevEnv\Lua5\Lua5.vcxproj">
      <Project>{C9F953A3-9235-3E02-90C6-F745942FD865}</Project>
      <Name>Lua5</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>