// Minimal configuration system test - verify basic INI file operations
#include "KWin32.h"
#include <string>
#include <map>
#include <vector>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <cctype>
#include <stdio.h>
#include <stdarg.h>

// Simple debug implementation for testing
void g_DebugLog(const char* fmt, ...) {
    char buffer[1024];
    va_list va;
    
    va_start(va, fmt);
    vsnprintf(buffer, sizeof(buffer), fmt, va);
    va_end(va);
    
    printf("[DEBUG] %s\n", buffer);
}

// Minimal INI file class
class MinimalIniFile {
private:
    std::map<std::string, std::map<std::string, std::string> > m_sections;
    std::string m_filename;
    bool m_modified;
    
    std::string Trim(const std::string& str) {
        size_t start = str.find_first_not_of(" \t\r\n");
        if (start == std::string::npos) return "";
        
        size_t end = str.find_last_not_of(" \t\r\n");
        return str.substr(start, end - start + 1);
    }
    
    int ToInt(const std::string& str, int defaultValue = 0) {
        std::istringstream iss(Trim(str));
        int result;
        if (iss >> result) {
            return result;
        }
        return defaultValue;
    }

    float ToFloat(const std::string& str, float defaultValue = 0.0f) {
        std::istringstream iss(Trim(str));
        float result;
        if (iss >> result) {
            return result;
        }
        return defaultValue;
    }

    bool ToBool(const std::string& str, bool defaultValue = false) {
        std::string trimmed = Trim(str);
        for (size_t i = 0; i < trimmed.length(); ++i) {
            trimmed[i] = std::tolower(trimmed[i]);
        }

        if (trimmed == "true" || trimmed == "1" || trimmed == "yes") {
            return true;
        } else if (trimmed == "false" || trimmed == "0" || trimmed == "no") {
            return false;
        }
        return defaultValue;
    }
    
    std::string ToString(int value) {
        std::ostringstream oss;
        oss << value;
        return oss.str();
    }
    
    std::string ToString(float value) {
        std::ostringstream oss;
        oss << value;
        return oss.str();
    }
    
    std::string ToString(bool value) {
        return value ? "true" : "false";
    }
    
public:
    MinimalIniFile() : m_modified(false) {}
    
    bool LoadFromFile(const std::string& filename) {
        std::ifstream file(filename.c_str());
        if (!file.is_open()) {
            g_DebugLog("Failed to open ini file: %s", filename.c_str());
            return false;
        }
        
        m_sections.clear();
        m_filename = filename;
        
        std::string line;
        std::string currentSection;
        
        while (std::getline(file, line)) {
            line = Trim(line);
            
            // 跳过空行和注释
            if (line.empty() || line[0] == ';' || line[0] == '#') {
                continue;
            }
            
            // 检查是否是节标题
            if (line[0] == '[' && line[line.length() - 1] == ']') {
                currentSection = line.substr(1, line.length() - 2);
                currentSection = Trim(currentSection);
                continue;
            }
            
            // 检查是否是键值对
            size_t equalPos = line.find('=');
            if (equalPos != std::string::npos && !currentSection.empty()) {
                std::string key = Trim(line.substr(0, equalPos));
                std::string value = Trim(line.substr(equalPos + 1));
                
                if (!key.empty()) {
                    m_sections[currentSection][key] = value;
                }
            }
        }
        
        m_modified = false;
        g_DebugLog("Loaded ini file: %s (%d sections)", filename.c_str(), (int)m_sections.size());
        return true;
    }
    
    bool SaveToFile(const std::string& filename = "") {
        std::string saveFilename = filename.empty() ? m_filename : filename;
        if (saveFilename.empty()) {
            g_DebugLog("No filename specified for saving ini file");
            return false;
        }
        
        std::ofstream file(saveFilename.c_str());
        if (!file.is_open()) {
            g_DebugLog("Failed to create ini file: %s", saveFilename.c_str());
            return false;
        }
        
        // 写入所有节和键值对
        for (std::map<std::string, std::map<std::string, std::string> >::const_iterator sectionIt = m_sections.begin(); 
             sectionIt != m_sections.end(); ++sectionIt) {
            file << "[" << sectionIt->first << "]" << std::endl;
            
            for (std::map<std::string, std::string>::const_iterator keyIt = sectionIt->second.begin(); 
                 keyIt != sectionIt->second.end(); ++keyIt) {
                file << keyIt->first << "=" << keyIt->second << std::endl;
            }
            
            file << std::endl; // 节之间空一行
        }
        
        if (!filename.empty()) {
            m_filename = filename;
        }
        m_modified = false;
        
        g_DebugLog("Saved ini file: %s (%d sections)", saveFilename.c_str(), (int)m_sections.size());
        return true;
    }
    
    std::string GetString(const std::string& section, const std::string& key, const std::string& defaultValue = "") {
        std::map<std::string, std::map<std::string, std::string> >::iterator sectionIt = m_sections.find(section);
        if (sectionIt != m_sections.end()) {
            std::map<std::string, std::string>::iterator keyIt = sectionIt->second.find(key);
            if (keyIt != sectionIt->second.end()) {
                return keyIt->second;
            }
        }
        return defaultValue;
    }
    
    int GetInt(const std::string& section, const std::string& key, int defaultValue = 0) {
        std::string value = GetString(section, key, "");
        return value.empty() ? defaultValue : ToInt(value, defaultValue);
    }
    
    float GetFloat(const std::string& section, const std::string& key, float defaultValue = 0.0f) {
        std::string value = GetString(section, key, "");
        return value.empty() ? defaultValue : ToFloat(value, defaultValue);
    }
    
    bool GetBool(const std::string& section, const std::string& key, bool defaultValue = false) {
        std::string value = GetString(section, key, "");
        return value.empty() ? defaultValue : ToBool(value, defaultValue);
    }
    
    void SetString(const std::string& section, const std::string& key, const std::string& value) {
        m_sections[section][key] = value;
        m_modified = true;
    }
    
    void SetInt(const std::string& section, const std::string& key, int value) {
        SetString(section, key, ToString(value));
    }
    
    void SetFloat(const std::string& section, const std::string& key, float value) {
        SetString(section, key, ToString(value));
    }
    
    void SetBool(const std::string& section, const std::string& key, bool value) {
        SetString(section, key, ToString(value));
    }
    
    bool HasSection(const std::string& section) {
        return m_sections.find(section) != m_sections.end();
    }
    
    bool HasKey(const std::string& section, const std::string& key) {
        std::map<std::string, std::map<std::string, std::string> >::iterator sectionIt = m_sections.find(section);
        if (sectionIt != m_sections.end()) {
            return sectionIt->second.find(key) != sectionIt->second.end();
        }
        return false;
    }
    
    std::vector<std::string> GetSectionNames() {
        std::vector<std::string> names;
        for (std::map<std::string, std::map<std::string, std::string> >::iterator it = m_sections.begin(); 
             it != m_sections.end(); ++it) {
            names.push_back(it->first);
        }
        return names;
    }
    
    std::vector<std::string> GetKeyNames(const std::string& section) {
        std::vector<std::string> names;
        std::map<std::string, std::map<std::string, std::string> >::iterator sectionIt = m_sections.find(section);
        if (sectionIt != m_sections.end()) {
            for (std::map<std::string, std::string>::iterator keyIt = sectionIt->second.begin(); 
                 keyIt != sectionIt->second.end(); ++keyIt) {
                names.push_back(keyIt->first);
            }
        }
        return names;
    }
};

// Test minimal configuration system functionality
ENGINE_API int TestMinimalConfigSystem() {
    g_DebugLog("Starting minimal configuration system test...");
    
    // Test 1: Basic INI file operations
    g_DebugLog("=== Test 1: Basic INI File Operations ===");
    
    MinimalIniFile config;
    
    // Test writing different types
    config.SetString("General", "AppName", "Sword3 Engine");
    config.SetInt("General", "Version", 100);
    config.SetFloat("General", "Scale", 1.5f);
    config.SetBool("General", "Debug", true);
    
    config.SetString("Graphics", "Resolution", "1024x768");
    config.SetInt("Graphics", "ColorDepth", 32);
    config.SetBool("Graphics", "Fullscreen", false);
    
    // Test reading back
    std::string appName = config.GetString("General", "AppName", "");
    if (appName != "Sword3 Engine") {
        g_DebugLog("ERROR: String read failed. Got: '%s'", appName.c_str());
        return -1;
    }
    g_DebugLog("String read passed: '%s'", appName.c_str());
    
    int version = config.GetInt("General", "Version", 0);
    if (version != 100) {
        g_DebugLog("ERROR: Int read failed. Got: %d", version);
        return -2;
    }
    g_DebugLog("Int read passed: %d", version);
    
    float scale = config.GetFloat("General", "Scale", 0.0f);
    if (scale < 1.4f || scale > 1.6f) {
        g_DebugLog("ERROR: Float read failed. Got: %f", scale);
        return -3;
    }
    g_DebugLog("Float read passed: %f", scale);
    
    bool debug = config.GetBool("General", "Debug", false);
    if (!debug) {
        g_DebugLog("ERROR: Bool read failed");
        return -4;
    }
    g_DebugLog("Bool read passed: %s", debug ? "true" : "false");
    
    // Test 2: Section and key operations
    g_DebugLog("=== Test 2: Section and Key Operations ===");
    
    if (!config.HasSection("General")) {
        g_DebugLog("ERROR: HasSection failed for existing section");
        return -5;
    }
    
    if (config.HasSection("NonExistent")) {
        g_DebugLog("ERROR: HasSection failed for non-existing section");
        return -6;
    }
    
    if (!config.HasKey("General", "AppName")) {
        g_DebugLog("ERROR: HasKey failed for existing key");
        return -7;
    }
    
    if (config.HasKey("General", "NonExistent")) {
        g_DebugLog("ERROR: HasKey failed for non-existing key");
        return -8;
    }
    
    std::vector<std::string> sections = config.GetSectionNames();
    if (sections.size() != 2) {
        g_DebugLog("ERROR: Expected 2 sections, got %d", (int)sections.size());
        return -9;
    }
    g_DebugLog("Section count passed: %d sections", (int)sections.size());
    
    std::vector<std::string> keys = config.GetKeyNames("General");
    if (keys.size() != 4) {
        g_DebugLog("ERROR: Expected 4 keys in General section, got %d", (int)keys.size());
        return -10;
    }
    g_DebugLog("Key count passed: %d keys in General section", (int)keys.size());
    
    // Test 3: File I/O
    g_DebugLog("=== Test 3: File I/O ===");
    
    if (!config.SaveToFile("test_config_minimal.ini")) {
        g_DebugLog("ERROR: Failed to save config file");
        return -11;
    }
    g_DebugLog("Config file saved successfully");
    
    MinimalIniFile config2;
    if (!config2.LoadFromFile("test_config_minimal.ini")) {
        g_DebugLog("ERROR: Failed to load config file");
        return -12;
    }
    g_DebugLog("Config file loaded successfully");
    
    // Verify loaded data
    std::string loadedAppName = config2.GetString("General", "AppName", "");
    if (loadedAppName != "Sword3 Engine") {
        g_DebugLog("ERROR: Loaded string mismatch. Got: '%s'", loadedAppName.c_str());
        return -13;
    }
    
    int loadedVersion = config2.GetInt("General", "Version", 0);
    if (loadedVersion != 100) {
        g_DebugLog("ERROR: Loaded int mismatch. Got: %d", loadedVersion);
        return -14;
    }
    
    g_DebugLog("File I/O verification passed");
    
    g_DebugLog("=== All minimal configuration system tests passed! ===");
    return 0;
}

// Test main function
#ifdef _STANDALONE
int main() {
    g_DebugLog("Starting minimal configuration system test suite...");
    
    int result = TestMinimalConfigSystem();
    
    if (result == 0) {
        printf("SUCCESS: All minimal configuration system tests passed!\n");
    } else {
        printf("FAILED: Minimal configuration system test failed with code %d\n", result);
    }
    
    return result;
}
#endif
