//---------------------------------------------------------------------------
// Sword3 Engine - Modernized String List
// 现代化的字符串列表 - 使用现代容器和算法
//---------------------------------------------------------------------------
#ifndef KStrList_Modern_H
#define KStrList_Modern_H

#include "KWin32.h"
#include "KList.h"
#include "KStrNode_modern.h"
#include <vector>
#include <unordered_map>
#include <algorithm>
#include <functional>

//---------------------------------------------------------------------------
// 现代化的字符串列表类
//---------------------------------------------------------------------------
#ifndef __linux
class ENGINE_API KStrList : public KList
#else
class KStrList : public KList
#endif
{
private:
    // 现代化：使用哈希表加速查找
    mutable std::unordered_map<std::string, KStrNode*> m_nameIndex;
    mutable bool m_indexDirty;
    
public:
    // 构造函数和析构函数
    KStrList();
    virtual ~KStrList();
    
    // 兼容性接口（保持与原有代码兼容）
    KStrNode* Find(char* str);
    KStrNode* Find(const char* str);
    
    // 现代化接口
    KStrNode* Find(const std::string& str);
    KStrNode* FindIgnoreCase(const std::string& str);
    
    // 添加和删除操作
    void AddString(const char* str);
    void AddString(const std::string& str);
    void AddStringNode(KStrNode* node);
    bool RemoveString(const char* str);
    bool RemoveString(const std::string& str);
    
    // 查询操作
    bool Contains(const char* str);
    bool Contains(const std::string& str);
    bool ContainsIgnoreCase(const std::string& str);
    size_t GetStringCount() const;
    
    // 遍历操作
    std::vector<std::string> GetAllStrings() const;
    std::vector<KStrNode*> GetAllNodes() const;
    
    // 排序操作
    void SortByName(bool ascending = true);
    void SortByLength(bool ascending = true);
    void SortCustom(std::function<bool(const KStrNode*, const KStrNode*)> comparator);
    
    // 过滤操作
    std::vector<KStrNode*> Filter(std::function<bool(const KStrNode*)> predicate) const;
    std::vector<KStrNode*> FilterByPrefix(const std::string& prefix) const;
    std::vector<KStrNode*> FilterBySuffix(const std::string& suffix) const;
    std::vector<KStrNode*> FilterByPattern(const std::string& pattern) const;
    
    // 转换操作
    template<typename T>
    std::vector<T> Transform(std::function<T(const KStrNode*)> transformer) const;
    
    // 聚合操作
    size_t CountIf(std::function<bool(const KStrNode*)> predicate) const;
    KStrNode* FindIf(std::function<bool(const KStrNode*)> predicate) const;
    
    // 字符串操作
    void ToUpperAll();
    void ToLowerAll();
    void TrimAll();
    
    // 导入导出
    bool LoadFromFile(const char* filename);
    bool SaveToFile(const char* filename) const;
    void LoadFromVector(const std::vector<std::string>& strings);
    std::vector<std::string> SaveToVector() const;
    
    // 调试和输出
    void DebugPrint() const;
    void Clear() override;
    
protected:
    // 内部辅助函数
    void RebuildIndex() const;
    void InvalidateIndex();
    void OnNodeAdded(KNode* node) override;
    void OnNodeRemoved(KNode* node) override;
};

//---------------------------------------------------------------------------
// 模板函数实现
//---------------------------------------------------------------------------
template<typename T>
std::vector<T> KStrList::Transform(std::function<T(const KStrNode*)> transformer) const {
    std::vector<T> result;
    
    KStrNode* current = static_cast<KStrNode*>(GetHead());
    while (current) {
        result.push_back(transformer(current));
        current = static_cast<KStrNode*>(current->GetNext());
    }
    
    return result;
}

//---------------------------------------------------------------------------
// 现代化的字符串映射类（键值对）
//---------------------------------------------------------------------------
template<typename T>
class KStrMap {
private:
    std::unordered_map<std::string, T> m_map;
    
public:
    // 基本操作
    void Set(const std::string& key, const T& value) { m_map[key] = value; }
    bool Get(const std::string& key, T& value) const {
        auto it = m_map.find(key);
        if (it != m_map.end()) {
            value = it->second;
            return true;
        }
        return false;
    }
    
    T Get(const std::string& key, const T& defaultValue = T{}) const {
        auto it = m_map.find(key);
        return (it != m_map.end()) ? it->second : defaultValue;
    }
    
    bool Contains(const std::string& key) const {
        return m_map.find(key) != m_map.end();
    }
    
    bool Remove(const std::string& key) {
        return m_map.erase(key) > 0;
    }
    
    void Clear() { m_map.clear(); }
    size_t Size() const { return m_map.size(); }
    bool Empty() const { return m_map.empty(); }
    
    // 遍历操作
    std::vector<std::string> GetKeys() const {
        std::vector<std::string> keys;
        for (const auto& pair : m_map) {
            keys.push_back(pair.first);
        }
        return keys;
    }
    
    std::vector<T> GetValues() const {
        std::vector<T> values;
        for (const auto& pair : m_map) {
            values.push_back(pair.second);
        }
        return values;
    }
    
    // 操作符重载
    T& operator[](const std::string& key) { return m_map[key]; }
    const T& operator[](const std::string& key) const { return m_map.at(key); }
};

//---------------------------------------------------------------------------
// 常用的特化类型定义
//---------------------------------------------------------------------------
using KStrIntMap = KStrMap<int>;
using KStrFloatMap = KStrMap<float>;
using KStrBoolMap = KStrMap<bool>;
using KStrPtrMap = KStrMap<void*>;
using KStrStrMap = KStrMap<std::string>;

//---------------------------------------------------------------------------
#endif // KStrList_Modern_H
