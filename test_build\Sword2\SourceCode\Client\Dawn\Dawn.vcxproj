﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{07393D3B-33CE-3CB7-8514-34195DF74393}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>Dawn</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\Sword2Code\test_build\bin\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Dawn.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Dawn</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\Sword2Code\test_build\bin\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Dawn.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Dawn</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\Sword2Code\test_build\bin\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Dawn.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Dawn</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\Sword2Code\test_build\bin\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Dawn.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Dawn</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Client\Dawn;D:\Sword2Code\Sword2\SourceCode\Engine\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager;D:\Sword2Code\Sword2\SourceCode\Common\Support\DynamicLoginPwd;D:\Sword2Code\Sword2\SourceCode\Common\Support\LocalStringLoader;D:\Sword2Code\Sword2\SourceCode\Common\Support\LogDatabase;D:\Sword2Code\Sword2\SourceCode\Common\Support\NameFilter;D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure;D:\Sword2Code\Sword2\SourceCode\Common\Support\SaveLoad;D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter;D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;UNICODE;_UNICODE;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;UNICODE;_UNICODE;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Client\Dawn;D:\Sword2Code\Sword2\SourceCode\Engine\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager;D:\Sword2Code\Sword2\SourceCode\Common\Support\DynamicLoginPwd;D:\Sword2Code\Sword2\SourceCode\Common\Support\LocalStringLoader;D:\Sword2Code\Sword2\SourceCode\Common\Support\LogDatabase;D:\Sword2Code\Sword2\SourceCode\Common\Support\NameFilter;D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure;D:\Sword2Code\Sword2\SourceCode\Common\Support\SaveLoad;D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter;D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Client\Dawn;D:\Sword2Code\Sword2\SourceCode\Engine\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager;D:\Sword2Code\Sword2\SourceCode\Common\Support\DynamicLoginPwd;D:\Sword2Code\Sword2\SourceCode\Common\Support\LocalStringLoader;D:\Sword2Code\Sword2\SourceCode\Common\Support\LogDatabase;D:\Sword2Code\Sword2\SourceCode\Common\Support\NameFilter;D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure;D:\Sword2Code\Sword2\SourceCode\Common\Support\SaveLoad;D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter;D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\..\..\..\lib\Debug\EngineD.lib;..\..\..\..\lib\Debug\CoreD.lib;..\..\..\..\lib\Debug\Support.lib;user32.lib;gdi32.lib;shell32.lib;comctl32.lib;ole32.lib;oleaut32.lib;uuid.lib;winmm.lib;..\..\..\..\lib\Debug\CoreD.lib;..\..\..\..\lib\Debug\EngineD.lib;D:\Sword2Code\DevEnv\dx9\lib\release\d3d9.lib;D:\Sword2Code\DevEnv\dx9\lib\release\ddraw.lib;D:\Sword2Code\DevEnv\dx9\lib\release\dsound.lib;D:\Sword2Code\DevEnv\dx9\lib\release\dinput8.lib;D:\Sword2Code\DevEnv\dx9\lib\release\dxguid.lib;ws2_32.lib;..\..\..\..\lib\Debug\Lua5D.lib;winmm.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/Sword2Code/test_build/lib/Debug/Dawn.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/Sword2Code/test_build/bin/Debug/Dawn.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Client\Dawn;D:\Sword2Code\Sword2\SourceCode\Engine\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager;D:\Sword2Code\Sword2\SourceCode\Common\Support\DynamicLoginPwd;D:\Sword2Code\Sword2\SourceCode\Common\Support\LocalStringLoader;D:\Sword2Code\Sword2\SourceCode\Common\Support\LogDatabase;D:\Sword2Code\Sword2\SourceCode\Common\Support\NameFilter;D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure;D:\Sword2Code\Sword2\SourceCode\Common\Support\SaveLoad;D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter;D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;UNICODE;_UNICODE;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;UNICODE;_UNICODE;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Client\Dawn;D:\Sword2Code\Sword2\SourceCode\Engine\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager;D:\Sword2Code\Sword2\SourceCode\Common\Support\DynamicLoginPwd;D:\Sword2Code\Sword2\SourceCode\Common\Support\LocalStringLoader;D:\Sword2Code\Sword2\SourceCode\Common\Support\LogDatabase;D:\Sword2Code\Sword2\SourceCode\Common\Support\NameFilter;D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure;D:\Sword2Code\Sword2\SourceCode\Common\Support\SaveLoad;D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter;D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Client\Dawn;D:\Sword2Code\Sword2\SourceCode\Engine\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager;D:\Sword2Code\Sword2\SourceCode\Common\Support\DynamicLoginPwd;D:\Sword2Code\Sword2\SourceCode\Common\Support\LocalStringLoader;D:\Sword2Code\Sword2\SourceCode\Common\Support\LogDatabase;D:\Sword2Code\Sword2\SourceCode\Common\Support\NameFilter;D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure;D:\Sword2Code\Sword2\SourceCode\Common\Support\SaveLoad;D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter;D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\..\..\..\lib\Release\Engine.lib;..\..\..\..\lib\Release\Core.lib;..\..\..\..\lib\Release\Support.lib;user32.lib;gdi32.lib;shell32.lib;comctl32.lib;ole32.lib;oleaut32.lib;uuid.lib;winmm.lib;..\..\..\..\lib\Release\Core.lib;..\..\..\..\lib\Release\Engine.lib;D:\Sword2Code\DevEnv\dx9\lib\release\d3d9.lib;D:\Sword2Code\DevEnv\dx9\lib\release\ddraw.lib;D:\Sword2Code\DevEnv\dx9\lib\release\dsound.lib;D:\Sword2Code\DevEnv\dx9\lib\release\dinput8.lib;D:\Sword2Code\DevEnv\dx9\lib\release\dxguid.lib;ws2_32.lib;..\..\..\..\lib\Release\Lua5.lib;winmm.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/Sword2Code/test_build/lib/Release/Dawn.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/Sword2Code/test_build/bin/Release/Dawn.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Client\Dawn;D:\Sword2Code\Sword2\SourceCode\Engine\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager;D:\Sword2Code\Sword2\SourceCode\Common\Support\DynamicLoginPwd;D:\Sword2Code\Sword2\SourceCode\Common\Support\LocalStringLoader;D:\Sword2Code\Sword2\SourceCode\Common\Support\LogDatabase;D:\Sword2Code\Sword2\SourceCode\Common\Support\NameFilter;D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure;D:\Sword2Code\Sword2\SourceCode\Common\Support\SaveLoad;D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter;D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;UNICODE;_UNICODE;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;UNICODE;_UNICODE;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Client\Dawn;D:\Sword2Code\Sword2\SourceCode\Engine\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager;D:\Sword2Code\Sword2\SourceCode\Common\Support\DynamicLoginPwd;D:\Sword2Code\Sword2\SourceCode\Common\Support\LocalStringLoader;D:\Sword2Code\Sword2\SourceCode\Common\Support\LogDatabase;D:\Sword2Code\Sword2\SourceCode\Common\Support\NameFilter;D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure;D:\Sword2Code\Sword2\SourceCode\Common\Support\SaveLoad;D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter;D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Client\Dawn;D:\Sword2Code\Sword2\SourceCode\Engine\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager;D:\Sword2Code\Sword2\SourceCode\Common\Support\DynamicLoginPwd;D:\Sword2Code\Sword2\SourceCode\Common\Support\LocalStringLoader;D:\Sword2Code\Sword2\SourceCode\Common\Support\LogDatabase;D:\Sword2Code\Sword2\SourceCode\Common\Support\NameFilter;D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure;D:\Sword2Code\Sword2\SourceCode\Common\Support\SaveLoad;D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter;D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\..\..\..\lib\MinSizeRel\Engine.lib;..\..\..\..\lib\MinSizeRel\Core.lib;..\..\..\..\lib\MinSizeRel\Support.lib;user32.lib;gdi32.lib;shell32.lib;comctl32.lib;ole32.lib;oleaut32.lib;uuid.lib;winmm.lib;..\..\..\..\lib\MinSizeRel\Core.lib;..\..\..\..\lib\MinSizeRel\Engine.lib;D:\Sword2Code\DevEnv\dx9\lib\release\d3d9.lib;D:\Sword2Code\DevEnv\dx9\lib\release\ddraw.lib;D:\Sword2Code\DevEnv\dx9\lib\release\dsound.lib;D:\Sword2Code\DevEnv\dx9\lib\release\dinput8.lib;D:\Sword2Code\DevEnv\dx9\lib\release\dxguid.lib;ws2_32.lib;..\..\..\..\lib\MinSizeRel\Lua5.lib;winmm.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/Sword2Code/test_build/lib/MinSizeRel/Dawn.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/Sword2Code/test_build/bin/MinSizeRel/Dawn.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Client\Dawn;D:\Sword2Code\Sword2\SourceCode\Engine\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager;D:\Sword2Code\Sword2\SourceCode\Common\Support\DynamicLoginPwd;D:\Sword2Code\Sword2\SourceCode\Common\Support\LocalStringLoader;D:\Sword2Code\Sword2\SourceCode\Common\Support\LogDatabase;D:\Sword2Code\Sword2\SourceCode\Common\Support\NameFilter;D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure;D:\Sword2Code\Sword2\SourceCode\Common\Support\SaveLoad;D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter;D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;UNICODE;_UNICODE;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;UNICODE;_UNICODE;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Client\Dawn;D:\Sword2Code\Sword2\SourceCode\Engine\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager;D:\Sword2Code\Sword2\SourceCode\Common\Support\DynamicLoginPwd;D:\Sword2Code\Sword2\SourceCode\Common\Support\LocalStringLoader;D:\Sword2Code\Sword2\SourceCode\Common\Support\LogDatabase;D:\Sword2Code\Sword2\SourceCode\Common\Support\NameFilter;D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure;D:\Sword2Code\Sword2\SourceCode\Common\Support\SaveLoad;D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter;D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Client\Dawn;D:\Sword2Code\Sword2\SourceCode\Engine\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager;D:\Sword2Code\Sword2\SourceCode\Common\Support\DynamicLoginPwd;D:\Sword2Code\Sword2\SourceCode\Common\Support\LocalStringLoader;D:\Sword2Code\Sword2\SourceCode\Common\Support\LogDatabase;D:\Sword2Code\Sword2\SourceCode\Common\Support\NameFilter;D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure;D:\Sword2Code\Sword2\SourceCode\Common\Support\SaveLoad;D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter;D:\Sword2Code\Sword2\SourceCode\Common\Support\VirtualItemProcessor;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\..\..\..\lib\RelWithDebInfo\Engine.lib;..\..\..\..\lib\RelWithDebInfo\Core.lib;..\..\..\..\lib\RelWithDebInfo\Support.lib;user32.lib;gdi32.lib;shell32.lib;comctl32.lib;ole32.lib;oleaut32.lib;uuid.lib;winmm.lib;..\..\..\..\lib\RelWithDebInfo\Core.lib;..\..\..\..\lib\RelWithDebInfo\Engine.lib;D:\Sword2Code\DevEnv\dx9\lib\release\d3d9.lib;D:\Sword2Code\DevEnv\dx9\lib\release\ddraw.lib;D:\Sword2Code\DevEnv\dx9\lib\release\dsound.lib;D:\Sword2Code\DevEnv\dx9\lib\release\dinput8.lib;D:\Sword2Code\DevEnv\dx9\lib\release\dxguid.lib;ws2_32.lib;..\..\..\..\lib\RelWithDebInfo\Lua5.lib;winmm.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/Sword2Code/test_build/lib/RelWithDebInfo/Dawn.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/Sword2Code/test_build/bin/RelWithDebInfo/Dawn.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\Sword2Code\Sword2\SourceCode\Client\Dawn\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/Sword2Code/Sword2/SourceCode/Client/Dawn/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/Sword2Code -BD:/Sword2Code/test_build --check-stamp-file D:/Sword2Code/test_build/Sword2/SourceCode/Client/Dawn/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\Sword2Code\test_build\Sword2\SourceCode\Client\Dawn\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/Sword2Code/Sword2/SourceCode/Client/Dawn/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/Sword2Code -BD:/Sword2Code/test_build --check-stamp-file D:/Sword2Code/test_build/Sword2/SourceCode/Client/Dawn/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\Sword2Code\test_build\Sword2\SourceCode\Client\Dawn\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule D:/Sword2Code/Sword2/SourceCode/Client/Dawn/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/Sword2Code -BD:/Sword2Code/test_build --check-stamp-file D:/Sword2Code/test_build/Sword2/SourceCode/Client/Dawn/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\Sword2Code\test_build\Sword2\SourceCode\Client\Dawn\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule D:/Sword2Code/Sword2/SourceCode/Client/Dawn/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/Sword2Code -BD:/Sword2Code/test_build --check-stamp-file D:/Sword2Code/test_build/Sword2/SourceCode/Client/Dawn/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\Sword2Code\test_build\Sword2\SourceCode\Client\Dawn\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\Dawn\Dawn.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\Dawn\KLogoDlg.cpp" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\Dawn\Dawn.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\Dawn\DawnStringTable.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\Dawn\DawnStringTable_T.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\Dawn\KLogoDlg.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Client\Dawn\resource.h" />
    <None Include="D:\Sword2Code\Sword2\SourceCode\Client\Dawn\DawnClient.ico">
    </None>
    <ResourceCompile Include="D:\Sword2Code\Sword2\SourceCode\Client\Dawn\DawnClient.rc" />
    <None Include="D:\Sword2Code\Sword2\SourceCode\Client\Dawn\logo.bmp">
    </None>
    <None Include="D:\Sword2Code\Sword2\SourceCode\Client\Dawn\月影.ico">
    </None>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\Sword2Code\test_build\ZERO_CHECK.vcxproj">
      <Project>{5056683F-7A79-357B-A3C6-E0F657A7870C}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\Sword2Code\test_build\Sword2\SourceCode\Common\Core\Core.vcxproj">
      <Project>{CFD13F0C-2493-3B4A-82F5-55C7C73BF55A}</Project>
      <Name>Core</Name>
    </ProjectReference>
    <ProjectReference Include="D:\Sword2Code\test_build\Sword2\SourceCode\Engine\Engine.vcxproj">
      <Project>{1946B23A-828A-3EB6-83E5-5A3787A50438}</Project>
      <Name>Engine</Name>
    </ProjectReference>
    <ProjectReference Include="D:\Sword2Code\test_build\DevEnv\Lua5\Lua5.vcxproj">
      <Project>{C9F953A3-9235-3E02-90C6-F745942FD865}</Project>
      <Name>Lua5</Name>
    </ProjectReference>
    <ProjectReference Include="D:\Sword2Code\test_build\Sword2\SourceCode\Common\Support\Support.vcxproj">
      <Project>{C0A6828F-D7DF-3084-91A2-DFF8343D5737}</Project>
      <Name>Support</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>