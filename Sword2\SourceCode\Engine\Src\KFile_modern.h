//---------------------------------------------------------------------------
// Sword3 Engine - Modernized File System
// 现代化的文件系统 - 使用std::filesystem和现代C++
//
// 策略：保持原有接口兼容性，内部使用现代C++实现
//---------------------------------------------------------------------------
#ifndef KFile_Modern_H
#define KFile_Modern_H

#include "KWin32.h"
#include <fstream>
#include <filesystem>
#include <memory>

//---------------------------------------------------------------------------
#define SEEK_ERROR 0xFFFFFFFF
//---------------------------------------------------------------------------

#ifndef __linux
class ENGINE_API KFile
#else
class KFile
#endif
{
private:
    std::unique_ptr<std::fstream> m_pFile;  // 现代化：使用std::fstream
    DWORD m_dwLen;   // File Size
    DWORD m_dwPos;   // File Pointer
    std::filesystem::path m_filePath;  // 现代化：使用std::filesystem::path
    
public:
    KFile();
    ~KFile();
    
    // 保持原有接口兼容性
    BOOL Open(LPSTR FileName);
    BOOL Create(LPSTR FileName);
    BOOL Append(LPSTR FileName);
    void Close();
    DWORD Read(LPVOID lpBuffer, DWORD dwReadBytes);
    DWORD Write(LPVOID lpBuffer, DWORD dwWriteBytes);
    DWORD Seek(LONG lDistance, DWORD dwMoveMethod);
    DWORD Tell();
    DWORD Size();
    
    // 现代化扩展接口
    bool IsOpen() const;
    bool IsEOF() const;
    std::filesystem::path GetPath() const { return m_filePath; }
    
private:
    // 内部辅助函数
    void UpdateFileSize();
    std::ios::openmode GetOpenMode(const char* mode);
};

//---------------------------------------------------------------------------
#endif // KFile_Modern_H
