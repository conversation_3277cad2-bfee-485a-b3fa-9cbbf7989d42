# Support库 CMakeLists.txt
# 支持库包含各种游戏支持功能

cmake_minimum_required(VERSION 3.16)

# 设置项目名称
set(SUPPORT_LIB_NAME Support)

# 收集所有子目录
set(SUPPORT_SUBDIRS
    CampManager
    DynamicLoginPwd
    LocalStringLoader
    LogDatabase
    NameFilter
    PlayerTreasure
    SaveLoad
    TongCenter
    VirtualItemProcessor
)

# 创建支持库目标
add_library(${SUPPORT_LIB_NAME} STATIC)

# 收集所有源文件
set(SUPPORT_SOURCES "")
set(SUPPORT_HEADERS "")

# 遍历每个子目录，收集源文件
foreach(SUBDIR ${SUPPORT_SUBDIRS})
    if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/${SUBDIR})
        # 收集.cpp文件
        file(GLOB SUBDIR_SOURCES "${CMAKE_CURRENT_SOURCE_DIR}/${SUBDIR}/*.cpp")
        file(GLOB SUBDIR_C_SOURCES "${CMAKE_CURRENT_SOURCE_DIR}/${SUBDIR}/*.c")
        
        # 收集.h文件
        file(GLOB SUBDIR_HEADERS "${CMAKE_CURRENT_SOURCE_DIR}/${SUBDIR}/*.h")
        
        # 添加到总列表
        list(APPEND SUPPORT_SOURCES ${SUBDIR_SOURCES} ${SUBDIR_C_SOURCES})
        list(APPEND SUPPORT_HEADERS ${SUBDIR_HEADERS})
        
        # 添加包含目录
        target_include_directories(${SUPPORT_LIB_NAME} PUBLIC ${CMAKE_CURRENT_SOURCE_DIR}/${SUBDIR})
        
        message(STATUS "Support: Added ${SUBDIR} (${CMAKE_LIST_LENGTH} sources)")
    else()
        message(STATUS "Support: Skipping missing directory ${SUBDIR}")
    endif()
endforeach()

# 如果有源文件，添加到目标
if(SUPPORT_SOURCES)
    target_sources(${SUPPORT_LIB_NAME} PRIVATE ${SUPPORT_SOURCES} ${SUPPORT_HEADERS})
    message(STATUS "Support: Total sources: ${CMAKE_LIST_LENGTH}")
else()
    # 如果没有源文件，创建一个空的源文件
    set(EMPTY_SOURCE_FILE "${CMAKE_CURRENT_BINARY_DIR}/empty_support.cpp")
    file(WRITE ${EMPTY_SOURCE_FILE} "// Empty source file for Support library\n")
    target_sources(${SUPPORT_LIB_NAME} PRIVATE ${EMPTY_SOURCE_FILE})
    message(STATUS "Support: No sources found, created empty library")
endif()

# 设置包含目录
target_include_directories(${SUPPORT_LIB_NAME} PUBLIC 
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_SOURCE_DIR}/Sword2/SourceCode/Include
    ${CMAKE_SOURCE_DIR}/Sword2/SourceCode/Engine/Include
)

# 链接依赖库
target_link_libraries(${SUPPORT_LIB_NAME} PUBLIC
    Core
    Engine
)

# 设置编译属性
set_target_properties(${SUPPORT_LIB_NAME} PROPERTIES
    OUTPUT_NAME "Support"
    ARCHIVE_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib"
)

# 平台特定设置
if(WIN32)
    target_compile_definitions(${SUPPORT_LIB_NAME} PRIVATE
        WIN32
        _WINDOWS
        _LIB
    )
endif()

message(STATUS "Support library configured")
