{"timestamp": "2025-07-01T10:24:33.813469", "total_time_seconds": 2.741694, "verification_results": {"prerequisites": {"tools": {"cmake": {"status": "ok", "version": "cmake version 4.0.1"}, "python": {"status": "ok", "version": "Python 3.13.5"}, "git": {"status": "error", "error": "[WinError 2] 系统找不到指定的文件。"}}, "files": {"CMakeLists.txt": "exists", "build.sh": "exists", "build.bat": "exists", "Sword2/SourceCode": "exists", "kitchen": "exists"}}, "build": {"success": true, "build_time": 0.43901658058166504, "artifacts": {"bin/SO2GameSvr.exe": {"exists": false}, "bin/SO2Client.exe": {"exists": false}, "lib": {"exists": false}}, "output": "\n"}, "tests": {"success": false, "error": "Test failed", "stderr": "nner.run_tests(categories, args.parallel, args.format)\n  File \"D:\\Sword2Code\\scripts\\test-runner.py\", line 332, in run_tests\n    results = self.run_test_category(category, parallel)\n  File \"D:\\Sword2Code\\scripts\\test-runner.py\", line 186, in run_test_category\n    print(f\"    {symbol} {test_name} ({result['duration']:.2f}s)\")\n    ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nUnicodeEncodeError: 'gbk' codec can't encode character '\\u23ed' in position 4: illegal multibyte sequence\n"}, "resources": {"success": false, "error": "Resource validation failed", "stderr": "^\n  File \"D:\\Sword2Code\\scripts\\resource-manager.py\", line 389, in main\n    success = manager.run(args.action)\n  File \"D:\\Sword2Code\\scripts\\resource-manager.py\", line 363, in run\n    return self.validate_resources()\n           ~~~~~~~~~~~~~~~~~~~~~~~^^\n  File \"D:\\Sword2Code\\scripts\\resource-manager.py\", line 331, in validate_resources\n    print(\"\\u2705 资源验证通过\")\n    ~~~~~^^^^^^^^^^^^^^^^^^^\nUnicodeEncodeError: 'gbk' codec can't encode character '\\u2705' in position 0: illegal multibyte sequence\n"}, "deployment": {"docker": false, "docker_compose": false, "deploy_script": true, "docker_config": true}, "monitoring": {"success": false, "error": "Monitoring failed", "stderr": "Traceback (most recent call last):\n  File \"D:\\Sword2Code\\scripts\\monitoring.py\", line 13, in <module>\n    import mysql.connector\nModuleNotFoundError: No module named 'mysql'\n"}}, "summary": {"total_checks": 6, "successful_checks": 1, "failed_checks": 5, "skipped_checks": 0}}