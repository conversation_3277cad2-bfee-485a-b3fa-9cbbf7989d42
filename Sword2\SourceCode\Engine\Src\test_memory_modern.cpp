// Modern memory management test - verify smart pointers and RAII
#include "KWin32.h"
#include <memory>
#include <vector>
#include <stdio.h>
#include <stdarg.h>

// Simple debug implementation for testing
void g_DebugLog(const char* fmt, ...) {
    char buffer[1024];
    va_list va;
    
    va_start(va, fmt);
    vsnprintf(buffer, sizeof(buffer), fmt, va);
    va_end(va);
    
    printf("[DEBUG] %s\n", buffer);
}

// Test class for memory management
class TestObject {
private:
    int m_id;
    static int s_instanceCount;
    
public:
    TestObject(int id) : m_id(id) {
        s_instanceCount++;
        g_DebugLog("TestObject %d created (total: %d)", m_id, s_instanceCount);
    }
    
    ~TestObject() {
        s_instanceCount--;
        g_DebugLog("TestObject %d destroyed (remaining: %d)", m_id, s_instanceCount);
    }
    
    int GetId() const { return m_id; }
    
    static int GetInstanceCount() { return s_instanceCount; }
    
    void DoWork() {
        g_DebugLog("TestObject %d doing work", m_id);
    }
};

int TestObject::s_instanceCount = 0;

// Simple RAII wrapper for C-style resources
class SimpleRAII {
private:
    void* m_resource;
    size_t m_size;
    
public:
    explicit SimpleRAII(size_t size) : m_size(size) {
        m_resource = malloc(size);
        if (m_resource) {
            memset(m_resource, 0, size);
            g_DebugLog("RAII allocated %d bytes", (int)size);
        } else {
            g_DebugLog("RAII allocation failed for %d bytes", (int)size);
        }
    }
    
    ~SimpleRAII() {
        if (m_resource) {
            free(m_resource);
            g_DebugLog("RAII freed %d bytes", (int)m_size);
        }
    }
    
    void* Get() const { return m_resource; }
    size_t Size() const { return m_size; }
    bool IsValid() const { return m_resource != nullptr; }
    
    // Disable copy
    SimpleRAII(const SimpleRAII&);
    SimpleRAII& operator=(const SimpleRAII&);
};

// Test modern memory management functionality
ENGINE_API int TestModernMemoryManagement() {
    g_DebugLog("Starting modern memory management test...");
    
    // Test 1: Basic unique_ptr operations
    g_DebugLog("=== Test 1: Basic unique_ptr Operations ===");
    
    int initialCount = TestObject::GetInstanceCount();
    
    {
        std::unique_ptr<TestObject> ptr1(new TestObject(1));
        
        if (!ptr1) {
            g_DebugLog("ERROR: unique_ptr should not be null");
            return -1;
        }
        
        if (ptr1->GetId() != 1) {
            g_DebugLog("ERROR: Object ID mismatch");
            return -2;
        }
        
        ptr1->DoWork();
        
        // Test move semantics
        std::unique_ptr<TestObject> ptr2 = std::move(ptr1);
        
        if (ptr1) {
            g_DebugLog("ERROR: Moved-from unique_ptr should be null");
            return -3;
        }
        
        if (!ptr2 || ptr2->GetId() != 1) {
            g_DebugLog("ERROR: Move failed");
            return -4;
        }
        
        g_DebugLog("unique_ptr operations passed");
    }
    
    if (TestObject::GetInstanceCount() != initialCount) {
        g_DebugLog("ERROR: Object not properly destroyed. Count: %d", TestObject::GetInstanceCount());
        return -5;
    }
    
    // Test 2: shared_ptr operations
    g_DebugLog("=== Test 2: shared_ptr Operations ===");
    
    std::shared_ptr<TestObject> shared1;
    std::shared_ptr<TestObject> shared2;
    
    {
        shared1 = std::shared_ptr<TestObject>(new TestObject(2));
        
        if (shared1.use_count() != 1) {
            g_DebugLog("ERROR: Initial use_count should be 1, got %ld", shared1.use_count());
            return -6;
        }
        
        shared2 = shared1;
        
        if (shared1.use_count() != 2 || shared2.use_count() != 2) {
            g_DebugLog("ERROR: Use count should be 2 after copy");
            return -7;
        }
        
        if (shared1.get() != shared2.get()) {
            g_DebugLog("ERROR: shared_ptr should point to same object");
            return -8;
        }
        
        shared1.reset();
        
        if (shared2.use_count() != 1) {
            g_DebugLog("ERROR: Use count should be 1 after reset");
            return -9;
        }
        
        g_DebugLog("shared_ptr operations passed");
    }
    
    shared2.reset();
    
    if (TestObject::GetInstanceCount() != initialCount) {
        g_DebugLog("ERROR: shared_ptr object not properly destroyed");
        return -10;
    }
    
    // Test 3: RAII pattern
    g_DebugLog("=== Test 3: RAII Pattern ===");
    
    {
        SimpleRAII raii(1024);
        
        if (!raii.IsValid()) {
            g_DebugLog("ERROR: RAII allocation failed");
            return -11;
        }
        
        if (raii.Size() != 1024) {
            g_DebugLog("ERROR: RAII size mismatch");
            return -12;
        }
        
        // Test memory access
        char* memory = static_cast<char*>(raii.Get());
        memory[0] = 'A';
        memory[1023] = 'Z';
        
        if (memory[0] != 'A' || memory[1023] != 'Z') {
            g_DebugLog("ERROR: Memory access failed");
            return -13;
        }
        
        g_DebugLog("RAII pattern passed");
    }
    
    // Test 4: Container with smart pointers
    g_DebugLog("=== Test 4: Container with Smart Pointers ===");
    
    {
        std::vector<std::unique_ptr<TestObject> > objects;
        
        // Add objects to container
        for (int i = 0; i < 5; ++i) {
            objects.push_back(std::unique_ptr<TestObject>(new TestObject(10 + i)));
        }
        
        if (objects.size() != 5) {
            g_DebugLog("ERROR: Container size mismatch");
            return -14;
        }
        
        if (TestObject::GetInstanceCount() != initialCount + 5) {
            g_DebugLog("ERROR: Object count mismatch. Expected: %d, Got: %d", 
                       initialCount + 5, TestObject::GetInstanceCount());
            return -15;
        }
        
        // Access objects
        for (size_t i = 0; i < objects.size(); ++i) {
            if (objects[i]->GetId() != 10 + (int)i) {
                g_DebugLog("ERROR: Object ID mismatch in container");
                return -16;
            }
            objects[i]->DoWork();
        }
        
        // Remove some objects
        objects.erase(objects.begin() + 2);  // Remove object with ID 12
        
        if (objects.size() != 4) {
            g_DebugLog("ERROR: Container size after erase");
            return -17;
        }
        
        g_DebugLog("Container with smart pointers passed");
    }
    
    if (TestObject::GetInstanceCount() != initialCount) {
        g_DebugLog("ERROR: Container objects not properly destroyed");
        return -18;
    }
    
    // Test 5: Exception safety
    g_DebugLog("=== Test 5: Exception Safety ===");
    
    bool exceptionHandled = false;
    
    try {
        std::unique_ptr<TestObject> ptr(new TestObject(100));
        
        // Simulate exception (without actually throwing)
        if (ptr->GetId() == 100) {
            exceptionHandled = true;
            g_DebugLog("Exception safety simulation completed");
        }
        
    } catch (...) {
        exceptionHandled = true;
        g_DebugLog("Exception caught and handled");
    }
    
    if (!exceptionHandled) {
        g_DebugLog("ERROR: Exception safety test failed");
        return -19;
    }
    
    if (TestObject::GetInstanceCount() != initialCount) {
        g_DebugLog("ERROR: Exception safety - object not properly destroyed");
        return -20;
    }
    
    g_DebugLog("Exception safety passed");
    
    // Test 6: Memory leak detection
    g_DebugLog("=== Test 6: Memory Leak Detection ===");
    
    int beforeCount = TestObject::GetInstanceCount();
    
    {
        // Create and destroy objects in nested scope
        std::unique_ptr<TestObject> temp1(new TestObject(200));
        std::shared_ptr<TestObject> temp2(new TestObject(201));
        SimpleRAII temp3(512);
        
        g_DebugLog("Created temporary objects");
    }
    
    int afterCount = TestObject::GetInstanceCount();
    
    if (beforeCount != afterCount) {
        g_DebugLog("ERROR: Memory leak detected. Before: %d, After: %d", beforeCount, afterCount);
        return -21;
    }
    
    g_DebugLog("Memory leak detection passed");
    
    // Test 7: Performance comparison
    g_DebugLog("=== Test 7: Performance Comparison ===");
    
    const int ITERATIONS = 1000;
    
    // Test raw pointer performance (for comparison)
    {
        for (int i = 0; i < ITERATIONS; ++i) {
            TestObject* raw = new TestObject(1000 + i);
            raw->DoWork();
            delete raw;
        }
        g_DebugLog("Raw pointer test completed: %d iterations", ITERATIONS);
    }
    
    // Test smart pointer performance
    {
        for (int i = 0; i < ITERATIONS; ++i) {
            std::unique_ptr<TestObject> smart(new TestObject(2000 + i));
            smart->DoWork();
            // Automatic cleanup
        }
        g_DebugLog("Smart pointer test completed: %d iterations", ITERATIONS);
    }
    
    if (TestObject::GetInstanceCount() != initialCount) {
        g_DebugLog("ERROR: Performance test - objects not properly cleaned up");
        return -22;
    }
    
    g_DebugLog("Performance comparison passed");
    
    g_DebugLog("=== All modern memory management tests passed! ===");
    return 0;
}

// Test main function
#ifdef _STANDALONE
int main() {
    g_DebugLog("Starting modern memory management test suite...");
    
    int result = TestModernMemoryManagement();
    
    if (result == 0) {
        printf("SUCCESS: All modern memory management tests passed!\n");
    } else {
        printf("FAILED: Modern memory management test failed with code %d\n", result);
    }
    
    return result;
}
#endif
