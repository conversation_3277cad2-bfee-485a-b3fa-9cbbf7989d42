﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{17FBC3FB-A110-38C7-97F5-D3741D7475A7}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>RelayT</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\Sword2Code\test_build\bin\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">RelayT.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">SO2RelayTD</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\Sword2Code\test_build\bin\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">RelayT.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">SO2RelayT</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\Sword2Code\test_build\bin\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">RelayT.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">SO2RelayT</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\Sword2Code\test_build\bin\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">RelayT.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">SO2RelayT</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Server\RelayT;D:\Sword2Code\Sword2\SourceCode\Include\Support;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\DevEnv\Include\mysql;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/Sword2Code/DevEnv/mysql/Include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_DEBUG;_S3RELAY;KG_USE_LARGER_PACKAGE;_RELAYT_TEST_VERSION;_CONSOLE;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_S3RELAY;KG_USE_LARGER_PACKAGE;_RELAYT_TEST_VERSION;_CONSOLE;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Server\RelayT;D:\Sword2Code\Sword2\SourceCode\Include\Support;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\DevEnv\Include\mysql;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;D:\Sword2Code\DevEnv\mysql\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Server\RelayT;D:\Sword2Code\Sword2\SourceCode\Include\Support;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\DevEnv\Include\mysql;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;D:\Sword2Code\DevEnv\mysql\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\..\..\..\lib\Debug\CoreD.lib;..\..\..\..\lib\Debug\EngineD.lib;..\..\..\..\lib\Debug\Lua5D.lib;D:\Sword2Code\DevEnv\mysql\lib\opt\mysqlclient.lib;ws2_32.lib;winmm.lib;D:\Sword2Code\DevEnv\dx9\lib\release\d3d9.lib;D:\Sword2Code\DevEnv\dx9\lib\release\ddraw.lib;D:\Sword2Code\DevEnv\dx9\lib\release\dsound.lib;D:\Sword2Code\DevEnv\dx9\lib\release\dinput8.lib;D:\Sword2Code\DevEnv\dx9\lib\release\dxguid.lib;advapi32.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/Sword2Code/test_build/lib/Debug/SO2RelayTD.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/Sword2Code/test_build/bin/Debug/SO2RelayTD.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Server\RelayT;D:\Sword2Code\Sword2\SourceCode\Include\Support;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\DevEnv\Include\mysql;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/Sword2Code/DevEnv/mysql/Include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_S3RELAY;KG_USE_LARGER_PACKAGE;_RELAYT_TEST_VERSION;_CONSOLE;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_S3RELAY;KG_USE_LARGER_PACKAGE;_RELAYT_TEST_VERSION;_CONSOLE;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Server\RelayT;D:\Sword2Code\Sword2\SourceCode\Include\Support;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\DevEnv\Include\mysql;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;D:\Sword2Code\DevEnv\mysql\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Server\RelayT;D:\Sword2Code\Sword2\SourceCode\Include\Support;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\DevEnv\Include\mysql;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;D:\Sword2Code\DevEnv\mysql\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\..\..\..\lib\Release\Core.lib;..\..\..\..\lib\Release\Engine.lib;..\..\..\..\lib\Release\Lua5.lib;D:\Sword2Code\DevEnv\mysql\lib\opt\mysqlclient.lib;ws2_32.lib;winmm.lib;D:\Sword2Code\DevEnv\dx9\lib\release\d3d9.lib;D:\Sword2Code\DevEnv\dx9\lib\release\ddraw.lib;D:\Sword2Code\DevEnv\dx9\lib\release\dsound.lib;D:\Sword2Code\DevEnv\dx9\lib\release\dinput8.lib;D:\Sword2Code\DevEnv\dx9\lib\release\dxguid.lib;advapi32.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/Sword2Code/test_build/lib/Release/SO2RelayT.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/Sword2Code/test_build/bin/Release/SO2RelayT.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Server\RelayT;D:\Sword2Code\Sword2\SourceCode\Include\Support;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\DevEnv\Include\mysql;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/Sword2Code/DevEnv/mysql/Include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_S3RELAY;KG_USE_LARGER_PACKAGE;_RELAYT_TEST_VERSION;_CONSOLE;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_S3RELAY;KG_USE_LARGER_PACKAGE;_RELAYT_TEST_VERSION;_CONSOLE;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Server\RelayT;D:\Sword2Code\Sword2\SourceCode\Include\Support;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\DevEnv\Include\mysql;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;D:\Sword2Code\DevEnv\mysql\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Server\RelayT;D:\Sword2Code\Sword2\SourceCode\Include\Support;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\DevEnv\Include\mysql;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;D:\Sword2Code\DevEnv\mysql\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\..\..\..\lib\MinSizeRel\Core.lib;..\..\..\..\lib\MinSizeRel\Engine.lib;..\..\..\..\lib\MinSizeRel\Lua5.lib;D:\Sword2Code\DevEnv\mysql\lib\opt\mysqlclient.lib;ws2_32.lib;winmm.lib;D:\Sword2Code\DevEnv\dx9\lib\release\d3d9.lib;D:\Sword2Code\DevEnv\dx9\lib\release\ddraw.lib;D:\Sword2Code\DevEnv\dx9\lib\release\dsound.lib;D:\Sword2Code\DevEnv\dx9\lib\release\dinput8.lib;D:\Sword2Code\DevEnv\dx9\lib\release\dxguid.lib;advapi32.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/Sword2Code/test_build/lib/MinSizeRel/SO2RelayT.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/Sword2Code/test_build/bin/MinSizeRel/SO2RelayT.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Server\RelayT;D:\Sword2Code\Sword2\SourceCode\Include\Support;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\DevEnv\Include\mysql;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/Sword2Code/DevEnv/mysql/Include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_S3RELAY;KG_USE_LARGER_PACKAGE;_RELAYT_TEST_VERSION;_CONSOLE;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_S3RELAY;KG_USE_LARGER_PACKAGE;_RELAYT_TEST_VERSION;_CONSOLE;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Server\RelayT;D:\Sword2Code\Sword2\SourceCode\Include\Support;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\DevEnv\Include\mysql;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;D:\Sword2Code\DevEnv\mysql\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Server\RelayT;D:\Sword2Code\Sword2\SourceCode\Include\Support;D:\Sword2Code\Sword2\SourceCode\Common\Support;D:\Sword2Code\DevEnv\Include\mysql;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src;D:\Sword2Code\Sword2\SourceCode\Common\Core;D:\Sword2Code\Sword2\SourceCode\Include\Common;D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO;D:\Sword2Code\DevEnv\Include\Lua5;D:\Sword2Code\DevEnv\Lua5\src;D:\Sword2Code\Sword2\SourceCode\Engine\Src;D:\Sword2Code\DevEnv\mysql\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\..\..\..\lib\RelWithDebInfo\Core.lib;..\..\..\..\lib\RelWithDebInfo\Engine.lib;..\..\..\..\lib\RelWithDebInfo\Lua5.lib;D:\Sword2Code\DevEnv\mysql\lib\opt\mysqlclient.lib;ws2_32.lib;winmm.lib;D:\Sword2Code\DevEnv\dx9\lib\release\d3d9.lib;D:\Sword2Code\DevEnv\dx9\lib\release\ddraw.lib;D:\Sword2Code\DevEnv\dx9\lib\release\dsound.lib;D:\Sword2Code\DevEnv\dx9\lib\release\dinput8.lib;D:\Sword2Code\DevEnv\dx9\lib\release\dxguid.lib;advapi32.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/Sword2Code/test_build/lib/RelWithDebInfo/SO2RelayT.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/Sword2Code/test_build/bin/RelWithDebInfo/SO2RelayT.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/Sword2Code/Sword2/SourceCode/Server/RelayT/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/Sword2Code -BD:/Sword2Code/test_build --check-stamp-file D:/Sword2Code/test_build/Sword2/SourceCode/Server/RelayT/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\Sword2Code\test_build\Sword2\SourceCode\Server\RelayT\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/Sword2Code/Sword2/SourceCode/Server/RelayT/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/Sword2Code -BD:/Sword2Code/test_build --check-stamp-file D:/Sword2Code/test_build/Sword2/SourceCode/Server/RelayT/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\Sword2Code\test_build\Sword2\SourceCode\Server\RelayT\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule D:/Sword2Code/Sword2/SourceCode/Server/RelayT/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/Sword2Code -BD:/Sword2Code/test_build --check-stamp-file D:/Sword2Code/test_build/Sword2/SourceCode/Server/RelayT/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\Sword2Code\test_build\Sword2\SourceCode\Server\RelayT\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule D:/Sword2Code/Sword2/SourceCode/Server/RelayT/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/Sword2Code -BD:/Sword2Code/test_build --check-stamp-file D:/Sword2Code/test_build/Sword2/SourceCode/Server/RelayT/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\Sword2Code\test_build\Sword2\SourceCode\Server\RelayT\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\S3Relay.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\Global.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\StdAfx.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\DoScript.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\KG_RelayDatabase.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\ShareDataBase.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\NetConnect.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\NetServer.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\HostConnect.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\HostServer.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\TongConnect.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\TongServer.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\ChatConnect.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\ChatServer.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\ChannelMgr.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\PlayerRelationMgr.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\OfflineMsgStore.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\RoleProcess.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\Test.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\MapArrangeCentre.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\KGlobalValues.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\TaskCentre.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\KEconomyCentre.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\KIBInfo.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\KCustomData.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\KCustomRankData.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\Frame\RelayConfig.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\MasterAndPrentice.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\KRenameDaemon.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\MissionEx\kvariant.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\MissionEx\MissionEx.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\MissionEx\MissionExManager.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\ScriptFuncComm.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\Outside\GmcConnection.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\Outside\GmcMsgProcessor.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\Outside\BishopConnection.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\Outside\BishopMsgProcessor.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\Outside\RoleDbConnection.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongCenter.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongDuty.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongManager.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongMember.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongProcessor.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongSync.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongTeam.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongTechControl.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongTechRes.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongMagicAttribute.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\TongCenter\TongSaveLoad.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KPlayerTreasure.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KMailAttachmentImp.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KMailImp.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KPostOffice_Entry.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KPostOfficeImp.cpp" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\S3Relay.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\Global.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\StdAfx.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\NetConnect.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\NetServer.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\HostConnect.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\HostServer.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\TongConnect.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\TongServer.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\ChatConnect.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\ChatServer.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\ChannelMgr.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\PlayerRelationMgr.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Server\RelayT\Frame\RelayConfig.h" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\Sword2Code\test_build\ZERO_CHECK.vcxproj">
      <Project>{5056683F-7A79-357B-A3C6-E0F657A7870C}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\Sword2Code\test_build\Sword2\SourceCode\Common\Core\Core.vcxproj">
      <Project>{CFD13F0C-2493-3B4A-82F5-55C7C73BF55A}</Project>
      <Name>Core</Name>
    </ProjectReference>
    <ProjectReference Include="D:\Sword2Code\test_build\Sword2\SourceCode\Engine\Engine.vcxproj">
      <Project>{1946B23A-828A-3EB6-83E5-5A3787A50438}</Project>
      <Name>Engine</Name>
    </ProjectReference>
    <ProjectReference Include="D:\Sword2Code\test_build\DevEnv\Lua5\Lua5.vcxproj">
      <Project>{C9F953A3-9235-3E02-90C6-F745942FD865}</Project>
      <Name>Lua5</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>