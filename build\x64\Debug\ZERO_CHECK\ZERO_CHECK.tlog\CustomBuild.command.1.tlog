^D:\SWORD2CODE\BUILD\CMAKEFILES\ACD1F1ED913889536E4D8C1E67C79A40\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/Sword2Code -BD:/Sword2Code/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/Sword2Code/build/Sword2.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
