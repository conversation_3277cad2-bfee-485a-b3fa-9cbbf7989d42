//---------------------------------------------------------------------------
// Sword3 Engine - Modernized Sorted List System Implementation
// 现代化的排序列表系统实现
//---------------------------------------------------------------------------
#include "KSortList_modern.h"
#include <sstream>
#include <numeric>

// Forward declaration for debug function
extern void g_DebugLog(const char* fmt, ...);

//---------------------------------------------------------------------------
// 构造函数和析构函数
//---------------------------------------------------------------------------
template<typename T>
KSortList<T>::KSortList(bool useMapMode) : m_useMap(useMapMode) {
}

template<typename T>
KSortList<T>::~KSortList() {
    Clear();
}

//---------------------------------------------------------------------------
// 兼容性接口实现
//---------------------------------------------------------------------------
template<typename T>
BOOL KSortList<T>::Init(int nItemSize, int nItemCount) {
    Clear();
    if (nItemCount > 0) {
        if (m_useMap) {
            // Map模式不需要预分配
        } else {
            m_items.reserve(nItemCount);
        }
    }
    return TRUE;
}

template<typename T>
BOOL KSortList<T>::Insert(DWORD dwHashCode, const T& item) {
    return Insert(dwHashCode, item) ? TRUE : FALSE;
}

template<typename T>
BOOL KSortList<T>::Search(DWORD dwHashCode, T& result) const {
    return Find(dwHashCode, result) ? TRUE : FALSE;
}

template<typename T>
void KSortList<T>::Free() {
    Clear();
}

//---------------------------------------------------------------------------
// 现代化接口实现
//---------------------------------------------------------------------------
template<typename T>
bool KSortList<T>::Insert(DWORD hashCode, const T& item) {
    if (m_useMap) {
        m_sortedMap[hashCode] = item;
        return true;
    } else {
        // 检查是否已存在
        for (auto& sortItem : m_items) {
            if (sortItem.hashCode == hashCode) {
                sortItem.data = item;  // 更新现有项
                return true;
            }
        }
        
        // 添加新项并保持排序
        SortItem<T> newItem(hashCode, item);
        auto it = std::lower_bound(m_items.begin(), m_items.end(), newItem);
        m_items.insert(it, newItem);
        return true;
    }
}

template<typename T>
bool KSortList<T>::Find(DWORD hashCode, T& result) const {
    if (m_useMap) {
        auto it = m_sortedMap.find(hashCode);
        if (it != m_sortedMap.end()) {
            result = it->second;
            return true;
        }
        return false;
    } else {
        SortItem<T> searchItem(hashCode, T{});
        auto it = std::lower_bound(m_items.begin(), m_items.end(), searchItem);
        if (it != m_items.end() && it->hashCode == hashCode) {
            result = it->data;
            return true;
        }
        return false;
    }
}

template<typename T>
bool KSortList<T>::Contains(DWORD hashCode) const {
    if (m_useMap) {
        return m_sortedMap.find(hashCode) != m_sortedMap.end();
    } else {
        SortItem<T> searchItem(hashCode, T{});
        auto it = std::lower_bound(m_items.begin(), m_items.end(), searchItem);
        return it != m_items.end() && it->hashCode == hashCode;
    }
}

template<typename T>
bool KSortList<T>::Remove(DWORD hashCode) {
    if (m_useMap) {
        auto it = m_sortedMap.find(hashCode);
        if (it != m_sortedMap.end()) {
            m_sortedMap.erase(it);
            return true;
        }
        return false;
    } else {
        SortItem<T> searchItem(hashCode, T{});
        auto it = std::lower_bound(m_items.begin(), m_items.end(), searchItem);
        if (it != m_items.end() && it->hashCode == hashCode) {
            m_items.erase(it);
            return true;
        }
        return false;
    }
}

template<typename T>
void KSortList<T>::Clear() {
    m_sortedMap.clear();
    m_items.clear();
}

//---------------------------------------------------------------------------
// 访问操作
//---------------------------------------------------------------------------
template<typename T>
T* KSortList<T>::Get(DWORD hashCode) {
    if (m_useMap) {
        auto it = m_sortedMap.find(hashCode);
        return (it != m_sortedMap.end()) ? &(it->second) : nullptr;
    } else {
        SortItem<T> searchItem(hashCode, T{});
        auto it = std::lower_bound(m_items.begin(), m_items.end(), searchItem);
        return (it != m_items.end() && it->hashCode == hashCode) ? &(it->data) : nullptr;
    }
}

template<typename T>
const T* KSortList<T>::Get(DWORD hashCode) const {
    if (m_useMap) {
        auto it = m_sortedMap.find(hashCode);
        return (it != m_sortedMap.end()) ? &(it->second) : nullptr;
    } else {
        SortItem<T> searchItem(hashCode, T{});
        auto it = std::lower_bound(m_items.begin(), m_items.end(), searchItem);
        return (it != m_items.end() && it->hashCode == hashCode) ? &(it->data) : nullptr;
    }
}

template<typename T>
T& KSortList<T>::operator[](DWORD hashCode) {
    if (m_useMap) {
        return m_sortedMap[hashCode];
    } else {
        SortItem<T> searchItem(hashCode, T{});
        auto it = std::lower_bound(m_items.begin(), m_items.end(), searchItem);
        if (it != m_items.end() && it->hashCode == hashCode) {
            return it->data;
        } else {
            // 插入新项
            SortItem<T> newItem(hashCode, T{});
            it = m_items.insert(it, newItem);
            return it->data;
        }
    }
}

template<typename T>
const T& KSortList<T>::operator[](DWORD hashCode) const {
    if (m_useMap) {
        return m_sortedMap.at(hashCode);
    } else {
        SortItem<T> searchItem(hashCode, T{});
        auto it = std::lower_bound(m_items.begin(), m_items.end(), searchItem);
        if (it != m_items.end() && it->hashCode == hashCode) {
            return it->data;
        } else {
            throw std::out_of_range("Hash code not found");
        }
    }
}

//---------------------------------------------------------------------------
// 容器操作
//---------------------------------------------------------------------------
template<typename T>
size_t KSortList<T>::Size() const {
    return m_useMap ? m_sortedMap.size() : m_items.size();
}

template<typename T>
bool KSortList<T>::Empty() const {
    return m_useMap ? m_sortedMap.empty() : m_items.empty();
}

template<typename T>
void KSortList<T>::Reserve(size_t capacity) {
    if (!m_useMap) {
        m_items.reserve(capacity);
    }
}

//---------------------------------------------------------------------------
// 遍历操作
//---------------------------------------------------------------------------
template<typename T>
std::vector<DWORD> KSortList<T>::GetHashCodes() const {
    std::vector<DWORD> hashCodes;
    
    if (m_useMap) {
        for (const auto& pair : m_sortedMap) {
            hashCodes.push_back(pair.first);
        }
    } else {
        for (const auto& item : m_items) {
            hashCodes.push_back(item.hashCode);
        }
    }
    
    return hashCodes;
}

template<typename T>
std::vector<T> KSortList<T>::GetValues() const {
    std::vector<T> values;
    
    if (m_useMap) {
        for (const auto& pair : m_sortedMap) {
            values.push_back(pair.second);
        }
    } else {
        for (const auto& item : m_items) {
            values.push_back(item.data);
        }
    }
    
    return values;
}

template<typename T>
std::vector<SortItem<T>> KSortList<T>::GetAllItems() const {
    std::vector<SortItem<T>> items;
    
    if (m_useMap) {
        for (const auto& pair : m_sortedMap) {
            items.push_back(SortItem<T>(pair.first, pair.second));
        }
    } else {
        items = m_items;
    }
    
    return items;
}

//---------------------------------------------------------------------------
// 范围查询
//---------------------------------------------------------------------------
template<typename T>
std::vector<SortItem<T>> KSortList<T>::GetRange(DWORD minHash, DWORD maxHash) const {
    std::vector<SortItem<T>> result;
    
    if (m_useMap) {
        auto lower = m_sortedMap.lower_bound(minHash);
        auto upper = m_sortedMap.upper_bound(maxHash);
        
        for (auto it = lower; it != upper; ++it) {
            result.push_back(SortItem<T>(it->first, it->second));
        }
    } else {
        for (const auto& item : m_items) {
            if (item.hashCode >= minHash && item.hashCode <= maxHash) {
                result.push_back(item);
            }
        }
    }
    
    return result;
}

template<typename T>
std::vector<SortItem<T>> KSortList<T>::GetFirst(size_t count) const {
    std::vector<SortItem<T>> result;
    
    if (m_useMap) {
        size_t i = 0;
        for (const auto& pair : m_sortedMap) {
            if (i >= count) break;
            result.push_back(SortItem<T>(pair.first, pair.second));
            i++;
        }
    } else {
        size_t actualCount = std::min(count, m_items.size());
        result.assign(m_items.begin(), m_items.begin() + actualCount);
    }
    
    return result;
}

template<typename T>
std::vector<SortItem<T>> KSortList<T>::GetLast(size_t count) const {
    std::vector<SortItem<T>> result;
    
    if (m_useMap) {
        if (count >= m_sortedMap.size()) {
            // 返回所有项
            for (const auto& pair : m_sortedMap) {
                result.push_back(SortItem<T>(pair.first, pair.second));
            }
        } else {
            // 从后往前取count个
            auto it = m_sortedMap.end();
            for (size_t i = 0; i < count; ++i) {
                --it;
                result.insert(result.begin(), SortItem<T>(it->first, it->second));
            }
        }
    } else {
        if (count >= m_items.size()) {
            result = m_items;
        } else {
            result.assign(m_items.end() - count, m_items.end());
        }
    }
    
    return result;
}

// 排序和查找
//---------------------------------------------------------------------------
template<typename T>
void KSortList<T>::SortByHash(bool ascending) {
    if (!m_useMap) {
        if (ascending) {
            std::sort(m_items.begin(), m_items.end());
        } else {
            std::sort(m_items.begin(), m_items.end(),
                     [](const SortItem<T>& a, const SortItem<T>& b) {
                         return a.hashCode > b.hashCode;
                     });
        }
    }
    // Map模式自动排序，无需手动排序
}

template<typename T>
void KSortList<T>::SortByValue(std::function<bool(const T&, const T&)> comparator) {
    if (!m_useMap) {
        std::sort(m_items.begin(), m_items.end(),
                 [&comparator](const SortItem<T>& a, const SortItem<T>& b) {
                     return comparator(a.data, b.data);
                 });
    } else {
        // 对于Map模式，需要转换为vector进行排序
        SyncMapToVector();
        m_useMap = false;
        SortByValue(comparator);
    }
}

// 调试和输出
template<typename T>
void KSortList<T>::DebugPrint() const {
    g_DebugLog("KSortList: %d items (mode: %s)",
               (int)Size(), m_useMap ? "Map" : "Vector");

    auto items = GetAllItems();
    int count = 0;
    for (const auto& item : items) {
        if (count < 10) {  // 只显示前10个
            g_DebugLog("  [%d] Hash: %u", count, item.hashCode);
        }
        count++;
    }

    if (count > 10) {
        g_DebugLog("  ... and %d more items", count - 10);
    }
}

template<typename T>
std::string KSortList<T>::ToString() const {
    std::ostringstream oss;
    oss << "KSortList[" << Size() << " items, "
        << (m_useMap ? "Map" : "Vector") << " mode]";
    return oss.str();
}

// 内部辅助函数
template<typename T>
void KSortList<T>::SyncMapToVector() {
    m_items.clear();
    for (const auto& pair : m_sortedMap) {
        m_items.push_back(SortItem<T>(pair.first, pair.second));
    }
    m_sortedMap.clear();
}

template<typename T>
void KSortList<T>::SyncVectorToMap() {
    m_sortedMap.clear();
    for (const auto& item : m_items) {
        m_sortedMap[item.hashCode] = item.data;
    }
    m_items.clear();
}

template<typename T>
DWORD KSortList<T>::GenerateHash(const T& item) const {
    // 简单的哈希生成，实际使用中可能需要更复杂的实现
    return static_cast<DWORD>(std::hash<T>{}(item));
}

// 显式模板实例化（常用类型）
template class KSortList<int>;
template class KSortList<float>;
template class KSortList<DWORD>;
template class KSortList<void*>;

//---------------------------------------------------------------------------
