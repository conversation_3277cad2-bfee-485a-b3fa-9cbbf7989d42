d:\sword2code\build\sword2\sourcecode\engine\minimaltest.dir\debug\test_minimal.obj
d:\sword2code\build\sword2\sourcecode\engine\cmakefiles\generate.stamp
d:\sword2code\build\bin\debug\minimaltest.exe
d:\sword2code\build\bin\debug\minimaltest.pdb
d:\sword2code\build\sword2\sourcecode\engine\minimaltest.dir\debug\minimaltest.ilk
d:\sword2code\build\sword2\sourcecode\engine\minimaltest.dir\debug\minimaltest.tlog\cl.command.1.tlog
d:\sword2code\build\sword2\sourcecode\engine\minimaltest.dir\debug\minimaltest.tlog\cl.items.tlog
d:\sword2code\build\sword2\sourcecode\engine\minimaltest.dir\debug\minimaltest.tlog\cl.read.1.tlog
d:\sword2code\build\sword2\sourcecode\engine\minimaltest.dir\debug\minimaltest.tlog\cl.write.1.tlog
d:\sword2code\build\sword2\sourcecode\engine\minimaltest.dir\debug\minimaltest.tlog\custombuild.command.1.tlog
d:\sword2code\build\sword2\sourcecode\engine\minimaltest.dir\debug\minimaltest.tlog\custombuild.read.1.tlog
d:\sword2code\build\sword2\sourcecode\engine\minimaltest.dir\debug\minimaltest.tlog\custombuild.write.1.tlog
d:\sword2code\build\sword2\sourcecode\engine\minimaltest.dir\debug\minimaltest.tlog\link.command.1.tlog
d:\sword2code\build\sword2\sourcecode\engine\minimaltest.dir\debug\minimaltest.tlog\link.read.1.tlog
d:\sword2code\build\sword2\sourcecode\engine\minimaltest.dir\debug\minimaltest.tlog\link.secondary.1.tlog
d:\sword2code\build\sword2\sourcecode\engine\minimaltest.dir\debug\minimaltest.tlog\link.write.1.tlog
