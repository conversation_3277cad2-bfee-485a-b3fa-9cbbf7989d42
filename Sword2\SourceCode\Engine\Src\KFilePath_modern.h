//---------------------------------------------------------------------------
// Sword3 Engine - Modernized File Path System
// 现代化的文件路径系统 - 使用std::filesystem
//---------------------------------------------------------------------------
#ifndef KFilePath_Modern_H
#define KFilePath_Modern_H

#include "KWin32.h"
#include <filesystem>
#include <string>

//---------------------------------------------------------------------------
#ifndef MAXPATH
#define MAXPATH   260
#define MAXDIR    256
#define MAXFILE   256
#define MAXEXT    256
#define MAXDRIVE    3
#endif

//---------------------------------------------------------------------------
// 保持原有接口兼容性，内部使用现代std::filesystem实现
//---------------------------------------------------------------------------

ENGINE_API void g_SetRootPath(LPSTR lpPathName = NULL);
ENGINE_API void g_GetRootPath(LPSTR lpPathName);
ENGINE_API void g_SetFilePath(LPSTR lpPathName);
ENGINE_API void g_GetFilePath(LPSTR lpPathName);
ENGINE_API void g_GetFullPath(LPSTR lpPathName, LPSTR lpFileName);
ENGINE_API void g_GetHalfPath(LPSTR lpPathName, LPSTR lpFileName);
ENGINE_API void g_GetPackPath(LPSTR lpPathName, LPSTR lpFileName);
ENGINE_API void g_GetDiskPath(LPSTR lpPathName, LPSTR lpFileName);
ENGINE_API void g_CreatePath(LPSTR lpPathName);
ENGINE_API void g_UnitePathAndName(char *lpPath, char *lpFile, char *lpGet);

ENGINE_API BOOL g_FileExists(LPSTR lpPathName);
ENGINE_API DWORD g_FileName2Id(LPSTR lpFileName);
ENGINE_API void g_ChangeFileExt(LPSTR lpFileName, LPSTR lpNewExt);
ENGINE_API void g_ExtractFileName(LPSTR lpFileName, LPSTR lpFilePath);
ENGINE_API void g_ExtractFilePath(LPSTR lpPathName, LPSTR lpFilePath);

//---------------------------------------------------------------------------
// 现代化扩展接口（可选使用）
//---------------------------------------------------------------------------
namespace ModernPath {
    std::filesystem::path GetRootPath();
    void SetRootPath(const std::filesystem::path& path);
    std::filesystem::path GetFullPath(const std::string& fileName);
    bool FileExists(const std::filesystem::path& path);
    void CreateDirectories(const std::filesystem::path& path);
    std::string GetFileName(const std::filesystem::path& path);
    std::string GetFileExtension(const std::filesystem::path& path);
    std::filesystem::path GetParentPath(const std::filesystem::path& path);
}

//---------------------------------------------------------------------------
#endif // KFilePath_Modern_H
