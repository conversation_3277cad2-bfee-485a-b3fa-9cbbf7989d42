//---------------------------------------------------------------------------
// Sword3 Engine - Modernized String List Implementation
// 现代化的字符串列表实现
//---------------------------------------------------------------------------
#include "KStrList_modern.h"
#include <fstream>
#include <sstream>

// Forward declaration for debug function
extern void g_DebugLog(LPSTR Fmt, ...);

//---------------------------------------------------------------------------
// 构造函数和析构函数
//---------------------------------------------------------------------------
KStrList::KStrList() : m_indexDirty(true) {
}

KStrList::~KStrList() {
    Clear();
}

//---------------------------------------------------------------------------
// 兼容性接口实现
//---------------------------------------------------------------------------
KStrNode* KStrList::Find(char* str) {
    return Find(static_cast<const char*>(str));
}

KStrNode* KStrList::Find(const char* str) {
    if (!str) return nullptr;
    return Find(std::string(str));
}

//---------------------------------------------------------------------------
// 现代化接口实现
//---------------------------------------------------------------------------
KStrNode* KStrList::Find(const std::string& str) {
    if (m_indexDirty) {
        RebuildIndex();
    }
    
    auto it = m_nameIndex.find(str);
    return (it != m_nameIndex.end()) ? it->second : nullptr;
}

KStrNode* KStrList::FindIgnoreCase(const std::string& str) {
    std::string lowerStr = ModernString::ToLower(str);
    
    KStrNode* current = static_cast<KStrNode*>(GetHead());
    while (current) {
        if (current->EqualsIgnoreCase(lowerStr)) {
            return current;
        }
        current = static_cast<KStrNode*>(current->GetNext());
    }
    
    return nullptr;
}

//---------------------------------------------------------------------------
// 添加和删除操作
//---------------------------------------------------------------------------
void KStrList::AddString(const char* str) {
    if (str) {
        AddString(std::string(str));
    }
}

void KStrList::AddString(const std::string& str) {
    KStrNode* node = new KStrNode(str);
    AddTail(node);
}

void KStrList::AddStringNode(KStrNode* node) {
    if (node) {
        AddTail(node);
    }
}

bool KStrList::RemoveString(const char* str) {
    if (!str) return false;
    return RemoveString(std::string(str));
}

bool KStrList::RemoveString(const std::string& str) {
    KStrNode* node = Find(str);
    if (node) {
        RemoveNode(node);
        delete node;
        return true;
    }
    return false;
}

//---------------------------------------------------------------------------
// 查询操作
//---------------------------------------------------------------------------
bool KStrList::Contains(const char* str) {
    return Find(str) != nullptr;
}

bool KStrList::Contains(const std::string& str) {
    return Find(str) != nullptr;
}

bool KStrList::ContainsIgnoreCase(const std::string& str) {
    return FindIgnoreCase(str) != nullptr;
}

size_t KStrList::GetStringCount() const {
    return static_cast<size_t>(GetNodeCount());
}

//---------------------------------------------------------------------------
// 遍历操作
//---------------------------------------------------------------------------
std::vector<std::string> KStrList::GetAllStrings() const {
    std::vector<std::string> result;
    
    KStrNode* current = static_cast<KStrNode*>(GetHead());
    while (current) {
        result.push_back(current->GetNameStr());
        current = static_cast<KStrNode*>(current->GetNext());
    }
    
    return result;
}

std::vector<KStrNode*> KStrList::GetAllNodes() const {
    std::vector<KStrNode*> result;
    
    KStrNode* current = static_cast<KStrNode*>(GetHead());
    while (current) {
        result.push_back(current);
        current = static_cast<KStrNode*>(current->GetNext());
    }
    
    return result;
}

//---------------------------------------------------------------------------
// 排序操作
//---------------------------------------------------------------------------
void KStrList::SortByName(bool ascending) {
    auto nodes = GetAllNodes();
    
    std::sort(nodes.begin(), nodes.end(), 
              [ascending](const KStrNode* a, const KStrNode* b) {
                  return ascending ? (a->GetNameStr() < b->GetNameStr()) 
                                  : (a->GetNameStr() > b->GetNameStr());
              });
    
    // 重新构建链表
    Clear();
    for (KStrNode* node : nodes) {
        AddTail(node);
    }
}

void KStrList::SortByLength(bool ascending) {
    auto nodes = GetAllNodes();
    
    std::sort(nodes.begin(), nodes.end(), 
              [ascending](const KStrNode* a, const KStrNode* b) {
                  return ascending ? (a->GetLength() < b->GetLength()) 
                                  : (a->GetLength() > b->GetLength());
              });
    
    // 重新构建链表
    Clear();
    for (KStrNode* node : nodes) {
        AddTail(node);
    }
}

void KStrList::SortCustom(std::function<bool(const KStrNode*, const KStrNode*)> comparator) {
    auto nodes = GetAllNodes();
    std::sort(nodes.begin(), nodes.end(), comparator);
    
    // 重新构建链表
    Clear();
    for (KStrNode* node : nodes) {
        AddTail(node);
    }
}

//---------------------------------------------------------------------------
// 过滤操作
//---------------------------------------------------------------------------
std::vector<KStrNode*> KStrList::Filter(std::function<bool(const KStrNode*)> predicate) const {
    std::vector<KStrNode*> result;
    
    KStrNode* current = static_cast<KStrNode*>(GetHead());
    while (current) {
        if (predicate(current)) {
            result.push_back(current);
        }
        current = static_cast<KStrNode*>(current->GetNext());
    }
    
    return result;
}

std::vector<KStrNode*> KStrList::FilterByPrefix(const std::string& prefix) const {
    return Filter([&prefix](const KStrNode* node) {
        return node->StartsWith(prefix);
    });
}

std::vector<KStrNode*> KStrList::FilterBySuffix(const std::string& suffix) const {
    return Filter([&suffix](const KStrNode* node) {
        return node->EndsWith(suffix);
    });
}

std::vector<KStrNode*> KStrList::FilterByPattern(const std::string& pattern) const {
    return Filter([&pattern](const KStrNode* node) {
        return node->Contains(pattern);
    });
}

//---------------------------------------------------------------------------
// 聚合操作
//---------------------------------------------------------------------------
size_t KStrList::CountIf(std::function<bool(const KStrNode*)> predicate) const {
    size_t count = 0;
    
    KStrNode* current = static_cast<KStrNode*>(GetHead());
    while (current) {
        if (predicate(current)) {
            count++;
        }
        current = static_cast<KStrNode*>(current->GetNext());
    }
    
    return count;
}

KStrNode* KStrList::FindIf(std::function<bool(const KStrNode*)> predicate) const {
    KStrNode* current = static_cast<KStrNode*>(GetHead());
    while (current) {
        if (predicate(current)) {
            return current;
        }
        current = static_cast<KStrNode*>(current->GetNext());
    }
    
    return nullptr;
}

//---------------------------------------------------------------------------
// 字符串操作
//---------------------------------------------------------------------------
void KStrList::ToUpperAll() {
    KStrNode* current = static_cast<KStrNode*>(GetHead());
    while (current) {
        current->ToUpper();
        current = static_cast<KStrNode*>(current->GetNext());
    }
    InvalidateIndex();
}

void KStrList::ToLowerAll() {
    KStrNode* current = static_cast<KStrNode*>(GetHead());
    while (current) {
        current->ToLower();
        current = static_cast<KStrNode*>(current->GetNext());
    }
    InvalidateIndex();
}

void KStrList::TrimAll() {
    KStrNode* current = static_cast<KStrNode*>(GetHead());
    while (current) {
        current->Trim();
        current = static_cast<KStrNode*>(current->GetNext());
    }
    InvalidateIndex();
}

//---------------------------------------------------------------------------
// 导入导出
//---------------------------------------------------------------------------
bool KStrList::LoadFromFile(const char* filename) {
    if (!filename) return false;
    
    std::ifstream file(filename);
    if (!file.is_open()) {
        g_DebugLog("Failed to open file for reading: %s", filename);
        return false;
    }
    
    Clear();
    std::string line;
    while (std::getline(file, line)) {
        if (!line.empty()) {
            AddString(ModernString::Trim(line));
        }
    }
    
    g_DebugLog("Loaded %d strings from file: %s", (int)GetStringCount(), filename);
    return true;
}

bool KStrList::SaveToFile(const char* filename) const {
    if (!filename) return false;
    
    std::ofstream file(filename);
    if (!file.is_open()) {
        g_DebugLog("Failed to open file for writing: %s", filename);
        return false;
    }
    
    KStrNode* current = static_cast<KStrNode*>(GetHead());
    while (current) {
        file << current->GetNameStr() << std::endl;
        current = static_cast<KStrNode*>(current->GetNext());
    }
    
    g_DebugLog("Saved %d strings to file: %s", (int)GetStringCount(), filename);
    return true;
}

void KStrList::LoadFromVector(const std::vector<std::string>& strings) {
    Clear();
    for (const auto& str : strings) {
        AddString(str);
    }
}

std::vector<std::string> KStrList::SaveToVector() const {
    return GetAllStrings();
}

//---------------------------------------------------------------------------
// 调试和输出
//---------------------------------------------------------------------------
void KStrList::DebugPrint() const {
    g_DebugLog("KStrList: %d strings", (int)GetStringCount());
    
    int index = 0;
    KStrNode* current = static_cast<KStrNode*>(GetHead());
    while (current && index < 10) {  // 只显示前10个
        g_DebugLog("  [%d] '%s'", index, current->GetName());
        current = static_cast<KStrNode*>(current->GetNext());
        index++;
    }
    
    if (current) {
        g_DebugLog("  ... and %d more", (int)GetStringCount() - 10);
    }
}

void KStrList::Clear() {
    KList::Clear();
    m_nameIndex.clear();
    m_indexDirty = true;
}

//---------------------------------------------------------------------------
// 内部辅助函数
//---------------------------------------------------------------------------
void KStrList::RebuildIndex() const {
    m_nameIndex.clear();
    
    KStrNode* current = static_cast<KStrNode*>(GetHead());
    while (current) {
        m_nameIndex[current->GetNameStr()] = current;
        current = static_cast<KStrNode*>(current->GetNext());
    }
    
    m_indexDirty = false;
}

void KStrList::InvalidateIndex() {
    m_indexDirty = true;
}

void KStrList::OnNodeAdded(KNode* node) {
    KList::OnNodeAdded(node);
    InvalidateIndex();
}

void KStrList::OnNodeRemoved(KNode* node) {
    KList::OnNodeRemoved(node);
    InvalidateIndex();
}

//---------------------------------------------------------------------------
