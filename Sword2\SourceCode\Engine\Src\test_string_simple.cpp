// Simple string system test - verify basic string operations
#include "KWin32.h"
#include "KStrBase_modern.h"
#include <stdio.h>
#include <stdarg.h>
#include <string>
#include <vector>

// Simple debug implementation for testing
void g_DebugLog(LPSTR Fmt, ...) {
    char buffer[1024];
    va_list va;
    
    va_start(va, Fmt);
    vsnprintf(buffer, sizeof(buffer), Fmt, va);
    va_end(va);
    
    printf("[DEBUG] %s\n", buffer);
}

// Test basic string functionality
ENGINE_API int TestBasicStringOperations() {
    g_DebugLog("Starting basic string operations test...");
    
    // Test 1: Traditional C-style functions
    g_DebugLog("=== Test 1: Traditional C-style Functions ===");
    
    char buffer1[100] = "Hello";
    char buffer2[100] = " World";
    
    // Test string concatenation
    g_StrCat(buffer1, buffer2);
    if (!g_StrCmp(buffer1, "Hello World")) {
        g_DebugLog("ERROR: String concatenation failed. Got: '%s'", buffer1);
        return -1;
    }
    g_DebugLog("String concatenation passed: '%s'", buffer1);
    
    // Test string upper case
    g_StrUpper(buffer1);
    if (!g_StrCmp(buffer1, "HELLO WORLD")) {
        g_DebugLog("ERROR: String upper case failed. Got: '%s'", buffer1);
        return -2;
    }
    g_DebugLog("String upper case passed: '%s'", buffer1);
    
    // Test string lower case
    g_StrLower(buffer1);
    if (!g_StrCmp(buffer1, "hello world")) {
        g_DebugLog("ERROR: String lower case failed. Got: '%s'", buffer1);
        return -3;
    }
    g_DebugLog("String lower case passed: '%s'", buffer1);
    
    // Test 2: Modern string operations
    g_DebugLog("=== Test 2: Modern String Operations ===");
    
    // Test string trimming
    std::string testStr = "  Hello Modern World  ";
    std::string trimmed = ModernString::Trim(testStr);
    if (trimmed != "Hello Modern World") {
        g_DebugLog("ERROR: String trim failed. Got: '%s'", trimmed.c_str());
        return -4;
    }
    g_DebugLog("String trim passed: '%s'", trimmed.c_str());
    
    // Test string upper case
    std::string upper = ModernString::ToUpper(trimmed);
    if (upper != "HELLO MODERN WORLD") {
        g_DebugLog("ERROR: Modern string upper failed. Got: '%s'", upper.c_str());
        return -5;
    }
    g_DebugLog("Modern string upper passed: '%s'", upper.c_str());
    
    // Test string replacement
    std::string replaced = ModernString::Replace(upper, "MODERN", "AWESOME");
    if (replaced != "HELLO AWESOME WORLD") {
        g_DebugLog("ERROR: String replace failed. Got: '%s'", replaced.c_str());
        return -6;
    }
    g_DebugLog("String replace passed: '%s'", replaced.c_str());
    
    // Test 3: String splitting and joining
    g_DebugLog("=== Test 3: String Splitting and Joining ===");
    
    std::string csvData = "apple,banana,cherry,date";
    std::vector<std::string> fruits = ModernString::Split(csvData, ',');
    
    if (fruits.size() != 4) {
        g_DebugLog("ERROR: String split failed. Expected 4 parts, got %d", (int)fruits.size());
        return -7;
    }
    
    if (fruits[0] != "apple" || fruits[1] != "banana" || fruits[2] != "cherry" || fruits[3] != "date") {
        g_DebugLog("ERROR: String split content failed");
        for (size_t i = 0; i < fruits.size(); i++) {
            g_DebugLog("  [%d] = '%s'", (int)i, fruits[i].c_str());
        }
        return -8;
    }
    g_DebugLog("String split passed: %d parts", (int)fruits.size());
    
    std::string rejoined = ModernString::Join(fruits, " | ");
    if (rejoined != "apple | banana | cherry | date") {
        g_DebugLog("ERROR: String join failed. Got: '%s'", rejoined.c_str());
        return -9;
    }
    g_DebugLog("String join passed: '%s'", rejoined.c_str());
    
    // Test 4: Type conversions
    g_DebugLog("=== Test 4: Type Conversions ===");
    
    int intVal = ModernString::ToInt("42");
    if (intVal != 42) {
        g_DebugLog("ERROR: String to int conversion failed. Got: %d", intVal);
        return -10;
    }
    g_DebugLog("String to int passed: %d", intVal);
    
    float floatVal = ModernString::ToFloat("3.14");
    if (floatVal < 3.13f || floatVal > 3.15f) {
        g_DebugLog("ERROR: String to float conversion failed. Got: %f", floatVal);
        return -11;
    }
    g_DebugLog("String to float passed: %f", floatVal);
    
    bool boolVal = ModernString::ToBool("true");
    if (!boolVal) {
        g_DebugLog("ERROR: String to bool conversion failed");
        return -12;
    }
    g_DebugLog("String to bool passed: %s", boolVal ? "true" : "false");
    
    std::string intStr = ModernString::ToString(123);
    if (intStr != "123") {
        g_DebugLog("ERROR: Int to string conversion failed. Got: '%s'", intStr.c_str());
        return -13;
    }
    g_DebugLog("Int to string passed: '%s'", intStr.c_str());
    
    // Test 5: String queries
    g_DebugLog("=== Test 5: String Queries ===");
    
    std::string queryStr = "Hello World";
    
    if (!ModernString::Contains(queryStr, "World")) {
        g_DebugLog("ERROR: String contains failed");
        return -14;
    }
    g_DebugLog("String contains passed");
    
    if (!ModernString::StartsWith(queryStr, "Hello")) {
        g_DebugLog("ERROR: String starts with failed");
        return -15;
    }
    g_DebugLog("String starts with passed");
    
    if (!ModernString::EndsWith(queryStr, "World")) {
        g_DebugLog("ERROR: String ends with failed");
        return -16;
    }
    g_DebugLog("String ends with passed");
    
    // Test 6: ModernStr class
    g_DebugLog("=== Test 6: ModernStr Class ===");
    
    ModernStr modernStr("  Hello ModernStr  ");
    modernStr.Trim().ToUpper().Replace("HELLO", "HI");
    
    if (modernStr.str() != "HI MODERNSTR") {
        g_DebugLog("ERROR: ModernStr chaining failed. Got: '%s'", modernStr.c_str());
        return -17;
    }
    g_DebugLog("ModernStr chaining passed: '%s'", modernStr.c_str());
    
    ModernStr numberStr("42");
    int number = numberStr.ToInt();
    if (number != 42) {
        g_DebugLog("ERROR: ModernStr ToInt failed. Got: %d", number);
        return -18;
    }
    g_DebugLog("ModernStr ToInt passed: %d", number);
    
    g_DebugLog("=== All basic string tests passed! ===");
    return 0;
}

// Test main function
#ifdef _STANDALONE
int main() {
    g_DebugLog("Starting basic string system test suite...");
    
    int result = TestBasicStringOperations();
    
    if (result == 0) {
        printf("SUCCESS: All basic string tests passed!\n");
    } else {
        printf("FAILED: Basic string test failed with code %d\n", result);
    }
    
    return result;
}
#endif
