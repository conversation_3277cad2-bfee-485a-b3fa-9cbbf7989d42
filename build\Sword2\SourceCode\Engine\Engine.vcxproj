﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{F5730F5E-5A8C-36D0-B965-53150D6418B9}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>Engine</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\Sword2Code\build\lib\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Engine.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">EngineD</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\Sword2Code\build\lib\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Engine.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Engine</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\Sword2Code\build\lib\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Engine.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Engine</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\Sword2Code\build\lib\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Engine.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Engine</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.lib</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/Sword2Code/DevEnv/dx9/inc"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;ENGINE_EXPORTS;_DEBUG;_CRT_SECURE_NO_WARNINGS;_WINSOCK_DEPRECATED_NO_WARNINGS;NOMINMAX;WIN32_LEAN_AND_MEAN;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;ENGINE_EXPORTS;_CRT_SECURE_NO_WARNINGS;_WINSOCK_DEPRECATED_NO_WARNINGS;NOMINMAX;WIN32_LEAN_AND_MEAN;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Src;D:\Sword2Code\DevEnv\dx9\inc;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Src;D:\Sword2Code\DevEnv\dx9\inc;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/Sword2Code/DevEnv/dx9/inc"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;ENGINE_EXPORTS;_CRT_SECURE_NO_WARNINGS;_WINSOCK_DEPRECATED_NO_WARNINGS;NOMINMAX;WIN32_LEAN_AND_MEAN;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;ENGINE_EXPORTS;_CRT_SECURE_NO_WARNINGS;_WINSOCK_DEPRECATED_NO_WARNINGS;NOMINMAX;WIN32_LEAN_AND_MEAN;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Src;D:\Sword2Code\DevEnv\dx9\inc;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Src;D:\Sword2Code\DevEnv\dx9\inc;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/Sword2Code/DevEnv/dx9/inc"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;ENGINE_EXPORTS;_CRT_SECURE_NO_WARNINGS;_WINSOCK_DEPRECATED_NO_WARNINGS;NOMINMAX;WIN32_LEAN_AND_MEAN;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;ENGINE_EXPORTS;_CRT_SECURE_NO_WARNINGS;_WINSOCK_DEPRECATED_NO_WARNINGS;NOMINMAX;WIN32_LEAN_AND_MEAN;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Src;D:\Sword2Code\DevEnv\dx9\inc;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Src;D:\Sword2Code\DevEnv\dx9\inc;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/Sword2Code/DevEnv/dx9/inc"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;ENGINE_EXPORTS;_CRT_SECURE_NO_WARNINGS;_WINSOCK_DEPRECATED_NO_WARNINGS;NOMINMAX;WIN32_LEAN_AND_MEAN;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;ENGINE_EXPORTS;_CRT_SECURE_NO_WARNINGS;_WINSOCK_DEPRECATED_NO_WARNINGS;NOMINMAX;WIN32_LEAN_AND_MEAN;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Src;D:\Sword2Code\DevEnv\dx9\inc;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\Sword2Code\Sword2\SourceCode\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Include;D:\Sword2Code\DevEnv\Include;D:\Sword2Code\Sword2\SourceCode\Engine\Src;D:\Sword2Code\DevEnv\dx9\inc;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\Sword2Code\Sword2\SourceCode\Engine\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/Sword2Code/Sword2/SourceCode/Engine/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/Sword2Code -BD:/Sword2Code/build --check-stamp-file D:/Sword2Code/build/Sword2/SourceCode/Engine/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\Sword2Code\build\Sword2\SourceCode\Engine\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/Sword2Code/Sword2/SourceCode/Engine/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/Sword2Code -BD:/Sword2Code/build --check-stamp-file D:/Sword2Code/build/Sword2/SourceCode/Engine/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\Sword2Code\build\Sword2\SourceCode\Engine\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule D:/Sword2Code/Sword2/SourceCode/Engine/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/Sword2Code -BD:/Sword2Code/build --check-stamp-file D:/Sword2Code/build/Sword2/SourceCode/Engine/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\Sword2Code\build\Sword2\SourceCode\Engine\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule D:/Sword2Code/Sword2/SourceCode/Engine/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/Sword2Code -BD:/Sword2Code/build --check-stamp-file D:/Sword2Code/build/Sword2/SourceCode/Engine/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\Sword2Code\build\Sword2\SourceCode\Engine\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KEngine.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KWin32.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KWin32App.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KWin32Wnd.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\stdafx.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KCanvas.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KDDraw.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KBitmap.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KBitmap16.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KBitmapConvert.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KSprite.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KSpriteCache.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KSpriteCodec.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KSpriteMaker.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KPalette.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KColors.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KDrawBase.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KDrawBitmap.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KDrawBitmap16.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KDrawSprite.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KDrawSpriteAlpha.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KDrawFade.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KDrawFont.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KDrawDotFont.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KDSound.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KMusic.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KWavSound.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KWavMusic.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KMp3Music.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KMp4Audio.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KMpgMusic.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KWavFile.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KWavCodec.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KDInput.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KKeyboard.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KMouse.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\Kime.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KFile.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KFilePath.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KFileDialog.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KFileCopy.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KPakFile.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KPakData.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KPakList.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KPakMake.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KPakTool.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KZipFile.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KZipData.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KZipList.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KZipCodec.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\XPackFile.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\ZPackFile.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\ZSPRPackFile.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KBmpFile.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KBmpFile24.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KJpgFile.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KGifFile.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KPcxFile.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KTgaFile32.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KMemBase.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KMemClass.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KMemClass1.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KMemManager.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KMemStack.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KList.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KNode.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KLinkArray.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KSafeList.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KSortList.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KHashList.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KHashNode.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KHashTable.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KBinTree.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KSortBinTree.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KFindBinTree.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KOctree.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KOctreeNode.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KStrBase.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KStrList.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KStrNode.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KSG_StringProcess.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KSG_MD5_String.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\md5.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KScript.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KScriptCache.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KScriptList.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KScriptSet.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KLuaScript.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KLuaScriptSet.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KStepLuaScript.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KEicScript.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KEicScriptSet.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\LuaFuns.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KNetClient.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KNetServer.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KNetServerNode.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KNetThread.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KTimer.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KRandom.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KDebug.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KEvent.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KMessage.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KMsgNode.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KIniFile.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KTabFile.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KTabFileCtrl.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KScanDir.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\Text.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KThread.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KMutex.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KAutoMutex.cpp" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KCriticalSection.h" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KGraphics.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KImageRes.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KImageStore.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KFont.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KBmp2Spr.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KCodec.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KCodecLha.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KCodecLzo.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KCache.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KSoundCache.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KVideo.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KAviFile.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KMp4Movie.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KMp4Video.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KPolygon.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KPolyRelation.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KDError.cpp" />
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KLubCmpl_Blocker.cpp" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KEngine.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KWin32.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KWin32App.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KWin32Wnd.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KCanvas.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KDDraw.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KBitmap.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KBitmap16.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KSprite.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KDrawBase.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KDrawSprite.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KDSound.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KMusic.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KWavSound.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KMp3Music.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KDInput.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KKeyboard.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KMouse.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KFile.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KFilePath.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KPakFile.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KMemBase.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KList.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KTimer.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KRandom.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KDebug.h" />
    <ClInclude Include="D:\Sword2Code\Sword2\SourceCode\Engine\Src\KIniFile.h" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\Sword2Code\build\ZERO_CHECK.vcxproj">
      <Project>{50640DD7-D2FA-3C60-B3F6-6DCFBCC8CB4F}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>