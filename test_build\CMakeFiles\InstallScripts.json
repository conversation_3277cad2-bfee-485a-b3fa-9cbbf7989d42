{"InstallScripts": ["D:/Sword2Code/test_build/cmake_install.cmake", "D:/Sword2Code/test_build/Sword2/SourceCode/Engine/cmake_install.cmake", "D:/Sword2Code/test_build/DevEnv/Lua5/cmake_install.cmake", "D:/Sword2Code/test_build/Sword2/SourceCode/Common/cmake_install.cmake", "D:/Sword2Code/test_build/Sword2/SourceCode/Common/Core/cmake_install.cmake", "D:/Sword2Code/test_build/Sword2/SourceCode/Common/Support/cmake_install.cmake", "D:/Sword2Code/test_build/Sword2/SourceCode/Common/GameSys/cmake_install.cmake", "D:/Sword2Code/test_build/Sword2/SourceCode/Common/Stat/cmake_install.cmake", "D:/Sword2Code/test_build/Sword2/SourceCode/Client/cmake_install.cmake", "D:/Sword2Code/test_build/Sword2/SourceCode/Client/Dawn/cmake_install.cmake", "D:/Sword2Code/test_build/Sword2/SourceCode/Client/DawnClient/cmake_install.cmake", "D:/Sword2Code/test_build/Sword2/SourceCode/Server/cmake_install.cmake", "D:/Sword2Code/test_build/Sword2/SourceCode/Server/GameServer/cmake_install.cmake", "D:/Sword2Code/test_build/Sword2/SourceCode/Server/Relay/cmake_install.cmake", "D:/Sword2Code/test_build/Sword2/SourceCode/Server/RelayT/cmake_install.cmake"], "Parallel": false}