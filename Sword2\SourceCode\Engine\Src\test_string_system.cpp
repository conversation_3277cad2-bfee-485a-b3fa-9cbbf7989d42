// String system modernization test - verify KStrBase_modern, KStrNode_modern, KStrList_modern
#include "KWin32.h"

// Forward declarations to avoid conflicts
void g_DebugLog(LPSTR Fmt, ...);

#include "KStrBase_modern.h"
#include "KStrNode_modern.h"
#include "KStrList_modern.h"
#include <stdio.h>
#include <stdarg.h>
#include <vector>
#include <string>

// Simple debug implementation for testing
void g_DebugLog(LPSTR Fmt, ...) {
    char buffer[1024];
    va_list va;
    
    va_start(va, Fmt);
    vsnprintf(buffer, sizeof(buffer), Fmt, va);
    va_end(va);
    
    printf("[DEBUG] %s\n", buffer);
}

// Test string system functionality
ENGINE_API int TestStringSystem() {
    g_DebugLog("Starting string system modernization test...");
    
    // Test 1: Basic string operations
    g_DebugLog("=== Test 1: Basic String Operations ===");
    
    // Test traditional C-style functions
    char buffer1[100] = "Hello";
    char buffer2[100] = " World";
    
    g_StrCat(buffer1, buffer2);
    if (!g_StrCmp(buffer1, "Hello World")) {
        g_DebugLog("ERROR: String concatenation failed");
        return -1;
    }
    
    g_StrUpper(buffer1);
    if (!g_StrCmp(buffer1, "HELLO WORLD")) {
        g_DebugLog("ERROR: String upper case failed");
        return -2;
    }
    
    g_DebugLog("Traditional string operations passed");
    
    // Test 2: Modern string operations
    g_DebugLog("=== Test 2: Modern String Operations ===");
    
    std::string testStr = "  Hello Modern World  ";
    std::string trimmed = ModernString::Trim(testStr);
    if (trimmed != "Hello Modern World") {
        g_DebugLog("ERROR: String trim failed. Got: '%s'", trimmed.c_str());
        return -3;
    }
    
    std::string upper = ModernString::ToUpper(trimmed);
    if (upper != "HELLO MODERN WORLD") {
        g_DebugLog("ERROR: Modern string upper failed. Got: '%s'", upper.c_str());
        return -4;
    }
    
    std::string replaced = ModernString::Replace(upper, "MODERN", "AWESOME");
    if (replaced != "HELLO AWESOME WORLD") {
        g_DebugLog("ERROR: String replace failed. Got: '%s'", replaced.c_str());
        return -5;
    }
    
    g_DebugLog("Modern string operations passed");
    
    // Test 3: String splitting and joining
    g_DebugLog("=== Test 3: String Splitting and Joining ===");
    
    std::string csvData = "apple,banana,cherry,date";
    std::vector<std::string> fruits = ModernString::Split(csvData, ',');
    
    if (fruits.size() != 4) {
        g_DebugLog("ERROR: String split failed. Expected 4 parts, got %d", (int)fruits.size());
        return -6;
    }
    
    if (fruits[0] != "apple" || fruits[1] != "banana" || fruits[2] != "cherry" || fruits[3] != "date") {
        g_DebugLog("ERROR: String split content failed");
        return -7;
    }
    
    std::string rejoined = ModernString::Join(fruits, " | ");
    if (rejoined != "apple | banana | cherry | date") {
        g_DebugLog("ERROR: String join failed. Got: '%s'", rejoined.c_str());
        return -8;
    }
    
    g_DebugLog("String splitting and joining passed");
    
    // Test 4: Type conversions
    g_DebugLog("=== Test 4: Type Conversions ===");
    
    int intVal = ModernString::ToInt("42");
    if (intVal != 42) {
        g_DebugLog("ERROR: String to int conversion failed. Got: %d", intVal);
        return -9;
    }
    
    float floatVal = ModernString::ToFloat("3.14");
    if (floatVal < 3.13f || floatVal > 3.15f) {
        g_DebugLog("ERROR: String to float conversion failed. Got: %f", floatVal);
        return -10;
    }
    
    bool boolVal = ModernString::ToBool("true");
    if (!boolVal) {
        g_DebugLog("ERROR: String to bool conversion failed");
        return -11;
    }
    
    std::string intStr = ModernString::ToString(123);
    if (intStr != "123") {
        g_DebugLog("ERROR: Int to string conversion failed. Got: '%s'", intStr.c_str());
        return -12;
    }
    
    g_DebugLog("Type conversions passed");
    
    // Test 5: KStrNode operations
    g_DebugLog("=== Test 5: KStrNode Operations ===");
    
    KStrNode node1("TestNode");
    KStrNode node2;
    node2.SetName("AnotherNode");
    
    if (!node1.Equals("TestNode")) {
        g_DebugLog("ERROR: KStrNode equals failed");
        return -13;
    }
    
    if (node1 == node2) {
        g_DebugLog("ERROR: KStrNode comparison failed");
        return -14;
    }
    
    node1.ToUpper();
    if (!node1.Equals("TESTNODE")) {
        g_DebugLog("ERROR: KStrNode ToUpper failed. Got: '%s'", node1.GetName());
        return -15;
    }
    
    if (!node1.StartsWith("TEST")) {
        g_DebugLog("ERROR: KStrNode StartsWith failed");
        return -16;
    }
    
    g_DebugLog("KStrNode operations passed");
    
    // Test 6: KStrList operations
    g_DebugLog("=== Test 6: KStrList Operations ===");
    
    KStrList strList;
    strList.AddString("apple");
    strList.AddString("banana");
    strList.AddString("cherry");
    strList.AddString("date");
    
    if (strList.GetStringCount() != 4) {
        g_DebugLog("ERROR: KStrList count failed. Expected 4, got %d", (int)strList.GetStringCount());
        return -17;
    }
    
    KStrNode* found = strList.Find("banana");
    if (!found || !found->Equals("banana")) {
        g_DebugLog("ERROR: KStrList find failed");
        return -18;
    }
    
    if (!strList.Contains("cherry")) {
        g_DebugLog("ERROR: KStrList contains failed");
        return -19;
    }
    
    // Test filtering
    auto filtered = strList.FilterByPrefix("a");
    if (filtered.size() != 1 || !filtered[0]->Equals("apple")) {
        g_DebugLog("ERROR: KStrList filter by prefix failed");
        return -20;
    }
    
    // Test sorting
    strList.SortByName(true);
    std::vector<std::string> sorted = strList.GetAllStrings();
    if (sorted[0] != "apple" || sorted[1] != "banana" || sorted[2] != "cherry" || sorted[3] != "date") {
        g_DebugLog("ERROR: KStrList sorting failed");
        return -21;
    }
    
    g_DebugLog("KStrList operations passed");
    
    // Test 7: ModernStr class
    g_DebugLog("=== Test 7: ModernStr Class ===");
    
    ModernStr modernStr("  Hello ModernStr  ");
    modernStr.Trim().ToUpper().Replace("HELLO", "HI");
    
    if (modernStr.str() != "HI MODERNSTR") {
        g_DebugLog("ERROR: ModernStr chaining failed. Got: '%s'", modernStr.c_str());
        return -22;
    }
    
    int modernInt = modernStr.ToInt(0);  // Should return default value
    if (modernInt != 0) {
        g_DebugLog("ERROR: ModernStr ToInt default failed");
        return -23;
    }
    
    ModernStr numberStr("42");
    int number = numberStr.ToInt();
    if (number != 42) {
        g_DebugLog("ERROR: ModernStr ToInt failed. Got: %d", number);
        return -24;
    }
    
    g_DebugLog("ModernStr class passed");
    
    g_DebugLog("=== All string system tests passed! ===");
    return 0;
}

// Test main function
#ifdef _STANDALONE
int main() {
    g_DebugLog("Starting string system modernization test suite...");
    
    int result = TestStringSystem();
    
    if (result == 0) {
        printf("SUCCESS: All string system tests passed!\n");
    } else {
        printf("FAILED: String system test failed with code %d\n", result);
    }
    
    return result;
}
#endif
