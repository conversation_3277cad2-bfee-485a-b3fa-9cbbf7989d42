﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KCore.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KMath.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\stdafx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KCharacterSet.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KObserver.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KPlayer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KPlayerEvent.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KPlayerEventMan.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KPlayerInteractive.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KPlayerPK.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KPlayerSyncCheck.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KPlayerTeam.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KPlayerTrade.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KStall.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KTeam.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KPlayerTitle.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KPlayerSkillState.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AI\KActiveAttack2AI.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AI\KActiveAttackAI.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AI\KAIBase.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AI\KAIManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AI\KClientAI.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AI\KDialogAI.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AI\KEscapeAI.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AI\KFollowerPassivitAI.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AI\KGroupAttackAI.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AI\KHostilityAI.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AI\KPassivityAttackAI.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AI\KPickCorpseAI.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AI\KNpcAIControl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AI\KDeludeAI.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AI\KCorpseRevivalAI.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AI\KAreaActiveAttackAI.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AI\KAIClientAutoFightEmei.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AI\KClientNpcAI.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AI\KPetAttackAI.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KFaction.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KPlayerFaction.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KNpc.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KNpcCommand.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KNpcExp.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KNpcFindPath.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KNpcScriptControl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KNpcSoundControl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KNpcTemplate.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KCharacter.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KNpcAttribute.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KNpcEventsBase.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KNpcEventAttribute.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KNpcGroup.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Character\CharacterConfig.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Character\AutoMove\KAutoMove.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Character\Paint\KCharacterResNode.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Character\Paint\KNpcResNode.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Character\Paint\KResListSvr.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Character\KPlayerAddendaAttribute.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Character\KPlayerSpecialAttribute.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Character\ColdDown.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Character\KPlayerRelation.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Character\KPlayerDining.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Character\KMultiRolesMgr.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Character\KPlayerReborn.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Character\KRebornData.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Character\KRebornManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KTitle.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KTitleManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KObjectPosition.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KExtendProtocolProcess.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Net\KNetCommand.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KNewProtocolProcess.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KProtocol.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KProtocolProcess.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\CoreServerShell.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KChannelProtocol.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KLifeSkill.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KLifeSkillList.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KMissle.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KMissleManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KMissleSet.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KSkill.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KSkillList.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KSkillManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KComposeSkill.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KGatherSkill.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KLifeSkillRes.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KRegion.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KSubWorld.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KSubWorldSet.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KWorldMsg.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KSortScript.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\ScriptFuns.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\LibScriptFuns.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\LuaFuns.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Script\ItemScript.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Script\TaskScript.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Script\NpcScript.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Script\TongScript.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KBuySell.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KChannelTrade.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KMessage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KSpecialAttribute.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KUiInteractive.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KInventory.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KItem.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Item\KItemAttribute.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KItemList.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KItemSet.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KItemTemplate.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KMagicDesc.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Item\KMysteryBook.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KViewItem.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KItemEightDiagram.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KItemEnchase.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KItemEpurate.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KItemEnhance.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Item\KAddendaAttribute.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Item\KBookDesc.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Item\KSuitEquip.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Item\KGuAttribute.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Item\LingShiAttr.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Item\LingShiCreator.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Item\LingShiComposeData.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Item\LingShiCompose.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Item\ItemValue.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Item\MakeEquipSocket.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Item\MakeEquipLingQi.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Item\ClientItemParser.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Item\KEquipComposeMagic.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KObj.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KObjectSet.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KObjectTemplate.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KMission.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KPlayerTask.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KTaskFuns.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KTaskNpcGroup.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KNormalTask.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KTaskNoteManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KCommerceTask.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KFactionTask.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KGMCommand.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Trigger\KTrigger.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Trigger\KTriggerManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Trigger\KTriggerSet.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Trigger\KTriggerTemplate.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KGameSpaceSoundSetting.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KMapMusic.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KPiPClientCtrl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KPiPCore.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\CompetitionListMgr.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Compress\LZO\minilzo.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KChatChannels.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KChatChannel.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KChannelAlias.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KChatCtrl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KGlobalValue.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KRecipe.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KComposeManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KWeatherMgr.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\LogCentre.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KSG_ExtPoint.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KBlueBoss.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KSuperBoss.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KOfflineLiveAction.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KCorpseRevival.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Camp\CampFile.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Camp\Camp.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Camp\CampManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Camp\CampRelation.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Camp\KPlayerCampInfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KClientPing.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\IB\KItemBillingObject.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\IB\KBillingPage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\IB\KBillingItem.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\IB\KBillingNumberable.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\IB\KItemBillingManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KCustomData.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KCustomScriptData.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KRelayShareData.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\MissionEx\MissionExData.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\MissionEx\MissionExDataManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\AffairData.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Npc\KMonsterHandbook.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KEventObserver.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KNpcEvent2Magic.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\PetSystem\KPetAttribute.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\PetSystem\KPetFeed.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\PetSystem\KPetLevelUp.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\PetSystem\KPetManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\PetSystem\KPetSet.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\PetSystem\KPetSummon.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Common\kobjectpool.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KPlayerSelectState.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Money.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\SaveLoad\KItemSaveLoad.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\SaveLoad\KFamilySaveLoad.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\SaveLoad\KRoleSaveLoad.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\SaveLoad\KPlayerSaveLoadMgr.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KPlayerMomentum.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KGestConvention.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KGestConventionRank.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\Item\KRebornSmelt.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KPossessionItem.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KPossessionMoney.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\KPossessionTrace.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\WeaponEffect\WeaponEffect.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\WeaponEffect\WeaponEffectManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\Src\WeaponEffect\WeaponEffectSet.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\SaveLoad\SaveLoadAux.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongCenter.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongDuty.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongMember.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongProcessor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongSync.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongTeam.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongDefine.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongTechControl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongTechRes.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\TongCenter\TongMagicAttribute.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KPlayerTreasure.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KMailAttachmentImp.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KMailImp.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KPostOffice_Entry.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\PlayerTreasure\KPostOfficeImp.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager\KAreaCampMgr.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager\KAreaCamp.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager\KCampMsgProcessor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\Sword2Code\Sword2\SourceCode\Common\Support\CampManager\KAreaCampTongSort.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\Sword2Code\Sword2\SourceCode\Common\Core\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{44AB8148-4A3D-32F7-9B57-640603BD4364}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
