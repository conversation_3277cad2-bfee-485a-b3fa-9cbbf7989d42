//---------------------------------------------------------------------------
// Sword3 Engine - Modernized File Path System Implementation
// 现代化的文件路径系统实现
//---------------------------------------------------------------------------
#include "KFilePath_modern.h"
#include "KDebug.h"
#include <filesystem>
#include <string>
#include <algorithm>

//---------------------------------------------------------------------------
// Global variables for path management
//---------------------------------------------------------------------------
static std::filesystem::path g_RootPath;
static std::filesystem::path g_CurrentFilePath;

//---------------------------------------------------------------------------
// Set root path
//---------------------------------------------------------------------------
void g_SetRootPath(LPSTR lpPathName) {
    try {
        if (lpPathName && strlen(lpPathName) > 0) {
            g_RootPath = std::filesystem::path(lpPathName);
        } else {
            // Use current working directory if no path specified
            g_RootPath = std::filesystem::current_path();
        }
        
        // Ensure the path exists
        if (!std::filesystem::exists(g_RootPath)) {
            std::filesystem::create_directories(g_RootPath);
        }
        
        g_DebugLog("Root path set to: %s", g_RootPath.string().c_str());
        
    } catch (const std::exception& e) {
        g_DebugLog("Exception setting root path: %s", e.what());
        g_RootPath = std::filesystem::current_path();
    }
}

//---------------------------------------------------------------------------
// Get root path
//---------------------------------------------------------------------------
void g_GetRootPath(LPSTR lpPathName) {
    if (!lpPathName) return;
    
    try {
        std::string rootStr = g_RootPath.string();
        strncpy(lpPathName, rootStr.c_str(), MAXPATH - 1);
        lpPathName[MAXPATH - 1] = '\0';
        
    } catch (const std::exception& e) {
        g_DebugLog("Exception getting root path: %s", e.what());
        lpPathName[0] = '\0';
    }
}

//---------------------------------------------------------------------------
// Set current file path
//---------------------------------------------------------------------------
void g_SetFilePath(LPSTR lpPathName) {
    try {
        if (lpPathName && strlen(lpPathName) > 0) {
            g_CurrentFilePath = std::filesystem::path(lpPathName);
        } else {
            g_CurrentFilePath = g_RootPath;
        }
        
        g_DebugLog("File path set to: %s", g_CurrentFilePath.string().c_str());
        
    } catch (const std::exception& e) {
        g_DebugLog("Exception setting file path: %s", e.what());
        g_CurrentFilePath = g_RootPath;
    }
}

//---------------------------------------------------------------------------
// Get current file path
//---------------------------------------------------------------------------
void g_GetFilePath(LPSTR lpPathName) {
    if (!lpPathName) return;
    
    try {
        std::string pathStr = g_CurrentFilePath.string();
        strncpy(lpPathName, pathStr.c_str(), MAXPATH - 1);
        lpPathName[MAXPATH - 1] = '\0';
        
    } catch (const std::exception& e) {
        g_DebugLog("Exception getting file path: %s", e.what());
        lpPathName[0] = '\0';
    }
}

//---------------------------------------------------------------------------
// Get full path by combining current path with filename
//---------------------------------------------------------------------------
void g_GetFullPath(LPSTR lpPathName, LPSTR lpFileName) {
    if (!lpPathName || !lpFileName) return;
    
    try {
        std::filesystem::path fileName(lpFileName);
        std::filesystem::path fullPath;
        
        // If filename is already absolute, use it as-is
        if (fileName.is_absolute()) {
            fullPath = fileName;
        } else {
            // Combine with current file path
            fullPath = g_CurrentFilePath / fileName;
        }
        
        // Normalize the path
        fullPath = std::filesystem::weakly_canonical(fullPath);
        
        std::string fullStr = fullPath.string();
        strncpy(lpPathName, fullStr.c_str(), MAXPATH - 1);
        lpPathName[MAXPATH - 1] = '\0';
        
    } catch (const std::exception& e) {
        g_DebugLog("Exception getting full path for %s: %s", lpFileName, e.what());
        strncpy(lpPathName, lpFileName, MAXPATH - 1);
        lpPathName[MAXPATH - 1] = '\0';
    }
}

//---------------------------------------------------------------------------
// Get relative path from root
//---------------------------------------------------------------------------
void g_GetHalfPath(LPSTR lpPathName, LPSTR lpFileName) {
    if (!lpPathName || !lpFileName) return;
    
    try {
        std::filesystem::path fileName(lpFileName);
        std::filesystem::path relativePath;
        
        if (fileName.is_absolute()) {
            // Make relative to root path
            relativePath = std::filesystem::relative(fileName, g_RootPath);
        } else {
            relativePath = fileName;
        }
        
        std::string relStr = relativePath.string();
        strncpy(lpPathName, relStr.c_str(), MAXPATH - 1);
        lpPathName[MAXPATH - 1] = '\0';
        
    } catch (const std::exception& e) {
        g_DebugLog("Exception getting half path for %s: %s", lpFileName, e.what());
        strncpy(lpPathName, lpFileName, MAXPATH - 1);
        lpPathName[MAXPATH - 1] = '\0';
    }
}

//---------------------------------------------------------------------------
// Create directory path
//---------------------------------------------------------------------------
void g_CreatePath(LPSTR lpPathName) {
    if (!lpPathName) return;
    
    try {
        std::filesystem::path dirPath(lpPathName);
        
        if (!std::filesystem::exists(dirPath)) {
            std::filesystem::create_directories(dirPath);
            g_DebugLog("Created directory: %s", lpPathName);
        }
        
    } catch (const std::exception& e) {
        g_DebugLog("Exception creating path %s: %s", lpPathName, e.what());
    }
}

//---------------------------------------------------------------------------
// Check if file exists
//---------------------------------------------------------------------------
BOOL g_FileExists(LPSTR lpPathName) {
    if (!lpPathName) return FALSE;
    
    try {
        std::filesystem::path filePath(lpPathName);
        return std::filesystem::exists(filePath) ? TRUE : FALSE;
        
    } catch (const std::exception& e) {
        g_DebugLog("Exception checking file existence %s: %s", lpPathName, e.what());
        return FALSE;
    }
}

//---------------------------------------------------------------------------
// Convert filename to ID (simple hash)
//---------------------------------------------------------------------------
DWORD g_FileName2Id(LPSTR lpFileName) {
    if (!lpFileName) return 0;
    
    // Simple hash algorithm for filename
    DWORD hash = 0;
    char* p = lpFileName;
    
    while (*p) {
        hash = hash * 31 + static_cast<DWORD>(tolower(*p));
        p++;
    }
    
    return hash;
}

//---------------------------------------------------------------------------
// Extract filename from full path
//---------------------------------------------------------------------------
void g_ExtractFileName(LPSTR lpFileName, LPSTR lpFilePath) {
    if (!lpFileName || !lpFilePath) return;
    
    try {
        std::filesystem::path filePath(lpFilePath);
        std::string fileName = filePath.filename().string();
        
        strncpy(lpFileName, fileName.c_str(), MAXFILE - 1);
        lpFileName[MAXFILE - 1] = '\0';
        
    } catch (const std::exception& e) {
        g_DebugLog("Exception extracting filename from %s: %s", lpFilePath, e.what());
        lpFileName[0] = '\0';
    }
}

//---------------------------------------------------------------------------
// Extract directory path from full path
//---------------------------------------------------------------------------
void g_ExtractFilePath(LPSTR lpPathName, LPSTR lpFilePath) {
    if (!lpPathName || !lpFilePath) return;
    
    try {
        std::filesystem::path filePath(lpFilePath);
        std::string dirPath = filePath.parent_path().string();
        
        strncpy(lpPathName, dirPath.c_str(), MAXPATH - 1);
        lpPathName[MAXPATH - 1] = '\0';
        
    } catch (const std::exception& e) {
        g_DebugLog("Exception extracting path from %s: %s", lpFilePath, e.what());
        lpPathName[0] = '\0';
    }
}

//---------------------------------------------------------------------------
// Unite path and filename
//---------------------------------------------------------------------------
void g_UnitePathAndName(char *lpPath, char *lpFile, char *lpGet) {
    if (!lpPath || !lpFile || !lpGet) return;

    try {
        std::filesystem::path pathPart(lpPath);
        std::filesystem::path filePart(lpFile);
        std::filesystem::path combined = pathPart / filePart;

        std::string combinedStr = combined.string();
        strncpy(lpGet, combinedStr.c_str(), MAXPATH - 1);
        lpGet[MAXPATH - 1] = '\0';

    } catch (const std::exception& e) {
        g_DebugLog("Exception uniting path %s and file %s: %s", lpPath, lpFile, e.what());
        lpGet[0] = '\0';
    }
}

//---------------------------------------------------------------------------
// Change file extension
//---------------------------------------------------------------------------
void g_ChangeFileExt(LPSTR lpFileName, LPSTR lpNewExt) {
    if (!lpFileName || !lpNewExt) return;

    try {
        std::filesystem::path filePath(lpFileName);
        filePath.replace_extension(lpNewExt);

        std::string newFileName = filePath.string();
        strncpy(lpFileName, newFileName.c_str(), MAXPATH - 1);
        lpFileName[MAXPATH - 1] = '\0';

    } catch (const std::exception& e) {
        g_DebugLog("Exception changing file extension: %s", e.what());
    }
}

//---------------------------------------------------------------------------
// Placeholder implementations for pack/disk path functions
// These will be implemented when we modernize the pack file system
//---------------------------------------------------------------------------
void g_GetPackPath(LPSTR lpPathName, LPSTR lpFileName) {
    // For now, just use the regular full path
    g_GetFullPath(lpPathName, lpFileName);
}

void g_GetDiskPath(LPSTR lpPathName, LPSTR lpFileName) {
    // For now, just use the regular full path
    g_GetFullPath(lpPathName, lpFileName);
}

//---------------------------------------------------------------------------
// Modern C++ namespace implementations (optional advanced interface)
//---------------------------------------------------------------------------
namespace ModernPath {

    std::filesystem::path GetRootPath() {
        return g_RootPath;
    }

    void SetRootPath(const std::filesystem::path& path) {
        g_RootPath = path;
        if (!std::filesystem::exists(g_RootPath)) {
            std::filesystem::create_directories(g_RootPath);
        }
    }

    std::filesystem::path GetFullPath(const std::string& fileName) {
        std::filesystem::path filePath(fileName);
        if (filePath.is_absolute()) {
            return filePath;
        }
        return g_CurrentFilePath / filePath;
    }

    bool FileExists(const std::filesystem::path& path) {
        return std::filesystem::exists(path);
    }

    void CreateDirectories(const std::filesystem::path& path) {
        std::filesystem::create_directories(path);
    }

    std::string GetFileName(const std::filesystem::path& path) {
        return path.filename().string();
    }

    std::string GetFileExtension(const std::filesystem::path& path) {
        return path.extension().string();
    }

    std::filesystem::path GetParentPath(const std::filesystem::path& path) {
        return path.parent_path();
    }
}
